import { SinonStub, stub, restore } from "sinon";
import { expect, should, use } from "chai";
import { factory } from "factory-girl";
import { complexStructure, createComplexStructure, truncate } from "../entities/helper";
import { FACTORY } from "../factories/common";
import { DomainMonitoringJob } from "../../skywind/jobs/domainMonitoringJob";
import { MonitoredDomain, DomainStatus } from "../../skywind/services/domainDetector/types";
import * as EntityService from "../../skywind/services/entity";
import { EntityStatus } from "../../skywind/entities/entity";
import * as httpClientModule from "../../skywind/utils/httpClient";
import { getStaticDomainPoolService } from "../../skywind/services/staticDomainPool";
import { Models } from "../../skywind/models/models";
import config from "../../skywind/config";

should();
use(require("chai-as-promised"));

class MockHttpClient {
    private domains = new Map<string, MonitoredDomain>();

    async get<T>(url: string): Promise<T> {
        if (url === "") {
            // List all domains
            return Array.from(this.domains.values()) as T;
        } else {
            // Get specific domain
            const domain = decodeURIComponent(url.replace("/", ""));
            const monitoredDomain = this.domains.get(domain);
            if (!monitoredDomain) {
                throw new Error(`Domain ${domain} not found`);
            }
            return monitoredDomain as T;
        }
    }

    async post<T>(_url: string, data: { domain: string }): Promise<T> {
        const monitoredDomain: MonitoredDomain = {
            domain: data.domain,
            status: { accessStatus: "UNKNOWN" }
        };
        this.domains.set(data.domain, monitoredDomain);
        return monitoredDomain as T;
    }

    async delete(url: string): Promise<void> {
        const domain = decodeURIComponent(url.replace("/", ""));
        this.domains.delete(domain);
    }

    // Helper methods for testing
    setDomainStatus(domain: string, status: DomainStatus) {
        const monitoredDomain = this.domains.get(domain);
        if (monitoredDomain) {
            monitoredDomain.status = status;
        }
    }

    getDomains(): string[] {
        return Array.from(this.domains.keys());
    }

    clear() {
        this.domains.clear();
    }
}

describe("Domain Monitoring Job", () => {
    let createHttpClientStub: SinonStub;
    let httpClient: MockHttpClient;
    let masterEntity: any;
    let loggingOutput: any;

    before(async () => {
        loggingOutput = config.loggingOutput;
        config.loggingOutput = "console";
        await truncate();
        await createComplexStructure();

        masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });
    });

    beforeEach(() => {
        createHttpClientStub = stub(httpClientModule, "createHttpClient");
        httpClient = new MockHttpClient();
        createHttpClientStub.returns(httpClient);
    });

    afterEach(() => {
        restore();
        httpClient.clear();
    });

    after(() => {
        config.loggingOutput = loggingOutput;
    });

    it("should process domains from static domain pools", async () => {
        const staticDomain1 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static1.example.com"
        });
        const staticDomain2 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static2.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-static-pool",
            domainDetectorAdapterId: "tapking"
        });

        // Add domains to the pool using the service
        const staticDomainPoolService = getStaticDomainPoolService();
        await staticDomainPoolService.addDomain(staticDomainPool.id, staticDomain1.id);
        await staticDomainPoolService.addDomain(staticDomainPool.id, staticDomain2.id);

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("static1.example.com");
        expect(httpClient.getDomains()).to.include("static2.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should process domains from dynamic domain pools", async () => {
        const dynamicDomain1 = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic1.example.com",
            environment: "test"
        });
        const dynamicDomain2 = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic2.example.com",
            environment: "test"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-dynamic-pool",
            domainDetectorAdapterId: "tapking"
        });

        // Add domains to the pool using the model
        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain1.id,
            isActive: true
        });
        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain2.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            dynamicDomainPoolId: dynamicDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("dynamic1.example.com");
        expect(httpClient.getDomains()).to.include("dynamic2.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should handle blocked domains and call setBlocked on sources", async () => {
        const blockedDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "blocked.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-pool",
            domainDetectorAdapterId: "tapking"
        });

        // Add domain to the pool using the service
        const staticDomainPoolService = getStaticDomainPoolService();
        await staticDomainPoolService.addDomain(staticDomainPool.id, blockedDomain.id);

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        httpClient.setDomainStatus("blocked.example.com", {
            accessStatus: "BLOCKED",
            lastCheckedAt: new Date().toISOString()
        });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.not.include("blocked.example.com");
    });

    it("should handle multiple domain pools with same adapter", async () => {
        const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static.example.com"
        });
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "dynamic.example.com",
            environment: "test"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "static-pool",
            domainDetectorAdapterId: "tapking"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "dynamic-pool",
            domainDetectorAdapterId: "tapking"
        });

        // Add domains to the pools using the services and models
        const staticDomainPoolService = getStaticDomainPoolService();
        await staticDomainPoolService.addDomain(staticDomainPool.id, staticDomain.id);
        await Models.DynamicDomainPoolItemModel.create({
            dynamicDomainPoolId: dynamicDomainPool.id,
            dynamicDomainId: dynamicDomain.id,
            isActive: true
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            dynamicDomainPoolId: dynamicDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("static.example.com");
        expect(httpClient.getDomains()).to.include("dynamic.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should handle empty domain list", async () => {
        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.have.length(0);
    });

    it("should handle domains with different statuses correctly", async () => {
        const availableDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "available.example.com"
        });
        const unknownDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "unknown.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-status-pool",
            domainDetectorAdapterId: "tapking"
        });

        // Add domains to the pool using the service
        const staticDomainPoolService = getStaticDomainPoolService();
        await staticDomainPoolService.addDomain(staticDomainPool.id, availableDomain.id);
        await staticDomainPoolService.addDomain(staticDomainPool.id, unknownDomain.id);

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        httpClient.setDomainStatus("available.example.com", {
            accessStatus: "AVAILABLE",
            lastCheckedAt: new Date().toISOString()
        });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("available.example.com");
        expect(httpClient.getDomains()).to.include("unknown.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should reuse detector instances for the same adapter", async () => {
        const domain1 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "domain1.example.com"
        });
        const domain2 = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "domain2.example.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-reuse-pool",
            domainDetectorAdapterId: "tapking"
        });

        // Add domains to the pool using the service
        const staticDomainPoolService = getStaticDomainPoolService();
        await staticDomainPoolService.addDomain(staticDomainPool.id, domain1.id);
        await staticDomainPoolService.addDomain(staticDomainPool.id, domain2.id);

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(createHttpClientStub.called).to.be.true;

        expect(httpClient.getDomains()).to.include("domain1.example.com");
        expect(httpClient.getDomains()).to.include("domain2.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });
});
