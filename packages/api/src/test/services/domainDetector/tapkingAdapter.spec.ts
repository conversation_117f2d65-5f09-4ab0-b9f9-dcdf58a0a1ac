import { expect } from "chai";
import * as sinon from "sinon";
import { Tap<PERSON>Adapter, DomainValidatorError } from "../../../skywind/services/domainDetector/tapkingAdapter";
import type { HttpClient } from "../../../skywind/utils/httpClient";
import * as httpClient from "../../../skywind/utils/httpClient";

describe("TapkingAdapter", () => {
    let client: TapkingAdapter;
    let httpClientStub: sinon.SinonStubbedInstance<HttpClient>;

    beforeEach(() => {
        sinon.stub(httpClient, "createHttpClient").returns({
            get: sinon.stub(),
            post: sinon.stub(),
            delete: sinon.stub(),
            put: sinon.stub(),
            patch: sinon.stub()
        } as any);

        client = new TapkingAdapter({
            baseUrl: "https://api.example.com",
            token: "test-token",
            timeout: 5000,
            retryConfig: {
                sleep: 100,
                maxTimeout: 1000
            }
        });
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("registerDomain", () => {
        it("should register a domain successfully", async () => {
            const mockResponse = {
                clientId: "test-client",
                name: "example.com",
                createdAt: 1751683700000,
                checkEndpoint: {
                    uri: null,
                    schema: "HTTPS",
                    headers: {},
                    method: "HEAD",
                    body: null,
                    validityRule: {
                        code: { from: 200, to: 299 },
                        body: null
                    }
                },
                statuses: [{
                    region: { countryCode: "TR" },
                    accessStatus: "UNKNOWN",
                    lastCheckedAt: null,
                    checkId: null
                }]
            };
            httpClientStub.post.resolves(mockResponse);

            const domain = "example.com";

            const result = await client.register(domain);
            expect(result).to.deep.equal(mockResponse);
            expect(httpClientStub.post).to.have.been.calledWith("/api/v1/domains", domain);
        });

        it("should handle registration errors", async () => {
            const errorResponse = { message: "Domain already exists" };
            const error = new httpClient.HttpClientError("Domain already exists", 409, errorResponse);
            httpClientStub.post.rejects(error);

            try {
                await client.register("example.com");
                expect.fail("Should have thrown an error");
            } catch (error) {
                expect(error).to.be.instanceOf(DomainValidatorError);
                expect(error.statusCode).to.equal(409);
            }
        });
    });

    describe("listDomains", () => {
        it("should list domains successfully", async () => {
            const mockResponse = [
                {
                    client: "test-client",
                    name: "example.com",
                    createdAt: "1754505038973",
                    checkEndpoint: {
                        uri: null,
                        schema: "HTTPS",
                        headers: {},
                        method: "HEAD",
                        body: null,
                        validityRule: {
                            code: { from: 200, to: 299 },
                            body: null
                        }
                    },
                    statuses: [{
                        region: { countryCode: "TR" },
                        accessStatus: "AVAILABLE",
                        lastCheckedAt: "1754505324058",
                        checkId: "019880aa-9ed5-7960-bb7b-bbcb9a29dda1"
                    }]
                }
            ];
            httpClientStub.get.resolves(mockResponse);

            const result = await client.list();
            expect(result).to.deep.equal(mockResponse);
            expect(httpClientStub.get).to.have.been.calledWith("/api/v1/domains");
        });
    });

    describe("getDomain", () => {
        it("should get domain status successfully", async () => {
            const mockResponse = {
                clientId: "test-client",
                name: "example.com",
                createdAt: 1751683700000,
                checkEndpoint: {
                    uri: null,
                    schema: "HTTPS",
                    headers: {},
                    method: "HEAD",
                    body: null,
                    validityRule: {
                        code: { from: 200, to: 299 },
                        body: null
                    }
                },
                statuses: [{
                    region: { countryCode: "TR" },
                    accessStatus: "AVAILABLE",
                    lastCheckedAt: 1751683512000,
                    checkId: "019880a8-a0c2-7ce7-abc5-3782e0c9c198"
                }]
            };
            httpClientStub.get.resolves(mockResponse);

            const result = await client.get("example.com");
            expect(result).to.deep.equal(mockResponse);
            expect(httpClientStub.get).to.have.been.calledWith("/api/v1/domains/example.com");
        });

        it("should handle domain not found", async () => {
            const error = new httpClient.HttpClientError("Domain not found", 404, { message: "Domain not found" });
            httpClientStub.get.rejects(error);

            try {
                await client.get("nonexistent.com");
                expect.fail("Should have thrown an error");
            } catch (error) {
                expect(error).to.be.instanceOf(DomainValidatorError);
                expect(error.statusCode).to.equal(404);
            }
        });
    });

    describe("removeDomain", () => {
        it("should remove domain successfully", async () => {
            httpClientStub.delete.resolves();

            await client.remove("example.com");
            expect(httpClientStub.delete).to.have.been.calledWith("/api/v1/domains/example.com");
        });
    });

    describe("error handling", () => {
        it("should handle network errors", async () => {
            const networkError = new httpClient.HttpClientError("Network error");
            httpClientStub.get.rejects(networkError);

            try {
                await client.list();
                expect.fail("Should have thrown an error");
            } catch (error) {
                expect(error).to.be.instanceOf(DomainValidatorError);
                expect(error.message).to.include("Network error");
            }
        });

        it("should handle timeout errors", async () => {
            const timeoutError = new httpClient.HttpClientError("Request timeout");
            httpClientStub.get.rejects(timeoutError);

            try {
                await client.list();
                expect.fail("Should have thrown an error");
            } catch (error) {
                expect(error).to.be.instanceOf(DomainValidatorError);
                expect(error.message).to.include("Request timeout");
            }
        });
    });
});
