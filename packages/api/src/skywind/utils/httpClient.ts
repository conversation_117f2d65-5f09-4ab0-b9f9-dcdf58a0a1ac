import * as superagent from "superagent";
import { retry, keepalive, lazy, RetryConfig, Lazy } from "@skywind-group/sw-utils";
import logger from "./logger";

const log = logger("http-client");

type ShouldRetryFn = (err: Error) => boolean;
type WrapErrorFn = (err: Error) => Error;

export interface HttpClientConfig {
    baseUrl?: string;
    headers?: Record<string, string>;
    timeout?: number;
    retryConfig?: RetryConfig;
    keepAlive?: keepalive.KeepAliveConfig;
    shouldRetry?: ShouldRetryFn;
    wrapError?: WrapErrorFn;
}

export interface HttpClientParams {
    headers?: Record<string, string>;
    shouldRetry?: ShouldRetryFn;
    wrapError?: WrapErrorFn;
}

export class HttpClientError extends Error {
    public readonly statusCode?: number;
    public readonly responseBody?: any;

    constructor(message: string, statusCode?: number, responseBody?: any) {
        super(message);
        this.name = "HttpClientError";
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }
}

function shouldRetry(error: Error): boolean {
    if (error instanceof HttpClientError) {
        // Don't retry on client errors (4xx) except for 429 (rate limit)
        if (error.statusCode && error.statusCode >= 400 && error.statusCode < 500 && error.statusCode !== 429) {
            return false;
        }
    }
    return true;
}

export class HttpClient {
    private readonly baseUrl: string;
    private readonly headers: Record<string, string>;
    private readonly timeout: number;
    private readonly retryConfig: RetryConfig;
    private readonly shouldRetry?: ShouldRetryFn;
    private readonly wrapError?: WrapErrorFn;
    private readonly httpAgent: Lazy<keepalive.HttpAgent>;
    private readonly httpsAgent: Lazy<keepalive.HttpsAgent>;

    constructor(config: HttpClientConfig) {
        this.baseUrl = config.baseUrl?.replace(/\/$/, "") || "";
        this.headers = config.headers || {};
        this.timeout = config.timeout || 30000;
        this.retryConfig = config.retryConfig || { sleep: 1000, maxTimeout: 10000 };
        this.shouldRetry = config.shouldRetry;
        this.wrapError = config.wrapError;

        // Create keepalive agents if configuration is provided
        if (config.keepAlive) {
            this.httpAgent = lazy(() => keepalive.createAgent(config.keepAlive!, false) as keepalive.HttpAgent);
            this.httpsAgent = lazy(() => keepalive.createAgent(config.keepAlive!, true) as keepalive.HttpsAgent);
        }
    }

    public async get<T>(url: string, params?: HttpClientParams): Promise<T> {
        return this.executeRequest<T>("GET", url, undefined, params);
    }

    public async post<T>(url: string, data?: any, params?: HttpClientParams): Promise<T> {
        return this.executeRequest<T>("POST", url, data, params);
    }

    public async delete<T = void>(url: string, params?: HttpClientParams): Promise<T> {
        return this.executeRequest<T>("DELETE", url, undefined, params);
    }

    public async put<T>(url: string, data?: any, params?: HttpClientParams): Promise<T> {
        return this.executeRequest<T>("PUT", url, data, params);
    }

    public async patch<T>(url: string, data?: any, params?: HttpClientParams): Promise<T> {
        return this.executeRequest<T>("PATCH", url, data, params);
    }

    private async executeRequest<T>(
        method: string,
        url: string,
        data?: any,
        params?: HttpClientParams
    ): Promise<T> {
        return retry(
            this.retryConfig,
            () => this.makeRequest<T>(method, url, data, params?.headers),
            params?.shouldRetry || this.shouldRetry || shouldRetry,
            params?.wrapError || this.wrapError
        );
    }

    private async makeRequest<T>(
        method: string,
        uri: string,
        data?: any,
        headers?: Record<string, string>
    ): Promise<T> {
        const url = this.baseUrl ? `${this.baseUrl}${uri}` : uri;
        const allHeaders = { ...this.headers, ...headers };
        log.debug({ method, url, body: data, headers: allHeaders }, "Making HTTP request");

        try {
            let request = superagent[method.toLowerCase()](url)
                .set(allHeaders)
                .timeout(this.timeout);

            // Add keepalive agent if configured
            if (this.httpAgent && this.httpsAgent) {
                const agent = url.startsWith("https://") ? this.httpsAgent.get() : this.httpAgent.get();
                if (agent) {
                    request = request.agent(agent);
                }
            }

            if (data && (method === "POST" || method === "PUT" || method === "PATCH")) {
                request = request.send(data);
            }

            const response = await request;

            log.debug({
                method,
                url,
                statusCode: response.status,
                responseBody: response.body
            }, "HTTP request successful");

            return response.body;
        } catch (error: any) {
            const statusCode = error.response?.status;
            const responseBody = error.response?.body;
            log.error({
                method,
                url,
                statusCode,
                error: error.message,
                responseBody
            }, "HTTP request failed");

            if (statusCode) {
                const errorMessage = responseBody?.message || responseBody?.error || `HTTP ${statusCode}`;
                throw new HttpClientError(errorMessage, statusCode, responseBody);
            } else {
                throw new HttpClientError(`Request failed: ${error.message}`, undefined, error);
            }
        }
    }
}

export function createHttpClient(config: HttpClientConfig): HttpClient {
    return new HttpClient(config);
}
