import { measures } from "@skywind-group/sw-utils";
import { type DomainDetector, getDomainDetector } from "../services/domainDetector/domainDetector";
import { loadDomains } from "../services/domainDetector/loadDomains";
import { CronJob } from "../utils/cronJob";
import config from "../config";

export class DomainMonitoringJob {
    private readonly detectors = new Map<string, DomainDetector>();

    public async fire() {
        await measures.measureProvider.runInTransaction("Domain monitoring job", async () => {
            try {
                const items = await loadDomains();
                for (const [adapterId, domains] of items.entries()) {
                    await this.getDetector(adapterId).update(domains);
                }
            } catch (err) {
                measures.measureProvider.saveError(err);
                throw err;
            }
        });
    }

    private getDetector(adapter: string) {
        let item = this.detectors.get(adapter);
        if (!item) {
            item = getDomainDetector(adapter);
            this.detectors.set(adapter, item);
        }
        return item;
    }
}

const job = new DomainMonitoringJob();

let cronJob: CronJob;

export async function initDomainMonitoringJob(): Promise<void> {
    if (config.domainMonitoring.cronJob.enabled && !cronJob) {
        cronJob = new CronJob({
            name: "domain-monitoring",
            schedule: config.domainMonitoring.cronJob.schedule,
            timeout: config.domainMonitoring.cronJob.timeout
        }, job.fire.bind(job));

        if (config.domainMonitoring.cronJob.runOnServerStart) {
            cronJob.invoke.bind(job);
            await cronJob.invoke();
        }
    }
}
