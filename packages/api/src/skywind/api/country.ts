import * as express from "express";
import EntityCache from "../cache/entity";
import EntityCountryService from "../services/entityCountry";
import {
    auditable,
    authenticate,
    authorize,
    countryValidator,
    getBooleanParamFromRequestQuery,
    getEntityPath,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { NextFunction, Response } from "express";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & UserInfoHolder;

export const countriesValidator = validate({
    countries: {
        isCountryCode: true
    }
});

/**
 * Get list of available countries for a specific entity
 *
 */
router.get("/entities/:path/countries",
    authenticate,
    authorize,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne({ path: getEntityPath(req) }, req.keyEntity.path, true);
            const service = new EntityCountryService(entity);

            res.send(service.getList());
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Add country to entity. Should be available also in parent entity.
 *
 */
router.put("/entities/:path/countries/:country",
    authenticate,
    authorize,
    validate({ country: countryValidator }),
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne(
                { path: getEntityPath(req) },
                req.keyEntity.path, true, true);
            const service = new EntityCountryService(entity);
            await service.add(req.params["country"]);
            EntityCache.reset();

            res.status(201).send(service.getList());
        } catch (err) {
            next(err);
        }
    });

/**
 * Add countries to entity. Should be available also in parent entity.
 *
 */

router.put("/entities/:path/countries",
    authenticate,
    authorize,
    countriesValidator,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne(
                { path: getEntityPath(req) },
                req.keyEntity.path, true, true);
            const service = new EntityCountryService(entity);
            await service.add(req.body.countries);
            EntityCache.reset();

            res.status(201).send(service.getList());
        } catch (err) {
            next(err);
        }
    });

router.delete("/entities/:path/countries",
    authenticate,
    authorize,
    countriesValidator,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne({ path: getEntityPath(req) }, req.keyEntity.path, true, true);
            const service = new EntityCountryService(entity);
            await service.remove(req.body.countries);
            EntityCache.reset();

            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

/**
 * Remove country from entity
 *
 */
router.delete("/entities/:path/countries/:country",
    authenticate,
    authorize,
    validate({
        country: countryValidator
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne(
                { path: getEntityPath(req) },
                req.keyEntity.path, true, true);
            const service = new EntityCountryService(entity);
            await service.remove(req.params["country"], getBooleanParamFromRequestQuery(req, "force"));
            EntityCache.reset();

            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

router.get("/countries",
    authenticate,
    authorize,
    getCountries);

export async function getCountries(req: Request, res: Response, next: NextFunction) {
    const service = new EntityCountryService(req.keyEntity);
    res.send(service.getList());
    next();
}

router.put("/countries",
    authenticate,
    authorize,
    countriesValidator,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const service = new EntityCountryService(req.keyEntity);
            await service.add(req.body.countries);
            EntityCache.reset();
            res.status(201).send(service.getList());
        } catch (err) {
            next(err);
        }
    });

router.delete("/countries",
    authenticate,
    authorize,
    countriesValidator,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const service = new EntityCountryService(req.keyEntity);
            await service.remove(req.body.countries);
            EntityCache.reset();
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export default router;
