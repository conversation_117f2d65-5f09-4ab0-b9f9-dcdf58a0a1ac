import * as express from "express";
import EntityCache from "../cache/entity";
import EntityCurrencyService from "../services/entityCurrency";
import {
    auditable,
    authenticate,
    authorize,
    currencyValidator,
    getBooleanParamFromRequestQuery,
    getEntityPath,
    validate
} from "./middleware/middleware";
import { KeyEntityHolder, UserInfoHolder } from "../services/security";
import { NextFunction, Response } from "express";

const router: express.Router = express.Router();
type Request = express.Request & KeyEntityHolder & UserInfoHolder;

export const currenciesValidator = validate({
    currencies: {
        isCurrencyCode: true
    }
});

/**
 * Add currencies to entity. Should be available also in parent entity.
 *
 */

router.put("/entities/:path/currencies",
    authenticate,
    authorize,
    currenciesValidator,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne(
                { path: getEntityPath(req) },
                req.keyEntity.path, true, true);
            const service = new EntityCurrencyService(entity);
            await service.add(req.body.currencies);
            EntityCache.reset();

            res.status(201).send(service.getList());
        } catch (err) {
            next(err);
        }
    });

router.delete("/entities/:path/currencies",
    authenticate,
    authorize,
    currenciesValidator,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne(
                { path: getEntityPath(req) },
                req.keyEntity.path, true, true);
            const service = new EntityCurrencyService(entity);
            await service.remove(req.body.currencies);
            EntityCache.reset();

            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

/**
 * Get list of available currencies for a specific entity
 *
 */
router.get("/entities/:path/currencies",
    authenticate,
    authorize,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne({ path: getEntityPath(req) }, req.keyEntity.path, true);
            const service = new EntityCurrencyService(entity);

            res.send(service.getList());
            next();
        } catch (err) {
            next(err);
        }
    });

/**
 * Add currency to entity. Should be available also in parent entity.
 *
 */
router.put("/entities/:path/currencies/:currency",
    authenticate,
    authorize,
    validate({
        currency: currencyValidator,
    }),
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne({ path: getEntityPath(req) }, req.keyEntity.path, true, true);
            const service = new EntityCurrencyService(entity);
            await service.add(req.params["currency"]);
            EntityCache.reset();
            res.status(201).send(service.getList());
        } catch (err) {
            next(err);
        }
    });

/**
 * Remove currency from entity
 *
 */
router.delete("/entities/:path/currencies/:currency",
    authenticate,
    authorize,
    validate({
        currency: currencyValidator
    }),
    auditable,
    async (req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const entity = await EntityCache.findOne(
                { path: getEntityPath(req) },
                req.keyEntity.path, true, true);
            const service = new EntityCurrencyService(entity);
            await service.remove(req.params["currency"], getBooleanParamFromRequestQuery(req, "force"));
            EntityCache.reset();
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

/**
 * Get list of available currencies for a specific entity
 */

router.get("/currencies",
    authenticate,
    authorize,
    getCurrencies);

export async function getCurrencies(req: Request, res: Response, next: NextFunction) {
    const service = new EntityCurrencyService(req.keyEntity);
    res.send(service.getList());
    next();
}

router.put("/currencies",
    authenticate,
    authorize,
    currenciesValidator,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const service = new EntityCurrencyService(req.keyEntity);
            await service.add(req.body.currencies);
            res.status(201).send(service.getList());
        } catch (err) {
            next(err);
        }
    });

router.delete("/currencies",
    authenticate,
    authorize,
    auditable,
    async(req: Request, res: express.Response, next: express.NextFunction) => {
        try {
            const service = new EntityCurrencyService(req.keyEntity);
            await service.remove(req.body.currencies);
            res.status(204).end();
        } catch (err) {
            next(err);
        }
    });

export default router;
