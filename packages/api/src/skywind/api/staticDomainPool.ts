import { auditable, authenticate, authorize, decodePid, getEntity, validate, getBooleanParamFromRequestQuery } from "./middleware/middleware";
import * as express from "express";
import { getStaticDomainPoolService } from "../services/staticDomainPool";
import { KeyEntityHolder } from "../services/security";
import { EntityStaticDomainPoolService } from "../services/entityStaticDomainPool";
import { DomainPoolCreateData, StaticDomainPoolAttributes } from "../entities/domainPool";
import { StaticDomainType } from "../entities/domain";

const router: express.Router = express.Router();

type LobbyDomainsCreateData = { lobbyDomains?: DomainPoolCreateData["domains"] };
type LobbyDomainsType = { lobbyDomains?: StaticDomainPoolAttributes["domains"] };

// Helper function to add backward compatibility fields for lobby domains
function addLobbyDomains(pool: StaticDomainPoolAttributes): StaticDomainPoolAttributes & LobbyDomainsType {
    return {
        ...pool,
        lobbyDomains: pool.domains?.filter(domain => domain.type === StaticDomainType.LOBBY)
    };
}

async function getStaticDomainPools(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const pools = await getStaticDomainPoolService().findAll();
        res.send(pools.map(addLobbyDomains));
    } catch (err) {
        next(err);
    }
}

async function createStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { name, domainDetectorAdapterId, domains, lobbyDomains }: DomainPoolCreateData & LobbyDomainsCreateData = req.body;
        const pool = await getStaticDomainPoolService().create({
            name,
            domainDetectorAdapterId,
            domains: domains.concat(lobbyDomains || [])
        });
        res.status(201).send(addLobbyDomains(pool));
    } catch (err) {
        next(err);
    }
}

async function getStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const pool = await getStaticDomainPoolService().findById(req.params.poolId);
        res.send(addLobbyDomains(pool));
    } catch (err) {
        next(err);
    }
}

async function updateStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { name, domainDetectorAdapterId, domains: staticDomain, lobbyDomains }: Partial<DomainPoolCreateData> & LobbyDomainsCreateData = req.body;
        const domains = (staticDomain || []).concat(lobbyDomains || []);
        const updateData: Partial<DomainPoolCreateData> = {};
        if (name !== undefined) updateData.name = name;
        if (domainDetectorAdapterId !== undefined) updateData.domainDetectorAdapterId = domainDetectorAdapterId;
        if (domains.length) updateData.domains = domains;

        const pool = await getStaticDomainPoolService().update(req.params.poolId, updateData);
        res.send(addLobbyDomains(pool));
    } catch (err) {
        next(err);
    }
}

async function removeStaticDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await getStaticDomainPoolService().remove(req.params.poolId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function addStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        const pool = await getStaticDomainPoolService().addDomain(poolId, domainId);
        res.send(pool);
    } catch (err) {
        next(err);
    }
}

async function removeStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getStaticDomainPoolService().removeDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function enableStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getStaticDomainPoolService().enableDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function disableStaticDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getStaticDomainPoolService().disableDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getEntityStaticDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const inherited = getBooleanParamFromRequestQuery(req, "inherited", false);
        const entity = getEntity(req);
        const entityStaticDomainPoolService = new EntityStaticDomainPoolService(entity);
        const pool = await entityStaticDomainPoolService.getPool(inherited);
        res.send(addLobbyDomains(pool));
    } catch (err) {
        next(err);
    }
}

async function addEntityStaticDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getEntity(req);
        const entityStaticDomainPoolService = new EntityStaticDomainPoolService(entity);
        const pool = await entityStaticDomainPoolService.addPool(req.params.poolId);
        res.send(addLobbyDomains(pool));
    } catch (err) {
        next(err);
    }
}

async function removeEntityStaticDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getEntity(req);
        const entityStaticDomainPoolService = new EntityStaticDomainPoolService(entity);
        await entityStaticDomainPoolService.removePool();
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

// static domain pool management
router.get("/domain-pools/static", authenticate, authorize, getStaticDomainPools);
router.post("/domain-pools/static",
    authenticate,
    authorize,
    validate({
        name: { isString: true, notEmpty: true },
        domainDetectorAdapterId: { isString: true, optional: true },
        domains: { isDomainPoolItemArray: true, optional: true },
        lobbyDomains: { isDomainPoolItemArray: true, optional: true }
    }),
    decodePid({ forceReturnIfNumber: true, keysToParse: ["domains", "lobbyDomains"] }),
    auditable,
    createStaticDomainPool);
router.get("/domain-pools/:poolId/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    getStaticDomainPool);
router.patch("/domain-pools/:poolId/static",
    authenticate, authorize,
    validate({
        name: { isString: true, optional: true },
        domainDetectorAdapterId: { isString: true, optional: true },
        domains: { isDomainPoolItemArray: true, optional: true },
        lobbyDomains: { isDomainPoolItemArray: true, optional: true }
    }),
    decodePid({ forceReturnIfNumber: true, keysToParse: ["domains", "lobbyDomains"] }),
    auditable,
    updateStaticDomainPool);
router.delete("/domain-pools/:poolId/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    removeStaticDomainPool);

router.put("/domain-pools/:poolId/static/:domainId",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    addStaticDomainPoolItem);
router.delete("/domain-pools/:poolId/static/:domainId",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    removeStaticDomainPoolItem);
router.put("/domain-pools/:poolId/static/:domainId/enable",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    enableStaticDomainPoolItem);
router.put("/domain-pools/:poolId/static/:domainId/disable",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    disableStaticDomainPoolItem);

router.put("/domain-pools/:poolId/static/lobby/:domainId",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    addStaticDomainPoolItem);
router.delete("/domain-pools/:poolId/static/lobby/:domainId",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    removeStaticDomainPoolItem);
router.put("/domain-pools/:poolId/static/lobby/:domainId/enable",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    enableStaticDomainPoolItem);
router.put("/domain-pools/:poolId/static/lobby/:domainId/disable",
    authenticate,
    authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    disableStaticDomainPoolItem);

router.get("/entities/:path/domain-pools/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    getEntityStaticDomainPool);
router.put("/entities/:path/domain-pools/:poolId/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    addEntityStaticDomainPool);
router.delete("/entities/:path/domain-pools/static",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    removeEntityStaticDomainPool);

export default router;
