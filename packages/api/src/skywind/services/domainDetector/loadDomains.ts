import { Op } from "sequelize";
import { Models } from "../../models/models";
import { EntityStatus } from "../../entities/entity";
import { DomainStatus } from "../../entities/domain";
import { AdapterDomains, DomainSources } from "./types";
import { DomainSource, StaticPoolDomainSource, DynamicPoolDomainSource, DynamicDomainSource, StaticDomainSource } from "./domainSource";

interface DomainData {
    id: number;
    domain: string;
}

interface PoolItemData {
    isActive?: boolean;
    blockedDate?: Date;
}

interface StaticDomainPoolData {
    id: number;
    domainDetectorAdapterId: string;
    domains?: Array<DomainData & {
        StaticDomainPoolItem?: PoolItemData;
    }>;
}

interface DynamicDomainPoolData {
    id: number;
    domainDetectorAdapterId?: string;
    domains?: Array<DomainData & {
        DynamicDomainPoolItem?: PoolItemData;
    }>;
}

interface EntityWithDomains {
    id: number;
    StaticDomainPoolModel?: StaticDomainPoolData;
    DynamicDomainPoolModel?: DynamicDomainPoolData;
    staticDomain?: DomainData;
    lobbyDomain?: DomainData;
    liveStreamingDomain?: DomainData;
    ehubDomain?: DomainData;
    DynamicDomainModel?: DomainData;
}

type AdapterDomainInfo = { domainId: number; domainDetectorAdapterId: string; sources: DomainSource[]; };

export async function loadDomains(): Promise<AdapterDomains> {
    const entities = await Models.EntityModel.findAll({
        where: {
            [Op.or]: [
                {
                    staticDomainPoolId: {
                        [Op.ne]: null
                    }
                },
                {
                    dynamicDomainPoolId: {
                        [Op.ne]: null
                    }
                }
            ],
            status: EntityStatus.NORMAL
        },
        include: [
            {
                model: Models.StaticDomainPoolModel,
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainDetectorAdapterId"],
                required: false,
                include: [
                    {
                        model: Models.StaticDomainModel,
                        as: "domains",
                        where: {
                            status: DomainStatus.ACTIVE
                        },
                        through: {
                            attributes: ["isActive", "blockedDate"],
                            where: {
                                isActive: true,
                                blockedDate: null
                            }
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.DynamicDomainPoolModel,
                where: {
                    domainDetectorAdapterId: {
                        [Op.and]: [
                            { [Op.ne]: null },
                            { [Op.ne]: "" }
                        ]
                    }
                },
                attributes: ["id", "domainDetectorAdapterId"],
                required: false,
                include: [
                    {
                        model: Models.DynamicDomainModel,
                        as: "domains",
                        where: {
                            status: DomainStatus.ACTIVE
                        },
                        through: {
                            attributes: ["isActive", "blockedDate"],
                            where: {
                                isActive: true,
                                blockedDate: null
                            }
                        },
                        attributes: ["id", "domain"]
                    }
                ]
            },
            {
                model: Models.DynamicDomainModel,
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain"],
                required: false
            },
            {
                association: "staticDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain"],
                required: false
            },
            {
                association: "lobbyDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain"],
                required: false
            },
            {
                association: "liveStreamingDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain"],
                required: false
            },
            {
                association: "ehubDomain",
                where: {
                    status: DomainStatus.ACTIVE
                },
                attributes: ["id", "domain"],
                required: false
            }
        ],
        attributes: ["id"]
    });

    const items = new Map<string, AdapterDomainInfo>();
    for (const entity of entities) {
        toDomains(entity.toJSON() as EntityWithDomains, items);
    }
    const adapterDomains: AdapterDomains = new Map();
    for (const [domain, { domainDetectorAdapterId, domainId, sources }] of items.entries()) {
        const adapterDomain: DomainSources = adapterDomains.get(domainDetectorAdapterId) || new Map();
        adapterDomain.set(domain, {
            domainId,
            sources: [
                ...(adapterDomain.get(domain)?.sources || []),
                ...sources
            ]
        });
        adapterDomains.set(domainDetectorAdapterId, adapterDomain);
    }
    return adapterDomains;
}

function toDomains(entity: EntityWithDomains, items: Map<string, AdapterDomainInfo>): void {
    const fnDomain = (domainDetectorAdapterId: string) => ({ id, domain }: DomainData, source?: DomainSource) => {
        const existing = items.get(domain);
        items.set(domain, {
            domainId: id,
            domainDetectorAdapterId,
            sources: [source, ...existing?.sources ?? []]
        });
    };

    // Handle static domain pool
    if (entity.StaticDomainPoolModel?.domainDetectorAdapterId) {
        const { id, domainDetectorAdapterId, domains } = entity.StaticDomainPoolModel;
        const addDomain = fnDomain(domainDetectorAdapterId);

        if (Array.isArray(domains) && domains.length > 0) {
            for (const domain of domains) {
                addDomain(domain, new StaticPoolDomainSource(id, domain.id));
            }
        }
        if (entity.staticDomain) {
            addDomain(entity.staticDomain, new StaticDomainSource(entity.staticDomain.id));
        }
        if (entity.lobbyDomain) {
            addDomain(entity.lobbyDomain, new StaticDomainSource(entity.lobbyDomain.id));
        }
        if (entity.liveStreamingDomain) {
            addDomain(entity.liveStreamingDomain, new StaticDomainSource(entity.liveStreamingDomain.id));
        }
        if (entity.ehubDomain) {
            addDomain(entity.ehubDomain, new StaticDomainSource(entity.ehubDomain.id));
        }
    }

    // Handle dynamic domain pool
    if (entity.DynamicDomainPoolModel?.domainDetectorAdapterId) {
        const { id: poolId, domainDetectorAdapterId, domains } = entity.DynamicDomainPoolModel;
        const addDomain = fnDomain(domainDetectorAdapterId);

        if (Array.isArray(domains) && domains.length > 0) {
            for (const domain of domains) {
                addDomain(domain, new DynamicPoolDomainSource(poolId, domain.id));
            }
        }
        if (entity.DynamicDomainModel) {
            addDomain(entity.DynamicDomainModel, new DynamicDomainSource(entity.DynamicDomainModel.id));
        }
    }
}
