import { DomainSource } from "./domainSource";
import { getTapkingAdapter } from "./tapkingAdapter";
import { DomainSources, DomainDetectorAdapter, DomainStatus } from "./types";

interface DomainDetectorInfo {
    domainId?: number;
    status?: DomainStatus;
    sources?: DomainSource[];
}

export class DomainDetector {
    private readonly domains: Map<string, DomainDetectorInfo> = new Map();

    constructor(private readonly adapter: DomainDetectorAdapter) {
    }

    public async update(domains: DomainSources) {
        const list = await this.adapter.list();
        for (const { domain, status } of list) {
            this.domains.set(domain, {
                status,
                sources: this.domains.get(domain)?.sources
            });
        }
        for (const domain of this.domains.keys()) {
            if (!domains.has(domain)) {
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
        for (const [domain, { domainId, sources }] of domains.entries()) {
            if (!this.domains.has(domain)) {
                await this.adapter.register(domain);
                this.domains.set(domain, { domainId, status: { accessStatus: "UNKNOWN" }, sources });
            }
        }
        for (const [domain, { domainId, status, sources }] of this.domains.entries()) {
            if (domainId) {
                for (const source of sources ?? []) {
                    if (status?.accessStatus === "BLOCKED") {
                        await source.setBlocked(status, this.adapter.adapterId);
                        await this.adapter.remove(domain);
                        this.domains.delete(domain);
                    }
                }
            }
        }
    }
}

function getAdapter(adapter: string): DomainDetectorAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter();
    }
    throw new Error(`Domain detector adapter ${adapter} is not supported.`);
}

export function getDomainDetector(adapter: string): DomainDetector {
    return new DomainDetector(getAdapter(adapter));
}
