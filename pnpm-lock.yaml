lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      '@skywind-group/sw-utils':
        specifier: 2.5.3
        version: 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      '@types/lodash':
        specifier: 4.17.12
        version: 4.17.12
      '@types/node':
        specifier: ^22.14.1
        version: 22.14.1
      '@types/superagent':
        specifier: ^8.1.9
        version: 8.1.9
      '@typescript-eslint/eslint-plugin':
        specifier: ^8.30.1
        version: 8.43.0(@typescript-eslint/parser@8.43.0(eslint@9.35.0)(typescript@5.6.3))(eslint@9.35.0)(typescript@5.6.3)
      '@typescript-eslint/parser':
        specifier: ^8.30.1
        version: 8.43.0(eslint@9.35.0)(typescript@5.6.3)
      bole:
        specifier: 5.0.15
        version: 5.0.15
      chai:
        specifier: ~4.3.10
        version: 4.3.10
      chai-as-promised:
        specifier: ^7.1.1
        version: 7.1.2(chai@4.3.10)
      chai-datetime:
        specifier: 1.8.1
        version: 1.8.1
      chai-shallow-deep-equal:
        specifier: 1.4.4
        version: 1.4.4(chai@4.3.10)
      dotenv:
        specifier: ^16.0.0
        version: 16.6.1
      eslint:
        specifier: ^9.24.0
        version: 9.35.0
      eslint-plugin-sonarjs:
        specifier: ^3.0.2
        version: 3.0.5(eslint@9.35.0)
      eslint-plugin-unicorn:
        specifier: ^58.0.0
        version: 58.0.0(eslint@9.35.0)
      fast-glob:
        specifier: ^3.3.3
        version: 3.3.3
      lerna:
        specifier: 8.2.2
        version: 8.2.2(@types/node@22.14.1)(encoding@0.1.13)
      mocha:
        specifier: 10.7.3
        version: 10.7.3
      mocha-typescript:
        specifier: 1.1.12
        version: 1.1.12
      reflect-metadata:
        specifier: 0.1.13
        version: 0.1.13
      sinon:
        specifier: 16.1.3
        version: 16.1.3
      sinon-chai:
        specifier: 3.7.0
        version: 3.7.0(chai@4.3.10)(sinon@16.1.3)
      superagent:
        specifier: 10.2.3
        version: 10.2.3
      ts-node:
        specifier: ^10.7.0
        version: 10.9.2(@types/node@22.14.1)(typescript@5.6.3)
      tsconfig-paths:
        specifier: ^4.2.0
        version: 4.2.0
      typescript:
        specifier: 5.6.3
        version: 5.6.3

  packages/adapters:
    dependencies:
      '@skywind-group/sw-adapter-regulation-support':
        specifier: ^1.0.2
        version: 1.0.2(generic-pool@3.9.0)(ioredis@5.5.0)
      '@skywind-group/sw-currency-exchange':
        specifier: 2.3.19
        version: 2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)
      '@skywind-group/sw-management-playservice':
        specifier: workspace:~2.141.0-develop
        version: link:../playservice
      '@skywind-group/sw-management-promo-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../promo-wallet
      '@skywind-group/sw-management-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../wallet
      '@skywind-group/sw-pop-notification':
        specifier: 0.1.0
        version: 0.1.0
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      ioredis:
        specifier: 5.5.0
        version: 5.5.0
      properties:
        specifier: 1.2.1
        version: 1.2.1
      request:
        specifier: 2.88.0
        version: 2.88.0
      shortid:
        specifier: 2.2.16
        version: 2.2.16
      uuid:
        specifier: 9.0.1
        version: 9.0.1
    devDependencies:
      '@skywind-group/sw-messaging':
        specifier: 0.2.4
        version: 0.2.4

  packages/api:
    dependencies:
      '@fastify/compress':
        specifier: 8.0.1
        version: 8.0.1
      '@fastify/cookie':
        specifier: 11.0.2
        version: 11.0.2
      '@fastify/middie':
        specifier: 9.0.3
        version: 9.0.3
      '@fastify/static':
        specifier: 8.1.1
        version: 8.1.1
      '@google-cloud/storage':
        specifier: 7.16.0
        version: 7.16.0(encoding@0.1.13)
      '@skywind-group/gelf-stream':
        specifier: 1.2.6
        version: 1.2.6
      '@skywind-group/sw-adapter-regulation-support':
        specifier: ^1.0.2
        version: 1.0.2(generic-pool@3.9.0)(ioredis@5.5.0)
      '@skywind-group/sw-currency-exchange':
        specifier: 2.3.19
        version: 2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)
      '@skywind-group/sw-deferred-payment':
        specifier: ^2.0.0
        version: 2.1.0
      '@skywind-group/sw-deferred-payment-cache':
        specifier: ^2.0.0
        version: 2.2.0
      '@skywind-group/sw-deferred-payment-client':
        specifier: ^2.0.0
        version: 2.2.0(superagent@10.2.3)
      '@skywind-group/sw-domain-routing':
        specifier: 3.1.0
        version: 3.1.0(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))
      '@skywind-group/sw-falcon-oauth':
        specifier: 1.2.2
        version: 1.2.2(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))
      '@skywind-group/sw-game-provider-ext-game-history':
        specifier: ~3.1.0
        version: 3.1.10(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(bole@5.0.15)(ioredis@5.5.0)(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(pg@8.14.1)(sequelize@6.37.7(pg@8.14.1))(superagent-proxy@3.0.0(superagent@10.2.3))(superagent@10.2.3)
      '@skywind-group/sw-gameprovider-adapter-core':
        specifier: ~1.3.2
        version: 1.3.2(agentkeepalive@4.6.0)(superagent-proxy@3.0.0(superagent@10.2.3))
      '@skywind-group/sw-live-core':
        specifier: 2.0.6
        version: 2.0.6
      '@skywind-group/sw-management-adapters':
        specifier: workspace:~2.141.0-develop
        version: link:../adapters
      '@skywind-group/sw-management-deferredpayment':
        specifier: workspace:~2.141.0-develop
        version: link:../deferredpayment
      '@skywind-group/sw-management-gameprovider':
        specifier: workspace:~2.141.0-develop
        version: link:../gameprovider
      '@skywind-group/sw-management-gameprovider-core':
        specifier: workspace:~2.141.0-develop
        version: link:../gameprovider-core
      '@skywind-group/sw-management-i18n':
        specifier: workspace:~2.141.0-develop
        version: link:../i18n
      '@skywind-group/sw-management-playersession':
        specifier: workspace:~2.141.0-develop
        version: link:../playersession
      '@skywind-group/sw-management-playservice':
        specifier: workspace:~2.141.0-develop
        version: link:../playservice
      '@skywind-group/sw-management-promo-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../promo-wallet
      '@skywind-group/sw-management-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../wallet
      '@skywind-group/sw-messaging':
        specifier: 0.2.4
        version: 0.2.4
      '@skywind-group/sw-sm-result-builder':
        specifier: ~0.1.11
        version: 0.1.67
      '@skywind-group/sw-utils':
        specifier: 2.5.3
        version: 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      '@skywind-group/sw-wallet':
        specifier: 1.0.8
        version: 1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      agentkeepalive:
        specifier: ^4.5.0
        version: 4.6.0
      ajv:
        specifier: ^6.12.0
        version: 6.12.6
      append-query:
        specifier: 2.1.1
        version: 2.1.1
      authenticator:
        specifier: 1.1.5
        version: 1.1.5
      body-parser:
        specifier: 1.20.3
        version: 1.20.3
      bole:
        specifier: 5.0.15
        version: 5.0.15
      bole-console:
        specifier: 0.1.10
        version: 0.1.10
      cls-hooked:
        specifier: 4.2.2
        version: 4.2.2
      compression:
        specifier: 1.7.4
        version: 1.7.4
      cookie-parser:
        specifier: 1.4.7
        version: 1.4.7
      emitter-listener:
        specifier: 1.1.2
        version: 1.1.2
      express:
        specifier: 4.21.1
        version: 4.21.1
      express-csv:
        specifier: 0.6.0
        version: 0.6.0
      express-http-proxy:
        specifier: 2.1.1
        version: 2.1.1
      express-mung:
        specifier: 0.5.1
        version: 0.5.1
      express-prom-bundle:
        specifier: 7.0.2
        version: 7.0.2(prom-client@15.0.0)
      express-validator:
        specifier: 5.3.1
        version: 5.3.1
      fastify:
        specifier: 5.3.0
        version: 5.3.0
      generic-pool:
        specifier: 3.9.0
        version: 3.9.0
      hashids:
        specifier: ^2.3.0
        version: 2.3.0
      hashring:
        specifier: 3.2.0
        version: 3.2.0
      inversify:
        specifier: 5.0.1
        version: 5.0.1
      inversify-express-utils:
        specifier: 6.3.2
        version: 6.3.2
      inversify-inject-decorators:
        specifier: 3.1.0
        version: 3.1.0
      ioredis:
        specifier: 5.5.0
        version: 5.5.0
      is-cidr:
        specifier: ^4.0.2
        version: 4.0.2
      js-big-integer:
        specifier: 1.0.2
        version: 1.0.2
      json-refs:
        specifier: 3.0.12
        version: 3.0.12
      jsonwebtoken:
        specifier: 9.0.2
        version: 9.0.2
      kafka-node:
        specifier: 5.0.0
        version: 5.0.0
      lodash:
        specifier: 4.17.21
        version: 4.17.21
      maxmind:
        specifier: 4.3.22
        version: 4.3.22
      method-override:
        specifier: 3.0.0
        version: 3.0.0
      node-cache:
        specifier: 5.1.2
        version: 5.1.2
      node-mailjet:
        specifier: 3.3.1
        version: 3.3.1
      node-schedule:
        specifier: 2.1.1
        version: 2.1.1
      pg:
        specifier: 8.14.1
        version: 8.14.1
      prom-client:
        specifier: 15.0.0
        version: 15.0.0
      qrcode:
        specifier: 1.3.3
        version: 1.3.3
      reflect-metadata:
        specifier: 0.2.2
        version: 0.2.2
      request:
        specifier: 2.88.0
        version: 2.88.0
      sequelize:
        specifier: 6.37.7
        version: 6.37.7(pg@8.14.1)
      serve-static:
        specifier: 1.16.2
        version: 1.16.2
      socket.io:
        specifier: 4.8.1
        version: 4.8.1
      socket.io-v2:
        specifier: npm:socket.io@2.1.1
        version: socket.io@2.1.1
      superagent:
        specifier: 10.2.3
        version: 10.2.3
      swagger-tools:
        specifier: 0.10.1
        version: 0.10.1
      to-ico:
        specifier: ^1.1.5
        version: 1.1.5
      trek-captcha:
        specifier: 0.4.0
        version: 0.4.0
      twilio:
        specifier: 3.29.0
        version: 3.29.0
      uuid:
        specifier: 9.0.1
        version: 9.0.1
      validator:
        specifier: ^13.9.0
        version: 13.15.15
    devDependencies:
      '@types/chai':
        specifier: ^4.3.11
        version: 4.3.20
      '@types/chai-as-promised':
        specifier: ^7.1.8
        version: 7.1.8
      '@types/chai-datetime':
        specifier: 1.0.0
        version: 1.0.0
      '@types/cls-hooked':
        specifier: ^4.2.0
        version: 4.3.9
      '@types/express':
        specifier: 4.0.35
        version: 4.0.35
      '@types/express-http-proxy':
        specifier: 1.6.6
        version: 1.6.6
      '@types/express-serve-static-core':
        specifier: 4.0.48
        version: 4.0.48
      '@types/express-validator':
        specifier: 2.20.33
        version: 2.20.33
      '@types/hashring':
        specifier: 3.2.5
        version: 3.2.5
      '@types/i18n':
        specifier: 0.13.12
        version: 0.13.12
      '@types/jsonwebtoken':
        specifier: ^9.0.2
        version: 9.0.10
      '@types/lodash':
        specifier: 4.17.12
        version: 4.17.12
      '@types/mocha':
        specifier: ^10.0.8
        version: 10.0.10
      '@types/node':
        specifier: 22.14.1
        version: 22.14.1
      '@types/pg':
        specifier: 8.11.11
        version: 8.11.11
      '@types/request':
        specifier: 2.48.12
        version: 2.48.12
      '@types/serve-static':
        specifier: 1.15.7
        version: 1.15.7
      '@types/sinon':
        specifier: 10.0.20
        version: 10.0.20
      '@types/sinon-chai':
        specifier: 3.2.12
        version: 3.2.12
      '@types/supertest':
        specifier: 6.0.2
        version: 6.0.2
      '@types/to-ico':
        specifier: ^1.1.3
        version: 1.1.3
      '@types/uuid':
        specifier: 9.0.8
        version: 9.0.8
      '@types/validator':
        specifier: ^13.7.17
        version: 13.15.3
      chai:
        specifier: ~4.3.10
        version: 4.3.10
      chai-as-promised:
        specifier: ^7.1.1
        version: 7.1.2(chai@4.3.10)
      chai-datetime:
        specifier: 1.8.1
        version: 1.8.1
      chai-shallow-deep-equal:
        specifier: 1.4.4
        version: 1.4.4(chai@4.3.10)
      factory-girl:
        specifier: 5.0.2
        version: 5.0.2
      mocha:
        specifier: 10.7.3
        version: 10.7.3
      mocha-typescript:
        specifier: 1.1.12
        version: 1.1.12
      nodemon:
        specifier: ^2.0.15
        version: 2.0.22
      nyc:
        specifier: 15.1.0
        version: 15.1.0
      sinon:
        specifier: 16.1.3
        version: 16.1.3
      sinon-chai:
        specifier: 3.7.0
        version: 3.7.0(chai@4.3.10)(sinon@16.1.3)
      socket.io-client:
        specifier: 2.1.1
        version: 2.1.1
      supertest:
        specifier: 7.0.0
        version: 7.0.0
      ts-node:
        specifier: ^10.7.0
        version: 10.9.2(@types/node@22.14.1)(typescript@5.6.3)
      tsconfig-paths:
        specifier: ^4.2.0
        version: 4.2.0
      typescript:
        specifier: ^5.6.3
        version: 5.6.3

  packages/deferredpayment:
    dependencies:
      '@skywind-group/sw-deferred-payment':
        specifier: ^2.0.0
        version: 2.1.0
      '@skywind-group/sw-deferred-payment-cache':
        specifier: ^2.0.0
        version: 2.2.0
      '@skywind-group/sw-deferred-payment-client':
        specifier: ^2.0.0
        version: 2.2.0(superagent@10.2.3)
      '@skywind-group/sw-management-playersession':
        specifier: workspace:~2.141.0-develop
        version: link:../playersession
      '@skywind-group/sw-messaging':
        specifier: 0.2.4
        version: 0.2.4
    devDependencies:
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      generic-pool:
        specifier: 3.9.0
        version: 3.9.0
      ioredis:
        specifier: 5.5.0
        version: 5.5.0

  packages/gameprovider:
    dependencies:
      '@skywind-group/sw-deferred-payment':
        specifier: ^2.0.0
        version: 2.1.0
      '@skywind-group/sw-management-promo-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../promo-wallet
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      uuid:
        specifier: ^9.0.1
        version: 9.0.1
    devDependencies:
      '@skywind-group/sw-wallet':
        specifier: 1.0.8
        version: 1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)

  packages/gameprovider-api:
    dependencies:
      '@fastify/compress':
        specifier: 8.0.1
        version: 8.0.1
      '@fastify/cookie':
        specifier: 11.0.2
        version: 11.0.2
      '@fastify/middie':
        specifier: 9.0.3
        version: 9.0.3
      '@fastify/static':
        specifier: 8.1.1
        version: 8.1.1
      '@skywind-group/gelf-stream':
        specifier: 1.2.6
        version: 1.2.6
      '@skywind-group/sw-adapter-regulation-support':
        specifier: ^1.0.2
        version: 1.0.2(generic-pool@3.9.0)(ioredis@5.5.0)
      '@skywind-group/sw-currency-exchange':
        specifier: 2.3.19
        version: 2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)
      '@skywind-group/sw-deferred-payment-cache':
        specifier: ^2.0.0
        version: 2.2.0
      '@skywind-group/sw-management-deferredpayment':
        specifier: workspace:~2.141.0-develop
        version: link:../deferredpayment
      '@skywind-group/sw-management-gameprovider':
        specifier: workspace:~2.141.0-develop
        version: link:../gameprovider
      '@skywind-group/sw-management-gameprovider-core':
        specifier: workspace:~2.141.0-develop
        version: link:../gameprovider-core
      '@skywind-group/sw-management-i18n':
        specifier: workspace:~2.141.0-develop
        version: link:../i18n
      '@skywind-group/sw-management-playersession':
        specifier: workspace:~2.141.0-develop
        version: link:../playersession
      '@skywind-group/sw-management-playservice':
        specifier: workspace:~2.141.0-develop
        version: link:../playservice
      '@skywind-group/sw-management-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../wallet
      '@skywind-group/sw-messaging':
        specifier: 0.2.4
        version: 0.2.4
      '@skywind-group/sw-utils':
        specifier: 2.5.3
        version: 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      '@skywind-group/sw-wallet':
        specifier: 1.0.8
        version: 1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      agentkeepalive:
        specifier: ^4.5.0
        version: 4.6.0
      bole:
        specifier: 5.0.15
        version: 5.0.15
      bole-console:
        specifier: 0.1.10
        version: 0.1.10
      cls-hooked:
        specifier: 4.2.2
        version: 4.2.2
      emitter-listener:
        specifier: 1.1.2
        version: 1.1.2
      express-prom-bundle:
        specifier: 7.0.2
        version: 7.0.2(prom-client@15.0.0)
      fast-xml-parser:
        specifier: 4.4.1
        version: 4.4.1
      fastify:
        specifier: 5.3.0
        version: 5.3.0
      generic-pool:
        specifier: 3.9.0
        version: 3.9.0
      hashids:
        specifier: ^2.3.0
        version: 2.3.0
      inversify:
        specifier: 5.0.1
        version: 5.0.1
      inversify-express-utils:
        specifier: 6.3.2
        version: 6.3.2
      inversify-inject-decorators:
        specifier: 3.1.0
        version: 3.1.0
      ioredis:
        specifier: 5.5.0
        version: 5.5.0
      js-big-integer:
        specifier: 1.0.2
        version: 1.0.2
      kafka-node:
        specifier: 5.0.0
        version: 5.0.0
      method-override:
        specifier: 3.0.0
        version: 3.0.0
      pg:
        specifier: 8.14.1
        version: 8.14.1
      prom-client:
        specifier: 15.0.0
        version: 15.0.0
      reflect-metadata:
        specifier: 0.2.2
        version: 0.2.2
      sequelize:
        specifier: 6.37.7
        version: 6.37.7(pg@8.14.1)
      superagent:
        specifier: 10.2.3
        version: 10.2.3
      superagent-proxy:
        specifier: 3.0.0
        version: 3.0.0(superagent@10.2.3)
      uuid:
        specifier: ^9.0.1
        version: 9.0.1
    devDependencies:
      '@types/chai':
        specifier: ^4.3.11
        version: 4.3.20
      '@types/chai-as-promised':
        specifier: ^7.1.8
        version: 7.1.8
      '@types/chai-datetime':
        specifier: 1.0.0
        version: 1.0.0
      '@types/cls-hooked':
        specifier: ^4.2.0
        version: 4.3.9
      '@types/i18n':
        specifier: 0.13.12
        version: 0.13.12
      '@types/mocha':
        specifier: ^10.0.8
        version: 10.0.10
      '@types/node':
        specifier: 22.14.1
        version: 22.14.1
      '@types/pg':
        specifier: 8.11.11
        version: 8.11.11
      '@types/serve-static':
        specifier: 1.15.7
        version: 1.15.7
      '@types/sinon':
        specifier: 10.0.20
        version: 10.0.20
      '@types/sinon-chai':
        specifier: 3.2.12
        version: 3.2.12
      chai:
        specifier: ~4.3.10
        version: 4.3.10
      chai-as-promised:
        specifier: ^7.1.1
        version: 7.1.2(chai@4.3.10)
      chai-datetime:
        specifier: 1.8.1
        version: 1.8.1
      mocha:
        specifier: 10.7.3
        version: 10.7.3
      mocha-typescript:
        specifier: 1.1.12
        version: 1.1.12
      nyc:
        specifier: 15.1.0
        version: 15.1.0
      sinon:
        specifier: 16.1.3
        version: 16.1.3
      sinon-chai:
        specifier: 3.7.0
        version: 3.7.0(chai@4.3.10)(sinon@16.1.3)
      ts-node:
        specifier: ^10.7.0
        version: 10.9.2(@types/node@22.14.1)(typescript@5.6.3)
      tsconfig-paths:
        specifier: ^4.2.0
        version: 4.2.0
      typescript:
        specifier: 5.6.3
        version: 5.6.3

  packages/gameprovider-core:
    dependencies:
      '@skywind-group/sw-currency-exchange':
        specifier: 2.3.19
        version: 2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)
      '@skywind-group/sw-deferred-payment':
        specifier: ^2.0.0
        version: 2.1.0
      '@skywind-group/sw-game-provider-ext-game-history':
        specifier: ~3.1.0
        version: 3.1.10(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(bole@5.0.15)(ioredis@5.5.0)(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(pg@8.14.1)(sequelize@6.37.7(pg@8.14.1))(superagent-proxy@3.0.0(superagent@10.2.3))(superagent@10.2.3)
      '@skywind-group/sw-management-adapters':
        specifier: workspace:~2.141.0-develop
        version: link:../adapters
      '@skywind-group/sw-management-deferredpayment':
        specifier: workspace:~2.141.0-develop
        version: link:../deferredpayment
      '@skywind-group/sw-management-gameprovider':
        specifier: workspace:~2.141.0-develop
        version: link:../gameprovider
      '@skywind-group/sw-management-playersession':
        specifier: workspace:~2.141.0-develop
        version: link:../playersession
      '@skywind-group/sw-management-playservice':
        specifier: workspace:~2.141.0-develop
        version: link:../playservice
      '@skywind-group/sw-management-promo-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../promo-wallet
      '@skywind-group/sw-management-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../wallet
      '@skywind-group/sw-messaging':
        specifier: 0.2.4
        version: 0.2.4
      '@skywind-group/sw-wallet':
        specifier: 1.0.8
        version: 1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      jsonwebtoken:
        specifier: 9.0.2
        version: 9.0.2
      lodash:
        specifier: 4.17.21
        version: 4.17.21
      request:
        specifier: 2.88.0
        version: 2.88.0
    devDependencies:
      '@skywind-group/sw-adapter-regulation-support':
        specifier: ^1.0.2
        version: 1.0.2(generic-pool@3.9.0)(ioredis@5.5.0)
      '@types/chai-as-promised':
        specifier: ^7.1.8
        version: 7.1.8
      '@types/request':
        specifier: 2.48.12
        version: 2.48.12
      chai-exclude:
        specifier: 2.0.2
        version: 2.0.2(chai@4.3.10)
      emitter-listener:
        specifier: 1.1.2
        version: 1.1.2
      express-prom-bundle:
        specifier: 7.0.2
        version: 7.0.2(prom-client@15.0.0)
      inversify:
        specifier: 5.0.1
        version: 5.0.1
      inversify-express-utils:
        specifier: 6.3.2
        version: 6.3.2
      inversify-inject-decorators:
        specifier: 3.1.0
        version: 3.1.0
      ioredis:
        specifier: 5.5.0
        version: 5.5.0
      kafka-node:
        specifier: 5.0.0
        version: 5.0.0
      nyc:
        specifier: 15.1.0
        version: 15.1.0
      prom-client:
        specifier: 15.0.0
        version: 15.0.0
      sequelize:
        specifier: 6.37.7
        version: 6.37.7(pg@8.14.1)
      uuid:
        specifier: 9.0.1
        version: 9.0.1

  packages/i18n:
    dependencies:
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      i18n:
        specifier: 0.15.1
        version: 0.15.1
    devDependencies:
      '@types/i18n':
        specifier: 0.13.12
        version: 0.13.12

  packages/playersession:
    devDependencies:
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      ioredis:
        specifier: 5.5.0
        version: 5.5.0

  packages/playservice:
    dependencies:
      '@skywind-group/sw-currency-exchange':
        specifier: 2.3.19
        version: 2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)
      '@skywind-group/sw-deferred-payment':
        specifier: ^2.0.0
        version: 2.1.0
      '@skywind-group/sw-management-promo-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../promo-wallet
      '@skywind-group/sw-management-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../wallet
      '@skywind-group/sw-wallet':
        specifier: 1.0.8
        version: 1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      lodash:
        specifier: 4.17.21
        version: 4.17.21
      uuid:
        specifier: ^9.0.1
        version: 9.0.1

  packages/promo-wallet:
    dependencies:
      '@skywind-group/sw-currency-exchange':
        specifier: 2.3.19
        version: 2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)
      '@skywind-group/sw-management-wallet':
        specifier: workspace:~2.141.0-develop
        version: link:../wallet
      '@skywind-group/sw-wallet':
        specifier: 1.0.8
        version: 1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      uuid:
        specifier: ^9.0.1
        version: 9.0.1

  packages/wallet:
    dependencies:
      '@skywind-group/sw-currency-exchange':
        specifier: 2.3.19
        version: 2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)
      '@skywind-group/sw-wallet':
        specifier: 1.0.8
        version: 1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)
      '@skywind-group/sw-wallet-adapter-core':
        specifier: 2.1.9
        version: 2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))
      uuid:
        specifier: ^9.0.1
        version: 9.0.1

packages:

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.28.4':
    resolution: {integrity: sha512-YsmSKC29MJwf0gF8Rjjrg5LQCmyh+j/nD8/eP7f+BeoQTKYqs9RoWbjGOdy0+1Ekr68RJZMUOPVQaQisnIo4Rw==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.28.4':
    resolution: {integrity: sha512-2BCOP7TN8M+gVDj7/ht3hsaO/B/n5oDbiAyyvnRlNOs+u1o+JWNYTQrmpuNp1/Wq2gcFrI01JAW+paEKDMx/CA==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.28.3':
    resolution: {integrity: sha512-3lSpxGgvnmZznmBkCRnVREPUFJv2wrv9iAoFDvADJc0ypmdOxdUtcLeBgBJ6zE0PMeTKnxeQzyk0xTBq4Ep7zw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-globals@7.28.0':
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.28.3':
    resolution: {integrity: sha512-gytXUbs8k2sXS9PnQptz5o0QnpLL51SwASIORY6XaBKF88nsOT0Zw9szLqlSGQDP/4TljBAD5y98p2U1fqkdsw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.28.4':
    resolution: {integrity: sha512-HFN59MmQXGHVyYadKLVumYsA9dBFun/ldYxipEjzA4196jpLZd8UjEEBLkbEkvfYreDqJhZxYAWFPtrfhNpj4w==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.28.4':
    resolution: {integrity: sha512-yZbBqeM6TkpP9du/I2pUZnJsRMGGvOuIrhjzC1AwHwW+6he4mni6Bp/m8ijn0iOuZuPI2BfkCoSRunpyjnrQKg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/template@7.27.2':
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.28.4':
    resolution: {integrity: sha512-YEzuboP2qvQavAcjgQNVgsvHIDv6ZpwXvcvjmyySP2DIMuByS/6ioU5G9pYrWHM6T2YDfc7xga9iNzYOs12CFQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.4':
    resolution: {integrity: sha512-bkFqkLhh3pMBUQQkpVgWDWq/lqzc2678eUyDlTBhRqhCHFguYYGM0Efga7tYk4TogG/3x0EEl66/OQ+WGbWB/Q==}
    engines: {node: '>=6.9.0'}

  '@cspotcode/source-map-support@0.8.1':
    resolution: {integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==}
    engines: {node: '>=12'}

  '@elastic/elasticsearch@0.1.0-beta.2':
    resolution: {integrity: sha512-jSfXnXpxxGCkJ0gxIRObK0sHjGsvO4vDnZtOA0qkQ4gUUaH2fZ0OSn28AHw6DMqYZSLRXPqNYHE5Nj7hBe/2kQ==}
    engines: {node: '>=6'}

  '@emnapi/core@1.5.0':
    resolution: {integrity: sha512-sbP8GzB1WDzacS8fgNPpHlp6C9VZe+SJP3F90W9rLemaQj2PzIuTEl1qDOYQf58YIpyjViI24y9aPWCjEzY2cg==}

  '@emnapi/runtime@1.5.0':
    resolution: {integrity: sha512-97/BJ3iXHww3djw6hYIfErCZFee7qCtrneuLa20UXFCOTCfBM2cvQHjWJ2EG0s0MtdNwInarqCTz35i4wWXHsQ==}

  '@emnapi/wasi-threads@1.1.0':
    resolution: {integrity: sha512-WI0DdZ8xFSbgMjR1sFsKABJ/C5OnRrjT06JXbZKexJGrDuPTzZdDYfFlsgcCXCyf+suG5QU2e/y1Wo2V/OapLQ==}

  '@eslint-community/eslint-utils@4.9.0':
    resolution: {integrity: sha512-ayVFHdtZ+hsq1t2Dy24wCmGXGe4q9Gu3smhLYALJrr473ZH27MsnSL+LKUlimp4BWJqMDMLmPpx/Q9R3OAlL4g==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.21.0':
    resolution: {integrity: sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.3.1':
    resolution: {integrity: sha512-xR93k9WhrDYpXHORXpxVL5oHj3Era7wo6k/Wd8/IsQNnZUTzkGS29lyn3nAT05v6ltUuTFVCCYDEGfy2Or/sPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.13.0':
    resolution: {integrity: sha512-yfkgDw1KR66rkT5A8ci4irzDysN7FRpq3ttJolR88OqQikAWqwA8j5VZyas+vjyBNFIJ7MfybJ9plMILI2UrCw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.15.2':
    resolution: {integrity: sha512-78Md3/Rrxh83gCxoUc0EiciuOHsIITzLy53m3d9UyiW8y9Dj2D29FeETqyKA+BRK76tnTp6RXWb3pCay8Oyomg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.35.0':
    resolution: {integrity: sha512-30iXE9whjlILfWobBkNerJo+TXYsgVM5ERQwMcMKCHckHflCmf7wXDAHlARoWnh0s1U72WqlbeyE7iAcCzuCPw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.2.8':
    resolution: {integrity: sha512-ZAoA40rNMPwSm+AeHpCq8STiNAwzWLJuP8Xv4CHIc9wv/PSuExjMrmjfYNj682vW0OOiZ1HKxzvjQr9XZIisQA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.3.5':
    resolution: {integrity: sha512-Z5kJ+wU3oA7MMIqVR9tyZRtjYPr4OC004Q4Rw7pgOKUOKkJfZ3O24nz3WYfGRpMDNmcOi3TwQOmgm7B7Tpii0w==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@fastify/accept-negotiator@2.0.1':
    resolution: {integrity: sha512-/c/TW2bO/v9JeEgoD/g1G5GxGeCF1Hafdf79WPmUlgYiBXummY0oX3VVq4yFkKKVBKDNlaDUYoab7g38RpPqCQ==}

  '@fastify/ajv-compiler@4.0.2':
    resolution: {integrity: sha512-Rkiu/8wIjpsf46Rr+Fitd3HRP+VsxUFDDeag0hs9L0ksfnwx2g7SPQQTFL0E8Qv+rfXzQOxBJnjUB9ITUDjfWQ==}

  '@fastify/compress@8.0.1':
    resolution: {integrity: sha512-yWNfKhvL4orfN45LKCHCo8Fcsbj1kdNgwyShw2xpdHfzPf4A3MESmgSfUm3TCKQwgqDdrPnLfy1E+3I/DVP+BQ==}

  '@fastify/cookie@11.0.2':
    resolution: {integrity: sha512-GWdwdGlgJxyvNv+QcKiGNevSspMQXncjMZ1J8IvuDQk0jvkzgWWZFNC2En3s+nHndZBGV8IbLwOI/sxCZw/mzA==}

  '@fastify/error@4.2.0':
    resolution: {integrity: sha512-RSo3sVDXfHskiBZKBPRgnQTtIqpi/7zhJOEmAxCiBcM7d0uwdGdxLlsCaLzGs8v8NnxIRlfG0N51p5yFaOentQ==}

  '@fastify/fast-json-stringify-compiler@5.0.3':
    resolution: {integrity: sha512-uik7yYHkLr6fxd8hJSZ8c+xF4WafPK+XzneQDPU+D10r5X19GW8lJcom2YijX2+qtFF1ENJlHXKFM9ouXNJYgQ==}

  '@fastify/forwarded@3.0.0':
    resolution: {integrity: sha512-kJExsp4JCms7ipzg7SJ3y8DwmePaELHxKYtg+tZow+k0znUTf3cb+npgyqm8+ATZOdmfgfydIebPDWM172wfyA==}

  '@fastify/merge-json-schemas@0.2.1':
    resolution: {integrity: sha512-OA3KGBCy6KtIvLf8DINC5880o5iBlDX4SxzLQS8HorJAbqluzLRn80UXU0bxZn7UOFhFgpRJDasfwn9nG4FG4A==}

  '@fastify/middie@9.0.3':
    resolution: {integrity: sha512-7OYovKXp9UKYeVMcjcFLMcSpoMkmcZmfnG+eAvtdiatN35W7c+r9y1dRfpA+pfFVNuHGGqI3W+vDTmjvcfLcMA==}

  '@fastify/proxy-addr@5.0.0':
    resolution: {integrity: sha512-37qVVA1qZ5sgH7KpHkkC4z9SK6StIsIcOmpjvMPXNb3vx2GQxhZocogVYbr2PbbeLCQxYIPDok307xEvRZOzGA==}

  '@fastify/send@3.3.1':
    resolution: {integrity: sha512-6pofeVwaHN+E/MAofCwDqkWUliE3i++jlD0VH/LOfU8TJlCkMUSgKvA9bawDdVXxjve7XrdYMyDmkiYaoGWEtA==}

  '@fastify/static@8.1.1':
    resolution: {integrity: sha512-TW9eyVHJLytZNpBlSIqd0bl1giJkEaRaPZG+5AT3L/OBKq9U8D7g/OYmc2NPQZnzPURGhMt3IAWuyVkvd2nOkQ==}

  '@google-cloud/paginator@5.0.2':
    resolution: {integrity: sha512-DJS3s0OVH4zFDB1PzjxAsHqJT6sKVbRwwML0ZBP9PbU7Yebtu/7SWMRzvO2J3nUi9pRNITCfu4LJeooM2w4pjg==}
    engines: {node: '>=14.0.0'}

  '@google-cloud/projectify@4.0.0':
    resolution: {integrity: sha512-MmaX6HeSvyPbWGwFq7mXdo0uQZLGBYCwziiLIGq5JVX+/bdI3SAq6bP98trV5eTWfLuvsMcIC1YJOF2vfteLFA==}
    engines: {node: '>=14.0.0'}

  '@google-cloud/promisify@4.0.0':
    resolution: {integrity: sha512-Orxzlfb9c67A15cq2JQEyVc7wEsmFBmHjZWZYQMUyJ1qivXyMwdyNOs9odi79hze+2zqdTtu1E19IM/FtqZ10g==}
    engines: {node: '>=14'}

  '@google-cloud/storage@7.16.0':
    resolution: {integrity: sha512-7/5LRgykyOfQENcm6hDKP8SX/u9XxE5YOiWOkgkwcoO+cG8xT/cyOvp9wwN3IxfdYgpHs8CE7Nq2PKX2lNaEXw==}
    engines: {node: '>=14'}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.7':
    resolution: {integrity: sha512-/zUx+yOsIrG4Y43Eh2peDeKCxlRt/gET6aHfaKpuq267qXdYDFViVHfMaLyygZOnl0kGWxFIgsBy8QFuTLUXEQ==}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.4.3':
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==}
    engines: {node: '>=18.18'}

  '@hutson/parse-repository-url@3.0.2':
    resolution: {integrity: sha512-H9XAx3hc0BQHY6l+IFSWHDySypcXsvsuLhgYLUGywmJ5pswRVQJUHpOsobnLYp2ZUaUlKiKDrgWWhosOwAEM8Q==}
    engines: {node: '>=6.9.0'}

  '@inquirer/external-editor@1.0.1':
    resolution: {integrity: sha512-Oau4yL24d2B5IL4ma4UpbQigkVhzPDXLoqy1ggK4gnHg/stmkffJE4oOXHXF3uz0UEpywG68KcyXsyYpA1Re/Q==}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/node': '>=18'
    peerDependenciesMeta:
      '@types/node':
        optional: true

  '@ioredis/commands@1.3.1':
    resolution: {integrity: sha512-bYtU8avhGIcje3IhvF9aSjsa5URMZBHnwKtOvXsT4sfYy9gppW11gLPT/9oNqlJZD47yPKveQFTAFWpHjKvUoQ==}

  '@isaacs/balanced-match@4.0.1':
    resolution: {integrity: sha512-yzMTt9lEb8Gv7zRioUilSglI0c0smZ9k5D65677DLWLtWJaXIS3CqcGyUFByYKlnUj6TkjLVs54fBl6+TiGQDQ==}
    engines: {node: 20 || >=22}

  '@isaacs/brace-expansion@5.0.0':
    resolution: {integrity: sha512-ZT55BDLV0yv0RBm2czMiZ+SqCGO7AvmOM3G/w2xhVPH+te0aKgFjmBvGlL1dH+ql2tgGO3MVrbb3jCKyvpgnxA==}
    engines: {node: 20 || >=22}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@isaacs/string-locale-compare@1.1.0':
    resolution: {integrity: sha512-SQ7Kzhh9+D+ZW9MA0zkYv3VXhIDNx+LzM6EJ+/65I3QY+enU6Itte7E5XX7EWrqLW2FN4n06GWzBnPoC3th2aQ==}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}

  '@jest/schemas@29.6.3':
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jridgewell/gen-mapping@0.3.13':
    resolution: {integrity: sha512-2kkt/7niJ6MgEPxF0bYdQ6etZaA+fQvDcLKckhy1yIQOzaoKjBBjSj63/aLVjYE3qhRt5dvM+uUyfCg6UKCBbA==}

  '@jridgewell/remapping@2.3.5':
    resolution: {integrity: sha512-LI9u/+laYG4Ds1TDKSJW2YPrIlcVYOwi2fUC6xB43lueCjgxV4lffOCZCtYFiH6TNOX+tQKXx97T4IKHbhyHEQ==}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.5':
    resolution: {integrity: sha512-cYQ9310grqxueWbl+WuIUIaiUaDcj7WOq5fVhEljNVgRfOUhY9fy2zTvfoqWsnebh8Sl70VScFbICvJnLKB0Og==}

  '@jridgewell/trace-mapping@0.3.30':
    resolution: {integrity: sha512-GQ7Nw5G2lTu/BtHTKfXhKHok2WGetd4XYcVKGx00SjAk8GMwgJM3zr6zORiPGuOE+/vkc90KtTosSSvaCjKb2Q==}

  '@jridgewell/trace-mapping@0.3.9':
    resolution: {integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==}

  '@lerna/create@8.2.2':
    resolution: {integrity: sha512-1yn1MvWn2Yz0SFgTTQnef2m1YedF7KwqLLVIOrGkgQrkVHzsveAIk1A1RcRa2yyUh+siKI1YcJ7lUZIEt+qQ3Q==}
    engines: {node: '>=18.0.0'}

  '@lukeed/ms@2.0.2':
    resolution: {integrity: sha512-9I2Zn6+NJLfaGoz9jN3lpwDgAYvfGeNYdbAIjJOqzs4Tpc+VU3Jqq4IofSUBKajiDS8k9fZIg18/z13mpk1bsA==}
    engines: {node: '>=8'}

  '@messageformat/core@3.4.0':
    resolution: {integrity: sha512-NgCFubFFIdMWJGN5WuQhHCNmzk7QgiVfrViFxcS99j7F5dDS5EP6raR54I+2ydhe4+5/XTn/YIEppFaqqVWHsw==}

  '@messageformat/date-skeleton@1.1.0':
    resolution: {integrity: sha512-rmGAfB1tIPER+gh3p/RgA+PVeRE/gxuQ2w4snFWPF5xtb5mbWR7Cbw7wCOftcUypbD6HVoxrVdyyghPm3WzP5A==}

  '@messageformat/number-skeleton@1.2.0':
    resolution: {integrity: sha512-xsgwcL7J7WhlHJ3RNbaVgssaIwcEyFkBqxHdcdaiJzwTZAWEOD8BuUFxnxV9k5S0qHN3v/KzUpq0IUpjH1seRg==}

  '@messageformat/parser@5.1.1':
    resolution: {integrity: sha512-3p0YRGCcTUCYvBKLIxtDDyrJ0YijGIwrTRu1DT8gIviIDZru8H23+FkY6MJBzM1n9n20CiM4VeDYuBsrrwnLjg==}

  '@messageformat/runtime@3.0.1':
    resolution: {integrity: sha512-6RU5ol2lDtO8bD9Yxe6CZkl0DArdv0qkuoZC+ZwowU+cdRlVE1157wjCmlA5Rsf1Xc/brACnsZa5PZpEDfTFFg==}

  '@napi-rs/wasm-runtime@0.2.4':
    resolution: {integrity: sha512-9zESzOO5aDByvhIAsOy9TbpZ0Ur2AJbUI7UT73kcUTS2mxAMHOBaa1st/jAymNoCtvrit99kkzT1FZuXVcgfIQ==}

  '@noble/hashes@1.8.0':
    resolution: {integrity: sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A==}
    engines: {node: ^14.21.3 || >=16}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@npmcli/agent@2.2.2':
    resolution: {integrity: sha512-OrcNPXdpSl9UX7qPVRWbmWMCSXrcDa2M9DvrbOTj7ao1S4PlqVFYv9/yLKMkrJKZ/V5A/kDBC690or307i26Og==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@npmcli/arborist@7.5.4':
    resolution: {integrity: sha512-nWtIc6QwwoUORCRNzKx4ypHqCk3drI+5aeYdMTQQiRCcn4lOOgfQh7WyZobGYTxXPSq1VwV53lkpN/BRlRk08g==}
    engines: {node: ^16.14.0 || >=18.0.0}
    hasBin: true

  '@npmcli/fs@3.1.1':
    resolution: {integrity: sha512-q9CRWjpHCMIh5sVyefoD1cA7PkvILqCZsnSOEUUivORLjxCO/Irmue2DprETiNgEqktDBZaM1Bi+jrarx1XdCg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/git@5.0.8':
    resolution: {integrity: sha512-liASfw5cqhjNW9UFd+ruwwdEf/lbOAQjLL2XY2dFW/bkJheXDYZgOyul/4gVvEV4BWkTXjYGmDqMw9uegdbJNQ==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@npmcli/installed-package-contents@2.1.0':
    resolution: {integrity: sha512-c8UuGLeZpm69BryRykLuKRyKFZYJsZSCT4aVY5ds4omyZqJ172ApzgfKJ5eV/r3HgLdUYgFVe54KSFVjKoe27w==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  '@npmcli/map-workspaces@3.0.6':
    resolution: {integrity: sha512-tkYs0OYnzQm6iIRdfy+LcLBjcKuQCeE5YLb8KnrIlutJfheNaPvPpgoFEyEFgbjzl5PLZ3IA/BWAwRU0eHuQDA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/metavuln-calculator@7.1.1':
    resolution: {integrity: sha512-Nkxf96V0lAx3HCpVda7Vw4P23RILgdi/5K1fmj2tZkWIYLpXAN8k2UVVOsW16TsS5F8Ws2I7Cm+PU1/rsVF47g==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@npmcli/name-from-folder@2.0.0':
    resolution: {integrity: sha512-pwK+BfEBZJbKdNYpHHRTNBwBoqrN/iIMO0AiGvYsp3Hoaq0WbgGSWQR6SCldZovoDpY3yje5lkFUe6gsDgJ2vg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/node-gyp@3.0.0':
    resolution: {integrity: sha512-gp8pRXC2oOxu0DUE1/M3bYtb1b3/DbJ5aM113+XJBgfXdussRAsX0YOrOhdd8WvnAR6auDBvJomGAkLKA5ydxA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/package-json@5.2.0':
    resolution: {integrity: sha512-qe/kiqqkW0AGtvBjL8TJKZk/eBBSpnJkUWvHdQ9jM2lKHXRYYJuyNpJPlJw3c8QjC2ow6NZYiLExhUaeJelbxQ==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@npmcli/promise-spawn@7.0.2':
    resolution: {integrity: sha512-xhfYPXoV5Dy4UkY0D+v2KkwvnDfiA/8Mt3sWCGI/hM03NsYIH8ZaG6QzS9x7pje5vHZBZJ2v6VRFVTWACnqcmQ==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@npmcli/query@3.1.0':
    resolution: {integrity: sha512-C/iR0tk7KSKGldibYIB9x8GtO/0Bd0I2mhOaDb8ucQL/bQVTmGoeREaFj64Z5+iCBRf3dQfed0CjJL7I8iTkiQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/redact@2.0.1':
    resolution: {integrity: sha512-YgsR5jCQZhVmTJvjduTOIHph0L73pK8xwMVaDY0PatySqVM9AZj93jpoXYSJqfHFxFkN9dmqTw6OiqExsS3LPw==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@npmcli/run-script@8.1.0':
    resolution: {integrity: sha512-y7efHHwghQfk28G2z3tlZ67pLG0XdfYbcVG26r7YIXALRsrVQcTq4/tdenSmdOrEsNahIYA/eh8aEVROWGFUDg==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@nx/devkit@20.8.2':
    resolution: {integrity: sha512-rr9p2/tZDQivIpuBUpZaFBK6bZ+b5SAjZk75V4tbCUqGW3+5OPuVvBPm+X+7PYwUF6rwSpewxkjWNeGskfCe+Q==}
    peerDependencies:
      nx: '>= 19 <= 21'

  '@nx/nx-darwin-arm64@20.8.2':
    resolution: {integrity: sha512-t+bmCn6sRPNGU6hnSyWNvbQYA/KgsxGZKYlaCLRwkNhI2akModcBUqtktJzCKd1XHDqs6EkEFBWjFr8/kBEkSg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@nx/nx-darwin-x64@20.8.2':
    resolution: {integrity: sha512-pt/wmDLM31Es8/EzazlyT5U+ou2l60rfMNFGCLqleHEQ0JUTc0KWnOciBLbHIQFiPsCQZJFEKyfV5V/ncePmmw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@nx/nx-freebsd-x64@20.8.2':
    resolution: {integrity: sha512-joZxFbgJfkHkB9uMIJr73Gpnm9pnpvr0XKGbWC409/d2x7q1qK77tKdyhGm+A3+kaZFwstNVPmCUtUwJYyU6LA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@nx/nx-linux-arm-gnueabihf@20.8.2':
    resolution: {integrity: sha512-98O/qsxn4vIMPY/FyzvmVrl7C5yFhCUVk0/4PF+PA2SvtQ051L1eMRY6bq/lb69qfN6szJPZ41PG5mPx0NeLZw==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@nx/nx-linux-arm64-gnu@20.8.2':
    resolution: {integrity: sha512-h6a+HxwfSpxsi4KpxGgPh9GDBmD2E+XqGCdfYpobabxqEBvlnIlJyuDhlRR06cTWpuNXHpRdrVogmV6m/YbtDg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@nx/nx-linux-arm64-musl@20.8.2':
    resolution: {integrity: sha512-4Ev+jM0VAxDHV/dFgMXjQTCXS4I8W4oMe7FSkXpG8RUn6JK659DC8ExIDPoGIh+Cyqq6r6mw1CSia+ciQWICWQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@nx/nx-linux-x64-gnu@20.8.2':
    resolution: {integrity: sha512-nR0ev+wxu+nQYRd7bhqggOxK7UfkV6h+Ko1mumUFyrM5GvPpz/ELhjJFSnMcOkOMcvH0b6G5uTBJvN1XWCkbmg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@nx/nx-linux-x64-musl@20.8.2':
    resolution: {integrity: sha512-ost41l5yc2aq2Gc9bMMpaPi/jkXqbXEMEPHrxWKuKmaek3K2zbVDQzvBBNcQKxf/mlCsrqN4QO0mKYSRRqag5A==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@nx/nx-win32-arm64-msvc@20.8.2':
    resolution: {integrity: sha512-0SEOqT/daBG5WtM9vOGilrYaAuf1tiALdrFavY62+/arXYxXemUKmRI5qoKDTnvoLMBGkJs6kxhMO5b7aUXIvQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@nx/nx-win32-x64-msvc@20.8.2':
    resolution: {integrity: sha512-iIsY+tVqes/NOqTbJmggL9Juie/iaDYlWgXA9IUv88FE9thqWKhVj4/tCcPjsOwzD+1SVna3YISEEFsx5UV4ew==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@octokit/auth-token@4.0.0':
    resolution: {integrity: sha512-tY/msAuJo6ARbK6SPIxZrPBms3xPbfwBrulZe0Wtr/DIY9lje2HeV1uoebShn6mx7SjCHif6EjMvoREj+gZ+SA==}
    engines: {node: '>= 18'}

  '@octokit/core@5.2.2':
    resolution: {integrity: sha512-/g2d4sW9nUDJOMz3mabVQvOGhVa4e/BN/Um7yca9Bb2XTzPPnfTWHWQg+IsEYO7M3Vx+EXvaM/I2pJWIMun1bg==}
    engines: {node: '>= 18'}

  '@octokit/endpoint@9.0.6':
    resolution: {integrity: sha512-H1fNTMA57HbkFESSt3Y9+FBICv+0jFceJFPWDePYlR/iMGrwM5ph+Dd4XRQs+8X+PUFURLQgX9ChPfhJ/1uNQw==}
    engines: {node: '>= 18'}

  '@octokit/graphql@7.1.1':
    resolution: {integrity: sha512-3mkDltSfcDUoa176nlGoA32RGjeWjl3K7F/BwHwRMJUW/IteSa4bnSV8p2ThNkcIcZU2umkZWxwETSSCJf2Q7g==}
    engines: {node: '>= 18'}

  '@octokit/openapi-types@24.2.0':
    resolution: {integrity: sha512-9sIH3nSUttelJSXUrmGzl7QUBFul0/mB8HRYl3fOlgHbIWG+WnYDXU3v/2zMtAvuzZ/ed00Ei6on975FhBfzrg==}

  '@octokit/plugin-enterprise-rest@6.0.1':
    resolution: {integrity: sha512-93uGjlhUD+iNg1iWhUENAtJata6w5nE+V4urXOAlIXdco6xNZtUSfYY8dzp3Udy74aqO/B5UZL80x/YMa5PKRw==}

  '@octokit/plugin-paginate-rest@11.4.4-cjs.2':
    resolution: {integrity: sha512-2dK6z8fhs8lla5PaOTgqfCGBxgAv/le+EhPs27KklPhm1bKObpu6lXzwfUEQ16ajXzqNrKMujsFyo9K2eaoISw==}
    engines: {node: '>= 18'}
    peerDependencies:
      '@octokit/core': '5'

  '@octokit/plugin-request-log@4.0.1':
    resolution: {integrity: sha512-GihNqNpGHorUrO7Qa9JbAl0dbLnqJVrV8OXe2Zm5/Y4wFkZQDfTreBzVmiRfJVfE4mClXdihHnbpyyO9FSX4HA==}
    engines: {node: '>= 18'}
    peerDependencies:
      '@octokit/core': '5'

  '@octokit/plugin-rest-endpoint-methods@13.3.2-cjs.1':
    resolution: {integrity: sha512-VUjIjOOvF2oELQmiFpWA1aOPdawpyaCUqcEBc/UOUnj3Xp6DJGrJ1+bjUIIDzdHjnFNO6q57ODMfdEZnoBkCwQ==}
    engines: {node: '>= 18'}
    peerDependencies:
      '@octokit/core': ^5

  '@octokit/request-error@5.1.1':
    resolution: {integrity: sha512-v9iyEQJH6ZntoENr9/yXxjuezh4My67CBSu9r6Ve/05Iu5gNgnisNWOsoJHTP6k0Rr0+HQIpnH+kyammu90q/g==}
    engines: {node: '>= 18'}

  '@octokit/request@8.4.1':
    resolution: {integrity: sha512-qnB2+SY3hkCmBxZsR/MPCybNmbJe4KAlfWErXq+rBKkQJlbjdJeS85VI9r8UqeLYLvnAenU8Q1okM/0MBsAGXw==}
    engines: {node: '>= 18'}

  '@octokit/rest@20.1.2':
    resolution: {integrity: sha512-GmYiltypkHHtihFwPRxlaorG5R9VAHuk/vbszVoRTGXnAsY60wYLkh/E2XiFmdZmqrisw+9FaazS1i5SbdWYgA==}
    engines: {node: '>= 18'}

  '@octokit/types@13.10.0':
    resolution: {integrity: sha512-ifLaO34EbbPj0Xgro4G5lP5asESjwHracYJvVaPIyXMuiuXLlhic3S47cBdTb+jfODkTE5YtGCLt3Ay3+J97sA==}

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==}
    engines: {node: '>=8.0.0'}

  '@paralleldrive/cuid2@2.2.2':
    resolution: {integrity: sha512-ZOBkgDwEdoYVlSeRbYYXs0S9MejQofiVYoTbKzy/6GQa39/q5tQU2IX46+shYnUkpEl3wc+J6wRlar7r2EK2xA==}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@sigstore/bundle@2.3.2':
    resolution: {integrity: sha512-wueKWDk70QixNLB363yHc2D2ItTgYiMTdPwK8D9dKQMR3ZQ0c35IxP5xnwQ8cNLoCgCRcHf14kE+CLIvNX1zmA==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@sigstore/core@1.1.0':
    resolution: {integrity: sha512-JzBqdVIyqm2FRQCulY6nbQzMpJJpSiJ8XXWMhtOX9eKgaXXpfNOF53lzQEjIydlStnd/eFtuC1dW4VYdD93oRg==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@sigstore/protobuf-specs@0.3.3':
    resolution: {integrity: sha512-RpacQhBlwpBWd7KEJsRKcBQalbV28fvkxwTOJIqhIuDysMMaJW47V4OqW30iJB9uRpqOSxxEAQFdr8tTattReQ==}
    engines: {node: ^18.17.0 || >=20.5.0}

  '@sigstore/sign@2.3.2':
    resolution: {integrity: sha512-5Vz5dPVuunIIvC5vBb0APwo7qKA4G9yM48kPWJT+OEERs40md5GoUR1yedwpekWZ4m0Hhw44m6zU+ObsON+iDA==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@sigstore/tuf@2.3.4':
    resolution: {integrity: sha512-44vtsveTPUpqhm9NCrbU8CWLe3Vck2HO1PNLw7RIajbB7xhtn5RBPm1VNSCMwqGYHhDsBJG8gDF0q4lgydsJvw==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@sigstore/verify@1.2.1':
    resolution: {integrity: sha512-8iKx79/F73DKbGfRf7+t4dqrc0bRr0thdPrxAtCKWRm/F0tG71i6O1rvlnScncJLLBZHn3h8M3c1BSUAb9yu8g==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@sinclair/typebox@0.27.8':
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==}

  '@sinonjs/commons@3.0.1':
    resolution: {integrity: sha512-K3mCHKQ9sVh8o1C9**********************************************************************==}

  '@sinonjs/fake-timers@10.3.0':
    resolution: {integrity: sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==}

  '@sinonjs/fake-timers@11.3.1':
    resolution: {integrity: sha512-EVJO7nW5M/F5Tur0Rf2z/QoMo+1Ia963RiMtapiQrEWvY0iBUvADo8Beegwjpnle5BHkyHuoxSTW3jF43H1XRA==}

  '@sinonjs/samsam@8.0.3':
    resolution: {integrity: sha512-hw6HbX+GyVZzmaYNh82Ecj1vdGZrqVIn/keDTg63IgAwiQPO+xCz99uG6Woqgb4tM0mUiFENKZ4cqd7IX94AXQ==}

  '@sinonjs/text-encoding@0.7.3':
    resolution: {integrity: sha512-DE427ROAphMQzU4ENbliGYrBSYPXF+TtLg9S8vzeA+OF4ZKzoDdzfL8sxuMUGS/lgRhM6j1URSk9ghf7Xo1tyA==}

  '@skywind-group/gelf-stream@1.2.6':
    resolution: {integrity: sha1-7MDPKIKHnJrTUApLqU5f/idmgNI=, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/gelf-stream/-/@skywind-group/gelf-stream-1.2.6.tgz}

  '@skywind-group/sw-adapter-core@2.0.0':
    resolution: {integrity: sha512-****************************************************************/Amo+St70d2aQMJip0lOqQ==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-adapter-core/-/@skywind-group/sw-adapter-core-2.0.0.tgz}
    engines: {node: '>= 14.17.0'}
    peerDependencies:
      '@skywind-group/sw-utils': 1 || 2
      bole: '5'
      jsonwebtoken: ^9.0.2
      pg: ^8.13.0
      sequelize: 4 || 6

  '@skywind-group/sw-adapter-regulation-support@1.0.2':
    resolution: {integrity: sha512-vknlvx31NRsi+LnEBdA1uPclJRzXXokfpSMROFAHU+yG9y+tNxfev2wAPDg/TS8cY3yxu54vEXEJ3C0V29sMUg==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-adapter-regulation-support/-/@skywind-group/sw-adapter-regulation-support-1.0.2.tgz}
    engines: {node: '>=20'}
    peerDependencies:
      generic-pool: ^3.9.0
      ioredis: ^5.3.2
    peerDependenciesMeta:
      generic-pool:
        optional: true
      ioredis:
        optional: true

  '@skywind-group/sw-currency-exchange@2.3.19':
    resolution: {integrity: sha512-g8y0Mz/1IFICtOU9SQ3qOMCISvIRqjB7uUtZq/7ty016QQ4GcQzNC6P8VkhzoQ2q44LnQhroG/4ZnIMGPVrfEw==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-currency-exchange/-/@skywind-group/sw-currency-exchange-2.3.19.tgz}
    peerDependencies:
      jsonwebtoken: ^9.0.2
      node-schedule: ^2.1.1
      superagent: ^8.1.2 || ^9.0.2 || ^10.0.0

  '@skywind-group/sw-deferred-payment-cache@2.2.0':
    resolution: {integrity: sha512-jDmo+629ENxsZZEUTtpNlMMWK5+CIdteRMuzCi+MnapQXtgZ0virNCnvVAOSVBh3oR+RS6CUNNfxi15NhtOgWA==}

  '@skywind-group/sw-deferred-payment-client@2.2.0':
    resolution: {integrity: sha512-75kH2Vjo3vnqxTJ0wT5KRuTnF1u4YWiGvO9VTA2sCbJP6jvhorcV0gZ7jKNXqREPiOx1zjdGjuYtLdO1btDT/w==}
    peerDependencies:
      superagent: 8 || 9 || 10

  '@skywind-group/sw-deferred-payment@2.1.0':
    resolution: {integrity: sha512-nUzHJM6vkwC3fe7TLFLeETktdm/9YhVhVTkTl2EpqbfZMUZV9HgzH/qjK614104vfjz6cwKsGFA3jwoQTI8n6A==}

  '@skywind-group/sw-domain-routing@3.1.0':
    resolution: {integrity: sha512-UaEVoF/i6WEhIQH3dGZRzw8lVAhdH0O8n5O0HbAKnYLcdLkHCIutBTy20hXr+EeHhJmS1/L/dRXY5KPOtmMPtw==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-domain-routing/-/@skywind-group/sw-domain-routing-3.1.0.tgz}
    peerDependencies:
      '@skywind-group/sw-utils': ^1.0.29 || ^2.2.0

  '@skywind-group/sw-falcon-oauth@1.2.2':
    resolution: {integrity: sha512-Sapn0Fa43LcqStkH/*********************************************************************==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-falcon-oauth/-/@skywind-group/sw-falcon-oauth-1.2.2.tgz}
    peerDependencies:
      '@skywind-group/sw-utils': 1 || 2

  '@skywind-group/sw-game-provider-ext-game-history@3.1.10':
    resolution: {integrity: sha512-ET0xJXNuV4lAJO39GTn0pEPzKwxMwb1or7KXYkADGAW+TqtpJywcInPDEQK3ofy6lq2ICrkgaWjlBQ1bCLL5Yg==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-game-provider-ext-game-history/-/@skywind-group/sw-game-provider-ext-game-history-3.1.10.tgz}
    peerDependencies:
      '@skywind-group/sw-utils': 1 || 2
      ioredis: '5'
      jsonwebtoken: '9'
      node-schedule: ^2.1.1
      pg: 6.2.4 || ^8.3.0
      sequelize: 3.30.4 || 4 || 6
      superagent: ^10.0.0
      superagent-proxy: 3.0.0

  '@skywind-group/sw-gameprovider-adapter-core@1.3.2':
    resolution: {integrity: sha512-bpfK9JbNaRSYCPWzv2m6SWg8HlNUYR17jfLi88j34jQWOMTJGJVpatgOGDfaPR+MNSSQJEJ2+7bazF/qQg3s3A==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-gameprovider-adapter-core/-/@skywind-group/sw-gameprovider-adapter-core-1.3.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      agentkeepalive: ^3.5.2 || ^4.0.0
      superagent-proxy: 3.0.0

  '@skywind-group/sw-live-core@2.0.6':
    resolution: {integrity: sha512-pCJ/KtT1bW9XOrD7JUysKQ8WKXfzQGGcoTOuVblB+fWsXhtB0EbY642Yr/I+4VVy78Ng0/k/Cd/pckmDHiJuEA==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-live-core/-/@skywind-group/sw-live-core-2.0.6.tgz}
    engines: {node: '>= 14.17.0'}

  '@skywind-group/sw-messaging@0.2.4':
    resolution: {integrity: sha1-CggDQ20FdTdhStzI3+pEpGS0l7Q=, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-messaging/-/@skywind-group/sw-messaging-0.2.4.tgz}

  '@skywind-group/sw-pop-notification@0.1.0':
    resolution: {integrity: sha1-DEadn7/IZ6WyAOz7+i5QKpAgXTo=, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-pop-notification/-/@skywind-group/sw-pop-notification-0.1.0.tgz}

  '@skywind-group/sw-round-details-report@1.1.2':
    resolution: {integrity: sha512-KzMg1pdXHwg/JLsj+O19fUBXx9wj50jRY0G6BRmzEa8+ADwNubW1RCwlM6CjTnzFAvJXickAdkL3XL3XBgKQeQ==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-round-details-report/-/@skywind-group/sw-round-details-report-1.1.2.tgz}

  '@skywind-group/sw-sm-result-builder@0.1.67':
    resolution: {integrity: sha512-jtnTAD2l4PbrJedM0OwDpChluNcm5OLioVmNYSYjaUqZ6KpeFDiebdK4ycjOF+T5aaHFS4BtIauSYd97mgcUjg==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-sm-result-builder/-/@skywind-group/sw-sm-result-builder-0.1.67.tgz}

  '@skywind-group/sw-utils@2.5.3':
    resolution: {integrity: sha512-7sAfZC30cHi2C66rnvCZWnHKHXFtt9exzlXKQYBmJuuf4YHU7UiUiLeOnD7vu/AWZl1mZPyPeaBf25ixx1g49Q==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-utils/-/@skywind-group/sw-utils-2.5.3.tgz}
    engines: {node: '>=14.17'}
    peerDependencies:
      '@skywind-group/gelf-stream': 1.2.6
      agentkeepalive: ^4.5.0
      crc: ^4.3.2
      emitter-listener: ^1.1.2
      express-prom-bundle: ^6.6.0 || ^7.0.0 || 8
      generic-pool: ^3.9.0
      hashids: ^2.3.0
      ioredis: ^5.4.1
      js-big-integer: 1.0.2
      jsonwebtoken: ^9.0.2
      kafka-node: 5.0.0
      kafkajs: ^2.2.4
      measured-core: ^2.0.0
      prom-client: ^14.2.0 || ~15.0.0
      uuid: ^9.0.1
    peerDependenciesMeta:
      '@skywind-group/gelf-stream':
        optional: true
      agentkeepalive:
        optional: true
      crc:
        optional: true
      emitter-listener:
        optional: true
      express-prom-bundle:
        optional: true
      generic-pool:
        optional: true
      hashids:
        optional: true
      ioredis:
        optional: true
      js-big-integer:
        optional: true
      jsonwebtoken:
        optional: true
      kafka-node:
        optional: true
      kafkajs:
        optional: true
      measured-core:
        optional: true
      prom-client:
        optional: true
      uuid:
        optional: true

  '@skywind-group/sw-wallet-adapter-core@2.1.9':
    resolution: {integrity: sha512-beoHsV4SNO/FuWgcZZezjB7msqbZuHFv8di7ojKFu8e1GbM4idy0U/FutMZU5SD3mOCdR2Tj4QDtfzEIc5kLiA==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-wallet-adapter-core/-/@skywind-group/sw-wallet-adapter-core-2.1.9.tgz}
    engines: {node: '>=14'}
    peerDependencies:
      '@skywind-group/sw-deferred-payment': 1 || 2
      '@skywind-group/sw-round-details-report': 1 || 2
      '@skywind-group/sw-utils': ^2.3.0
      fast-xml-parser: 4.4.1
      superagent-proxy: 3.0.0

  '@skywind-group/sw-wallet@1.0.8':
    resolution: {integrity: sha512-Och4vHU+fV54nDhhmuD4lvJiRfudln7lmpjkx0gNnzZwlGXBEItWJ+Ll6EYrYnNRO3GCysAQaR1IsaVaBaiK0g==, tarball: https://swg.jfrog.io/swg/api/npm/npm/@skywind-group/sw-wallet/-/@skywind-group/sw-wallet-1.0.8.tgz}
    engines: {node: '>=14.17'}
    peerDependencies:
      '@skywind-group/sw-utils': ^2.3.3
      agentkeepalive: ^4.5.0
      crc: ^4.3.2
      ioredis: ^5.5.0
      js-big-integer: 1.0.2
      jsonwebtoken: ^9.0.2
      kafka-node: 5.0.0
      node-schedule: 2.1.1
      superagent: ^9.0.2 || ^10.0.0
      uuid: ^9.0.1

  '@socket.io/component-emitter@3.1.2':
    resolution: {integrity: sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==}

  '@tootallnate/once@1.1.2':
    resolution: {integrity: sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==}
    engines: {node: '>= 6'}

  '@tootallnate/once@2.0.0':
    resolution: {integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==}
    engines: {node: '>= 10'}

  '@tsconfig/node10@1.0.11':
    resolution: {integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==}

  '@tsconfig/node12@1.0.11':
    resolution: {integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==}

  '@tsconfig/node14@1.0.3':
    resolution: {integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==}

  '@tsconfig/node16@1.0.4':
    resolution: {integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==}

  '@tufjs/canonical-json@2.0.0':
    resolution: {integrity: sha512-yVtV8zsdo8qFHe+/3kw81dSLyF7D576A5cCFCi4X7B39tWT7SekaEFUnvnWJHz+9qO7qJTah1JbrDjWKqFtdWA==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@tufjs/models@2.0.1':
    resolution: {integrity: sha512-92F7/SFyufn4DXsha9+QfKnN03JGqtMFMXgSHbZOo8JG59WkTni7UzAouNQDf7AuP9OAMxVOPQcqG3sB7w+kkg==}
    engines: {node: ^16.14.0 || >=18.0.0}

  '@tybys/wasm-util@0.9.0':
    resolution: {integrity: sha512-6+7nlbMVX/PVDCwaIQ8nTOPveOcFLSt8GcXdx8hD0bt39uWxYT88uXzqTd4fTvqta7oeUJqudepapKNt2DYJFw==}

  '@types/body-parser@1.19.6':
    resolution: {integrity: sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==}

  '@types/caseless@0.12.5':
    resolution: {integrity: sha512-hWtVTC2q7hc7xZ/RLbxapMvDMgUnDvKvMOpKal4DrMyfGBUfB1oKaZlIRr6mJL+If3bAP6sV/QneGzF6tJjZDg==}

  '@types/chai-as-promised@7.1.8':
    resolution: {integrity: sha512-ThlRVIJhr69FLlh6IctTXFkmhtP3NpMZ2QGq69StYLyKZFp/HOp1VdKZj7RvfNWYYcJ1xlbLGLLWj1UvP5u/Gw==}

  '@types/chai-datetime@1.0.0':
    resolution: {integrity: sha512-YS6UUQj0dDyxTCc9EkgjaQ9qbBuvm5c2/aRRsfGy1v0KufWC0AGJGUKeypIL1TApUG3pKNLMaVa0RMBabDHrhg==}

  '@types/chai@4.3.20':
    resolution: {integrity: sha512-/pC9HAB5I/xMlc5FP77qjCnI16ChlJfW0tGa0IUcFn38VJrTV6DeZ60NU5KZBtaOZqjdpwTWohz5HU1RrhiYxQ==}

  '@types/cls-hooked@4.3.9':
    resolution: {integrity: sha512-CMtHMz6Q/dkfcHarq9nioXH8BDPP+v5xvd+N90lBQ2bdmu06UvnLDqxTKoOJzz4SzIwb/x9i4UXGAAcnUDuIvg==}

  '@types/connect@3.4.38':
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==}

  '@types/cookiejar@2.1.5':
    resolution: {integrity: sha512-he+DHOWReW0nghN24E1WUqM0efK4kI9oTqDm6XmK8ZPe2djZ90BSNdGnIyCLzCPw7/pogPlGbzI2wHGGmi4O/Q==}

  '@types/cors@2.8.19':
    resolution: {integrity: sha512-mFNylyeyqN93lfe/9CSxOGREz8cpzAhH+E93xJ4xWQf62V8sQ/24reV2nyzUWM6H6Xji+GGHpkbLe7pVoUEskg==}

  '@types/debug@4.1.12':
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/express-http-proxy@1.6.6':
    resolution: {integrity: sha512-J8ZqHG76rq1UB716IZ3RCmUhg406pbWxsM3oFCFccl5xlWUPzoR4if6Og/cE4juK8emH0H9quZa5ltn6ZdmQJg==}

  '@types/express-serve-static-core@4.0.48':
    resolution: {integrity: sha512-+W+fHO/hUI6JX36H8FlgdMHU3Dk4a/Fn08fW5qdd7MjPP/wJlzq9fkCrgaH0gES8vohVeqwefHwPa4ylVKyYIg==}

  '@types/express-serve-static-core@4.19.6':
    resolution: {integrity: sha512-N4LZ2xG7DatVqhCZzOGb1Yi5lMbXSZcmdLDe9EzSndPV2HpWYWzRbaerl2n27irrm94EPpprqa8KpskPT085+A==}

  '@types/express-validator@2.20.33':
    resolution: {integrity: sha512-dAlxnuNhKkM/Xq2148NySrnutOSeK8Xk6rpDdDrNT5akAKqFbtLmMKENaMYYh4xWcSqtSWc5l+TLxnZosa9p/g==}

  '@types/express@4.0.35':
    resolution: {integrity: sha512-B1BCEmQLo4Z5VM6SZhmHkYA6z8z8M2v+Wq3mySX7rZVhb0dySOzqbyaCV6CZI1NZkb0PO7Zr0TkVQCfXqOZ9ww==}

  '@types/express@4.17.23':
    resolution: {integrity: sha512-Crp6WY9aTYP3qPi2wGDo9iUe/rceX01UMhnF1jmwDcKCFM6cx7YhGP/Mpr3y9AASpfHixIG0E6azCcL5OcDHsQ==}

  '@types/hashring@3.2.5':
    resolution: {integrity: sha512-Y80Bt3Y9Z6erq9bGvwbxLD8z0XGLxK41XXeAJtaX4BbVUvStqhfGjKtXNVNISfBVxE/hndVw6aQ3EdOIVcwIFg==}

  '@types/http-errors@2.0.5':
    resolution: {integrity: sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==}

  '@types/i18n@0.13.12':
    resolution: {integrity: sha512-iAd2QjKh+0ToBXocmCS3m38GskiaGzmSV1MTQz2GaOraqSqBiLf46J7u3EGINl+st+Uk4lO3OL7QyIjTJlrWIg==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/jsonwebtoken@9.0.10':
    resolution: {integrity: sha512-asx5hIG9Qmf/1oStypjanR7iKTv0gXQ1Ov/jfrX6kS/EO0OFni8orbmGCn0672NHR3kXHwpAwR+B368ZGN/2rA==}

  '@types/lodash@4.17.12':
    resolution: {integrity: sha512-sviUmCE8AYdaF/KIHLDJBQgeYzPBI0vf/17NaYehBJfYD1j6/L95Slh07NlyK2iNyBNaEkb3En2jRt+a8y3xZQ==}

  '@types/methods@1.1.4':
    resolution: {integrity: sha512-ymXWVrDiCxTBE3+RIrrP533E70eA+9qu7zdWoHuOmGujkYtzf4HQF96b8nwHLqhuf4ykX61IGRIB38CC6/sImQ==}

  '@types/mime@1.3.5':
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==}

  '@types/minimatch@3.0.5':
    resolution: {integrity: sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ==}

  '@types/minimist@1.2.5':
    resolution: {integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==}

  '@types/mocha@10.0.10':
    resolution: {integrity: sha512-xPyYSz1cMPnJQhl0CLMH68j3gprKZaTjG3s5Vi+fDgx+uhG9NOXwbVt52eFS8ECyXhyKcjDLCBEqBExKuiZb7Q==}

  '@types/ms@2.1.0':
    resolution: {integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==}

  '@types/node@22.14.1':
    resolution: {integrity: sha512-u0HuPQwe/dHrItgHHpmw3N2fYCR6x4ivMNbPHRkBVP4CvN+kiRrKHWk3i8tXiO/joPwXLMYvF9TTF0eqgHIuOw==}

  '@types/normalize-package-data@2.4.4':
    resolution: {integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==}

  '@types/pg@8.11.11':
    resolution: {integrity: sha512-kGT1qKM8wJQ5qlawUrEkXgvMSXoV213KfMGXcwfDwUIfUHXqXYXOfS1nE1LINRJVVVx5wCm70XnFlMHaIcQAfw==}

  '@types/qs@6.14.0':
    resolution: {integrity: sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==}

  '@types/range-parser@1.2.7':
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==}

  '@types/request@2.48.12':
    resolution: {integrity: sha512-G3sY+NpsA9jnwm0ixhAFQSJ3Q9JkpLZpJbI3GMv0mIAT0y3mRabYeINzal5WOChIiaTEGQYlHOKgkaM9EisWHw==}

  '@types/send@0.17.5':
    resolution: {integrity: sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==}

  '@types/serve-static@1.15.7':
    resolution: {integrity: sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==}

  '@types/sinon-chai@3.2.12':
    resolution: {integrity: sha512-9y0Gflk3b0+NhQZ/oxGtaAJDvRywCa5sIyaVnounqLvmf93yBF4EgIRspePtkMs3Tr844nCclYMlcCNmLCvjuQ==}

  '@types/sinon@10.0.20':
    resolution: {integrity: sha512-2APKKruFNCAZgx3daAyACGzWuJ028VVCUDk6o2rw/Z4PXT0ogwdV4KUegW0MwVs0Zu59auPXbbuBJHF12Sx1Eg==}

  '@types/sinonjs__fake-timers@8.1.5':
    resolution: {integrity: sha512-mQkU2jY8jJEF7YHjHvsQO8+3ughTL1mcnn96igfhONmR+fUPSKIkefQYpSe8bsly2Ep7oQbn/6VG5/9/0qcArQ==}

  '@types/superagent@8.1.9':
    resolution: {integrity: sha512-pTVjI73witn+9ILmoJdajHGW2jkSaOzhiFYF1Rd3EQ94kymLqB9PjD9ISg7WaALC7+dCHT0FGe9T2LktLq/3GQ==}

  '@types/supertest@6.0.2':
    resolution: {integrity: sha512-137ypx2lk/wTQbW6An6safu9hXmajAifU/s7szAHLN/FeIm5w7yR0Wkl9fdJMRSHwOn4HLAI0DaB2TOORuhPDg==}

  '@types/to-ico@1.1.3':
    resolution: {integrity: sha512-3Ew8Hsz/qiDGzwvz75pjRU+6Ocfvrit6hHfirauEdJXxdro73MTe5XdcANh4GZ2wdJqWw9BIuHwWgKAfjUXoGw==}

  '@types/tough-cookie@4.0.5':
    resolution: {integrity: sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==}

  '@types/uuid@9.0.8':
    resolution: {integrity: sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==}

  '@types/validator@13.15.3':
    resolution: {integrity: sha512-7bcUmDyS6PN3EuD9SlGGOxM77F8WLVsrwkxyWxKnxzmXoequ6c7741QBrANq6htVRGOITJ7z72mTP6Z4XyuG+Q==}

  '@typescript-eslint/eslint-plugin@8.43.0':
    resolution: {integrity: sha512-8tg+gt7ENL7KewsKMKDHXR1vm8tt9eMxjJBYINf6swonlWgkYn5NwyIgXpbbDxTNU5DgpDFfj95prcTq2clIQQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.43.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/parser@8.43.0':
    resolution: {integrity: sha512-B7RIQiTsCBBmY+yW4+ILd6mF5h1FUwJsVvpqkrgpszYifetQ2Ke+Z4u6aZh0CblkUGIdR59iYVyXqqZGkZ3aBw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/project-service@8.43.0':
    resolution: {integrity: sha512-htB/+D/BIGoNTQYffZw4uM4NzzuolCoaA/BusuSIcC8YjmBYQioew5VUZAYdAETPjeed0hqCaW7EHg+Robq8uw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/scope-manager@8.43.0':
    resolution: {integrity: sha512-daSWlQ87ZhsjrbMLvpuuMAt3y4ba57AuvadcR7f3nl8eS3BjRc8L9VLxFLk92RL5xdXOg6IQ+qKjjqNEimGuAg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/tsconfig-utils@8.43.0':
    resolution: {integrity: sha512-ALC2prjZcj2YqqL5X/bwWQmHA2em6/94GcbB/KKu5SX3EBDOsqztmmX1kMkvAJHzxk7TazKzJfFiEIagNV3qEA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/type-utils@8.43.0':
    resolution: {integrity: sha512-qaH1uLBpBuBBuRf8c1mLJ6swOfzCXryhKND04Igr4pckzSEW9JX5Aw9AgW00kwfjWJF0kk0ps9ExKTfvXfw4Qg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/types@8.43.0':
    resolution: {integrity: sha512-vQ2FZaxJpydjSZJKiSW/LJsabFFvV7KgLC5DiLhkBcykhQj8iK9BOaDmQt74nnKdLvceM5xmhaTF+pLekrxEkw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.43.0':
    resolution: {integrity: sha512-7Vv6zlAhPb+cvEpP06WXXy/ZByph9iL6BQRBDj4kmBsW98AqEeQHlj/13X+sZOrKSo9/rNKH4Ul4f6EICREFdw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/utils@8.43.0':
    resolution: {integrity: sha512-S1/tEmkUeeswxd0GGcnwuVQPFWo8NzZTOMxCvw8BX7OMxnNae+i8Tm7REQen/SwUIPoPqfKn7EaZ+YLpiB3k9g==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/visitor-keys@8.43.0':
    resolution: {integrity: sha512-T+S1KqRD4sg/bHfLwrpF/K3gQLBM1n7Rp7OjjikjTEssI2YJzQpi5WXoynOaQ93ERIuq3O8RBTOUYDKszUCEHw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@yarnpkg/lockfile@1.1.0':
    resolution: {integrity: sha512-GpSwvyXOcOOlV70vbnzjj4fW5xW/FdUF6nQEt1ENy7m4ZCczi1+/buVUPAqmGfqznsORNFzUMjctTIp8a9tuCQ==}

  '@yarnpkg/parsers@3.0.2':
    resolution: {integrity: sha512-/HcYgtUSiJiot/XWGLOlGxPYUG65+/31V8oqk17vZLW1xlCoR4PampyePljOxY2n8/3jz9+tIFzICsyGujJZoA==}
    engines: {node: '>=18.12.0'}

  '@zkochan/js-yaml@0.0.7':
    resolution: {integrity: sha512-nrUSn7hzt7J6JWgWGz78ZYI8wj+gdIJdk0Ynjpp8l+trkn58Uqsf6RYrYkEK+3X18EX+TNdtJI0WxAtc+L84SQ==}
    hasBin: true

  JSONStream@1.3.5:
    resolution: {integrity: sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ==}
    hasBin: true

  abbrev@2.0.0:
    resolution: {integrity: sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  abstract-logging@2.0.1:
    resolution: {integrity: sha512-2BjRTZxTPvheOvGbBslFSYOUkr+SjPtOnrLP33f+VIWLzezQpZcqVg7ja3L4dBXmzzgwT+a029jRx5PCi3JuiA==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-walk@8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==}
    engines: {node: '>=0.4.0'}

  acorn@8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  add-stream@1.0.0:
    resolution: {integrity: sha512-qQLMr+8o0WC4FZGQTcJiKBVC59JylcPSrTtk6usvmIDFUOCKegapy1VHQwRbFMOFyb/inzUVqHs+eMYKDM1YeQ==}

  after@0.8.2:
    resolution: {integrity: sha512-QbJ0NTQ/I9DI3uSJA4cbexiwQeRAfjPScqIbSjUDd9TOrcg6pTkdgziesOqxBMBzit8vFCTwrP27t13vFOORRA==}

  agent-base@4.3.0:
    resolution: {integrity: sha512-salcGninV0nPrwpGNn4VTXBb1SOuXQBiqbrNXoeizJsHrsL6ERFM2Ne3JUSBWRE6aeNJI2ROP/WEEIDUiDe3cg==}
    engines: {node: '>= 4.0.0'}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  agent-base@7.1.4:
    resolution: {integrity: sha512-MnA+YT8fwfJPgBx3m60MNqakm30XOkyIoH1y6huTQvC0PwZG7ki8NacLBcrPbNoo8vEZy7Jpuk7+jMO+CUovTQ==}
    engines: {node: '>= 14'}

  agentkeepalive@4.6.0:
    resolution: {integrity: sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==}
    engines: {node: '>= 8.0.0'}

  aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}

  ajv-formats@3.0.1:
    resolution: {integrity: sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}

  ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}

  ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}

  ansi-regex@3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==}
    engines: {node: '>=4'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.2.2:
    resolution: {integrity: sha512-Bq3SmSpyFHaWjPk8If9yc6svM8c56dB5BAtW4Qbw5jHTwwXXcTLoRMkpDJp6VL0XzlWaCHTXrkFURMYmD0sLqg==}
    engines: {node: '>=12'}

  ansi-styles@2.2.1:
    resolution: {integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==}
    engines: {node: '>=0.10.0'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  ansi-styles@6.2.3:
    resolution: {integrity: sha512-4Dj6M28JB+oAH8kFkTLUo+a2jwOFkuqb3yucU0CANcRRUbxS0cP0nZYCGjcc3BNXwRIsUVmDGgzawme7zvJHvg==}
    engines: {node: '>=12'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  append-field@1.0.0:
    resolution: {integrity: sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw==}

  append-query@2.1.1:
    resolution: {integrity: sha512-adm0E8o1o7ay+HbkWvGIpNNeciLB/rxJ0heThHuzSSVq5zcdQ5/ZubFnUoY0imFmk6gZVghSpwoubLVtwi9EHQ==}

  append-transform@2.0.0:
    resolution: {integrity: sha512-7yeyCEurROLQJFv5Xj4lEGTy0borxepjFv1g22oAdqFu//SrAlDl1O1Nxx15SH1RoliUml6p8dwJW9jvZughhg==}
    engines: {node: '>=8'}

  aproba@1.2.0:
    resolution: {integrity: sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==}

  aproba@2.0.0:
    resolution: {integrity: sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==}

  archy@1.0.0:
    resolution: {integrity: sha512-Xg+9RwCg/0p32teKdGMPTPnVXKD0w3DfHnFTficozsAgsvq2XenPJq/MYpzzQ/v8zrOyJn6Ds39VA4JIDwFfqw==}

  are-we-there-yet@1.1.7:
    resolution: {integrity: sha512-nxwy40TuMiUGqMyRHgCSWZ9FM4VAoRP4xUYSTv5ImRog+h9yISPbVH7H8fASCIzYn9wlEv4zvFL7uKDMCFQm3g==}
    deprecated: This package is no longer supported.

  arg@4.1.3:
    resolution: {integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==}

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==}
    engines: {node: '>= 0.4'}

  array-differ@3.0.0:
    resolution: {integrity: sha512-THtfYS6KtME/yIAhKjZ2ul7XI96lQGHRputJQHO80LAWQnuGP4iCIN8vdMRboGbIEYBwU33q8Tch1os2+X0kMg==}
    engines: {node: '>=8'}

  array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  array-ify@1.0.0:
    resolution: {integrity: sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng==}

  array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==}
    engines: {node: '>= 0.4'}

  arraybuffer.slice@0.0.7:
    resolution: {integrity: sha512-wGUIVQXuehL5TCqQun8OW81jGzAWycqzFF8lFp+GOM5BXLYj3bKNsYC4daB7n6XjCqxQA/qgTJ+8ANR3acjrog==}

  arrify@1.0.1:
    resolution: {integrity: sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==}
    engines: {node: '>=0.10.0'}

  arrify@2.0.1:
    resolution: {integrity: sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==}
    engines: {node: '>=8'}

  asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}

  asn1@0.2.6:
    resolution: {integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==}

  assert-plus@1.0.0:
    resolution: {integrity: sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==}
    engines: {node: '>=0.8'}

  assertion-error@1.1.0:
    resolution: {integrity: sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==}

  ast-types@0.13.4:
    resolution: {integrity: sha512-x1FCFnFifvYDDzTaLII71vG5uvDwgtmDTEVWAxrgeiR8VjMONcCXJx7E+USjDtHlwFmt9MysbqgF9b9Vjr6w+w==}
    engines: {node: '>=4'}

  ast-types@0.14.2:
    resolution: {integrity: sha512-O0yuUDnZeQDL+ncNGlJ78BiO4jnYI3bvMsD5prT0/nsgijG/LpNBIr63gTjVTNsiGkgQhiyCShTgxt8oXOrklA==}
    engines: {node: '>=4'}

  async-function@1.0.0:
    resolution: {integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==}
    engines: {node: '>= 0.4'}

  async-hook-jl@1.7.6:
    resolution: {integrity: sha512-gFaHkFfSxTjvoxDMYqDuGHlcRyUuamF8s+ZTtJdDzqjws4mCt7v0vuV79/E2Wr2/riMQgtG4/yUtXWs1gZ7JMg==}
    engines: {node: ^4.7 || >=6.9 || >=7.3}

  async-limiter@1.0.1:
    resolution: {integrity: sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==}

  async-retry@1.3.3:
    resolution: {integrity: sha512-wfr/jstw9xNi/0teMHrRW7dsz3Lt5ARhYNZ2ewpadnhaIp5mbALhOAP+EAdsC7t4Z6wqsDVv9+W6gm1Dk9mEyw==}

  async@1.5.2:
    resolution: {integrity: sha512-nSVgobk4rv61R9PUSDtYt7mPVB2olxNR5RWJcAsH676/ef11bUZwvu7+RGYrYauVdDPcO519v68wRhXQtxsV9w==}

  async@2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==}

  async@3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  atomic-sleep@1.0.0:
    resolution: {integrity: sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==}
    engines: {node: '>=8.0.0'}

  authenticator-cli@1.0.5:
    resolution: {integrity: sha512-8FjXzLnytd93zE9IxtOr7/uptlxGBjQhWw6qEjPPLhfq1TqrUpaBEhPYOErtHWIPuveAgzHHEBgp2bh6GdQ6Ew==}
    hasBin: true

  authenticator@1.1.5:
    resolution: {integrity: sha512-eaT0Trfxka28DkljLQDxuoSn9uDTaYIoXhZMsAw3Z54fNC7BMIsvIxfG6Y5s+y02CH59IIyY3p1EOMqeIpljWQ==}
    hasBin: true

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}

  avvio@6.5.0:
    resolution: {integrity: sha512-BmzcZ7gFpyFJsW8G+tfQw8vJNUboA9SDkkHLZ9RAALhvw/rplfWwni8Ee1rA11zj/J7/E5EvZmweusVvTHjWCA==}

  avvio@9.1.0:
    resolution: {integrity: sha512-fYASnYi600CsH/j9EQov7lECAniYiBFiiAtBNuZYLA2leLe9qOvZzqYHFjtIj6gD2VMoMLP14834LFWvr4IfDw==}

  aws-sign2@0.7.0:
    resolution: {integrity: sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==}

  aws4@1.13.2:
    resolution: {integrity: sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==}

  axios@1.11.0:
    resolution: {integrity: sha512-1Lx3WLFQWm3ooKDYZD1eXmoGO9fxYQjrycfHFC8P0sCfQVXyROp0p9PFWBehewBOdCwHc+f/b8I0fMto5eSfwA==}

  axios@1.7.7:
    resolution: {integrity: sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==}

  babel-runtime@6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==}

  backo2@1.0.2:
    resolution: {integrity: sha512-zj6Z6M7Eq+PBZ7PQxl5NT665MvJdAkzp0f60nAJ+sLaSCBPMwVak5ZegFbgVCzFcCJTKFoMizvM5Ld7+JrRJHA==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-arraybuffer@0.1.5:
    resolution: {integrity: sha512-437oANT9tP582zZMwSvZGy2nmSeAb8DW2me3y+Uv1Wp2Rulr8Mqlyrv3E7MLxmsiaPSMMDmiDVzgE+e8zlMx9g==}
    engines: {node: '>= 0.6.0'}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  base64id@1.0.0:
    resolution: {integrity: sha512-rz8L+d/xByiB/vLVftPkyY215fqNrmasrcJsYkVcm4TgJNz+YXKrFaFAWibSaHkiKoSgMDCb+lipOIRQNGYesw==}
    engines: {node: '>= 0.4.0'}

  base64id@2.0.0:
    resolution: {integrity: sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==}
    engines: {node: ^4.5.0 || >= 5.9}

  bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==}

  before-after-hook@2.2.3:
    resolution: {integrity: sha512-NzUnlZexiaH/46WDhANlyR2bXRopNg4F/zuSA3OpZnllCUgRaOF2znDioDWrmbNVsuZk6l9pMquQB38cfBZwkQ==}

  better-assert@1.0.2:
    resolution: {integrity: sha512-bYeph2DFlpK1XmGs6fvlLRUN29QISM3GBuUwSFsMY2XRx4AvC0WNCS57j4c/xGrK2RS24C1w3YoBOsw9fT46tQ==}

  big-integer@1.6.52:
    resolution: {integrity: sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==}
    engines: {node: '>=0.6'}

  bignumber.js@2.4.0:
    resolution: {integrity: sha512-uw4ra6Cv483Op/ebM0GBKKfxZlSmn6NgFRby5L3yGTlunLj53KQgndDlqy2WVFOwgvurocApYkSud0aO+mvrpQ==}

  bignumber.js@4.1.0:
    resolution: {integrity: sha512-eJzYkFYy9L4JzXsbymsFn3p54D+llV27oTQ+ziJG7WFRheJcNZilgVXMG0LoZtlQSKBsJdWtLFqOD0u+U0jZKA==}

  bignumber.js@9.3.1:
    resolution: {integrity: sha512-Ko0uX15oIUS7wJ3Rb30Fs6SkVbLmPBAKdlm7q9+ak9bbIeFf0MwuBsQV6z7+X768/cHsfg+WlysDWJcmthjsjQ==}

  bin-links@4.0.4:
    resolution: {integrity: sha512-cMtq4W5ZsEwcutJrVId+a/tjt8GSbS+h0oNkdl6+6rBuEv8Ot33Bevj5KPm40t309zuhVic8NjpuL42QCiJWWA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  binary@0.3.0:
    resolution: {integrity: sha512-D4H1y5KYwpJgK8wk1Cue5LLPgmwHKYSChkbspQg5JtVuR5ulGckxfR62H3AE9UDkdMC8yyXlqYihuz3Aqg2XZg==}

  bindings@1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}

  bintrees@1.0.2:
    resolution: {integrity: sha512-VOMgTMwjAaUG580SXn3LacVgjurrbMme7ZZNYGSSV7mmtY6QQRh0Eg3pwIcntQ77DErK1L0NxkbetjcoXzVwKw==}

  bl@1.2.3:
    resolution: {integrity: sha512-pvcNpa0UU69UT341rO6AYy4FVAIkUHuZXRIWbq+zHnsVcRzDDjIAhGuuYoi0d//cwIwtt4pkpKycWEfjdV+vww==}

  bl@2.2.1:
    resolution: {integrity: sha512-6Pesp1w0DEX1N550i/uGV/TqucVL4AM/pgThFSN/Qq9si1/DF9aIHs1BxD8V/QU0HoeHO6cQRTAuYnLPKq1e4g==}

  bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}

  blob@0.0.5:
    resolution: {integrity: sha512-gaqbzQPqOoamawKg0LGVd7SzLgXS+JH61oWprSLH+P+abTczqJbhTR8CmJ2u9/bUYNmHTGJx/UEmn6doAvvuig==}

  bluebird@3.4.7:
    resolution: {integrity: sha512-iD3898SR7sWVRHbiQv+sHUtHnMvC1o3nW5rAcqnq3uOn07DSAppZYUkIGslDz6gXC7HfunPe7YVBgoEJASPcHA==}

  bluebird@3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}

  bmp-js@0.0.1:
    resolution: {integrity: sha512-OS74Rlt0Aynu2mTPmY9RZOUOXlqWecFIILFXr70vv16/xCZnFxvri9IKkF1IGxQ8r9dOE62qGNpKxXx8Lko8bg==}

  bmp-js@0.0.3:
    resolution: {integrity: sha512-epsm3Z92j5xwek9p97pVw3KbsNc0F4QnbYh+N93SpbJYuHFQQ/UAh6K+bKFGyLePH3Hudtl/Sa95Quqp0gX8IQ==}

  body-parser@1.12.4:
    resolution: {integrity: sha512-fueabp0EDZKvebbSI94mGzVlJr3vViXA7q+W+52MFZCrcJjRlnTkPQjpua8+6M6WOh1swnw+DJiUrETWRIQn9g==}
    engines: {node: '>= 0.8'}

  body-parser@1.18.2:
    resolution: {integrity: sha512-XIXhPptoLGNcvFyyOzjNXCjDYIbYj4iuXO0VU9lM0f3kYdG0ar5yg7C+pIc3OyoTlZXDu5ObpLTmS2Cgp89oDg==}
    engines: {node: '>= 0.8'}

  body-parser@1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  bole-console@0.1.10:
    resolution: {integrity: sha512-FXRdcHRXUChW/i5XDWmLcvhKIy4YyvF1qMDVIXtJaEcwZGOXDlmGwMc1YkFHcJ9jPp05KAjwzswI6oXlLSb0fQ==}
    engines: {node: '>=0.10'}

  bole@3.0.2:
    resolution: {integrity: sha512-WLCTpsZke/bFmc5FzMfl6p8aOP0c0opkJKXdx0Chpo68Dn2ScKf9Rii6STFLOeh7v5qOCYT/JLcxw7y0Dj66gw==}

  bole@5.0.15:
    resolution: {integrity: sha512-Fl3VU10+7uLIOSV6QKdVND/4uaiAo6oW5kAjwkwhuX6bMGeqiIvalaPNGsisknpWNpT8/RXSWkiytlaNNuBnhA==}

  brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browser-stdout@1.3.1:
    resolution: {integrity: sha512-qhAVI1+Av2X7qelOfAIYwXONood6XlZE/fXaBSmW/T5SzLAmCgzi+eiWE7fUvbHaeNBQH13UftjpXxsfLkMpgw==}

  browserslist@4.25.4:
    resolution: {integrity: sha512-4jYpcjabC606xJ3kw2QwGEZKX0Aw7sgQdZCvIK9dhVSPh76BKo+C+btT1RRofH7B+8iNpEbgGNVWiLki5q93yg==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-alloc-unsafe@1.1.0:
    resolution: {integrity: sha512-TEM2iMIEQdJ2yjPJoSIsldnleVaAk1oW3DBVUykyOLsEsFmEc9kn+SFFPz+gl54KQNxlDnAwCXosOS9Okx2xAg==}

  buffer-alloc@1.2.0:
    resolution: {integrity: sha512-CFsHQgjtW1UChdXgbyJGtnm+O/uLQeZdtbDo8mfUgYXCHSM1wgrVxXm6bSyrUuErEb+4sYVGCzASBRot7zyrow==}

  buffer-crc32@0.2.13:
    resolution: {integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==}

  buffer-equal-constant-time@1.0.1:
    resolution: {integrity: sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==}

  buffer-equal@0.0.1:
    resolution: {integrity: sha512-RgSV6InVQ9ODPdLWJ5UAqBqJBOg370Nz6ZQtRzpt6nUjc8v0St97uJ4PYC6NztqIScrAXafKM3mZPMygSe1ggA==}
    engines: {node: '>=0.4.0'}

  buffer-fill@1.0.0:
    resolution: {integrity: sha512-T7zexNBwiiaCOGDg9xNX9PBmjrubblRkENuptryuI64URkXDFum9il/JGL8Lm8wYfAXpredVXXZz7eMHilimiQ==}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  buffer-indexof-polyfill@1.0.2:
    resolution: {integrity: sha512-I7wzHwA3t1/lwXQh+A5PbNvJxgfo5r3xulgpYDB5zckTu/Z9oUK9biouBKQUjEqzaz3HnAT6TYoovmE+GqSf7A==}
    engines: {node: '>=0.10'}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  buffermaker@1.2.1:
    resolution: {integrity: sha512-IdnyU2jDHU65U63JuVQNTHiWjPRH0CS3aYd/WPaEwyX84rFdukhOduAVb1jwUScmb5X0JWPw8NZOrhoLMiyAHQ==}

  buffers@0.1.1:
    resolution: {integrity: sha512-9q/rDEGSb/Qsvv2qvzIzdluL5k7AaJOTrw23z9reQthrbF7is4CtlT0DXyO1oei2DCp4uojjzQ7igaSHp1kAEQ==}
    engines: {node: '>=0.2.0'}

  builtin-modules@3.3.0:
    resolution: {integrity: sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==}
    engines: {node: '>=6'}

  builtin-modules@5.0.0:
    resolution: {integrity: sha512-bkXY9WsVpY7CvMhKSR6pZilZu9Ln5WDrKVBUXf2S443etkmEO4V58heTecXcUIsNsi4Rx8JUO4NfX1IcQl4deg==}
    engines: {node: '>=18.20'}

  busboy@0.2.14:
    resolution: {integrity: sha512-InWFDomvlkEj+xWLBfU3AvnbVYqeTWmQopiW0tWWEy5yehYm2YkGEc59sUmw/4ty5Zj/b0WHGs1LgecuBSBGrg==}
    engines: {node: '>=0.8.0'}

  byte-size@8.1.1:
    resolution: {integrity: sha512-tUkzZWK0M/qdoLEqikxBWe4kumyuwjl3HO6zHTr4yEI23EojPtLYXdG1+AQY7MN0cGyNDvEaJ8wiYQm6P2bPxg==}
    engines: {node: '>=12.17'}

  bytes@1.0.0:
    resolution: {integrity: sha512-/x68VkHLeTl3/Ll8IvxdwzhrT+IyKc52e/oyHhA2RwqPqswSnjVbSddfPRwAsJtbilMAPSRWwAlpxdYsSWOTKQ==}

  bytes@2.1.0:
    resolution: {integrity: sha512-k9VSlRfRi5JYyQWMylSOgjld96ta1qaQUIvmn+na0BzViclH04PBumewv4z5aeXNkn6Z/gAN5FtPeBLvV20F9w==}

  bytes@3.0.0:
    resolution: {integrity: sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==}
    engines: {node: '>= 0.8'}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  cacache@18.0.4:
    resolution: {integrity: sha512-B+L5iIa9mgcjLbliir2th36yEwPftrzteHYujzsx3dFP/31GCHcIeS8f5MGd80odLOjaOvSpU3EEAmRQptkxLQ==}
    engines: {node: ^16.14.0 || >=18.0.0}

  caching-transform@4.0.0:
    resolution: {integrity: sha512-kpqOvwXnjjN44D89K5ccQC+RUrsy7jB/XLlRrx0D7/2HNcTPqzsb6XgYoErwko6QsV184CA2YgS1fxDiiDZMWA==}
    engines: {node: '>=8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}

  callsite@1.0.0:
    resolution: {integrity: sha512-0vdNRFXn5q+dtOqjfFtmtlI9N2eVZ7LMyEV2iKC5mEEFvSg/69Ml6b/WU2qF8W1nLRa0wiSrDT3Y5jOHZCwKPQ==}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase-keys@6.2.2:
    resolution: {integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==}
    engines: {node: '>=8'}

  camelcase@3.0.0:
    resolution: {integrity: sha512-4nhGqUkc4BqbBBB4Q6zLuD7lzzrHYrjKGeYaEji/3tFR5VdJu9v+LilhGIVe8wxEJPPOeWo7eg8dwY13TZ1BNg==}
    engines: {node: '>=0.10.0'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  can-promise@0.0.1:
    resolution: {integrity: sha512-gzVrHyyrvgt0YpDm7pn04MQt8gjh0ZAhN4ZDyCRtGl6YnuuK6b4aiUTD7G52r9l4YNmxfTtEscb92vxtAlL6XQ==}

  caniuse-lite@1.0.30001741:
    resolution: {integrity: sha512-QGUGitqsc8ARjLdgAfxETDhRbJ0REsP6O3I96TAth/mVjh2cYzN2u+3AzPP3aVSm2FehEItaJw1xd+IGBXWeSw==}

  caseless@0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==}

  centra@2.7.0:
    resolution: {integrity: sha512-PbFMgMSrmgx6uxCdm57RUos9Tc3fclMvhLSATYN39XsDV29B89zZ3KA89jmY0vwSGazyU+uerqwa6t+KaodPcg==}

  chai-as-promised@7.1.2:
    resolution: {integrity: sha512-aBDHZxRzYnUYuIAIPBH2s511DjlKPzXNlXSGFC8CwmroWQLfrW0LtE1nK3MAwwNhJPa9raEjNCmRoFpG0Hurdw==}
    peerDependencies:
      chai: '>= 2.1.2 < 6'

  chai-datetime@1.8.1:
    resolution: {integrity: sha512-GqQPq+l1yQI/c6sG2rvGkrM5SrmBm1ox/cmRjIA+g4632kLYGRWSIQR02qCeCkwabjYyCZNsHAKMV6gCsSV/1Q==}

  chai-exclude@2.0.2:
    resolution: {integrity: sha512-QmNVnvdSw8Huccdjm49mKu3HtoHxvjdavgYkY0KPQ5MI5UWfbc9sX1YqRgaMPf2GGtDXPoF2ram3AeNS4945Xw==}
    peerDependencies:
      chai: '>= 4.0.0 < 5'

  chai-shallow-deep-equal@1.4.4:
    resolution: {integrity: sha512-nJMVI1MRfElJ5Qb/A4dAV7A2RSXijRv/LaJKnoIAJKUQHFvx31eiEnDtSI9yTyqQua7WWj4xZHRsIvIeG6C/cQ==}
    engines: {node: '>= 0.6.0'}
    peerDependencies:
      chai: '>= 1.9.0'

  chai@4.3.10:
    resolution: {integrity: sha512-0UXG04VuVbruMUYbJ6JctvH0YnC/4q3/AkT18q4NaITo91CUm0liMS9VqzT9vZhVQ/1eqPanMWjBM+Juhfb/9g==}
    engines: {node: '>=4'}

  chainsaw@0.1.0:
    resolution: {integrity: sha512-75kWfWt6MEKNC8xYXIdRpDehRYY/tNSgwKaJq+dbbDcxORuVrrQ+SEHoWsniVn9XPYfP4gmdWIeDk/4YNp1rNQ==}

  chalk@1.1.3:
    resolution: {integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==}
    engines: {node: '>=0.10.0'}

  chalk@4.1.0:
    resolution: {integrity: sha512-qwx12AxXe2Q5xQ43Ac//I6v5aXTipYrSESdOgzrN+9XjgEpyjpKuvSGaN4qE93f7TQTlerQQ8S+EQ0EyDoVL1A==}
    engines: {node: '>=10'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chance@1.1.13:
    resolution: {integrity: sha512-V6lQCljcLznE7tUYUM9EOAnnKXbctE6j/rdQkYOHIWbfGQbrzTsAXNW9CdU5XCo4ArXQCj/rb6HgxPlmGJcaUg==}

  chardet@2.1.0:
    resolution: {integrity: sha512-bNFETTG/pM5ryzQ9Ad0lJOTa6HWD/YsScAR3EnCPZRPlQh77JocYktSHOUHelyhm8IARL+o4c4F1bP5KVOjiRA==}

  check-error@1.0.3:
    resolution: {integrity: sha512-iKEoDYaRmd1mxM90a2OEfWhjsjPpYPuQ+lMYsoxB126+t8fw7ySEO48nmDg5COTjxDI65/Y2OWpeEHk3ZOe8zg==}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  chownr@1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}

  chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}

  ci-info@3.9.0:
    resolution: {integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==}
    engines: {node: '>=8'}

  ci-info@4.3.0:
    resolution: {integrity: sha512-l+2bNRMiQgcfILUi33labAZYIWlH1kWDp+ecNo5iisRKrbm0xcRyCww71/YU0Fkw0mAFpz9bJayXPjey6vkmaQ==}
    engines: {node: '>=8'}

  cidr-regex@3.1.1:
    resolution: {integrity: sha512-RBqYd32aDwbCMFJRL6wHOlDNYJsPNTt8vC82ErHF5vKt8QQzxm1FrkW8s/R5pVrXMf17sba09Uoy91PKiddAsw==}
    engines: {node: '>=10'}

  clean-regexp@1.0.0:
    resolution: {integrity: sha512-GfisEZEJvzKrmGWkvfhgzcz/BllN1USeqD2V6tg14OAOgaCD2Z/PUEuxnAZ/nPvmaHRG7a8y77p1T/IRQ4D1Hw==}
    engines: {node: '>=4'}

  clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}

  cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}

  cli-spinners@2.6.1:
    resolution: {integrity: sha512-x/5fWmGMnbKQAaNwN+UZlV79qBLM9JFnJuJ03gIi5whrob0xV0ofNVHy9DhwGdsMJQc2OKv0oGmLzvaqvAVv+g==}
    engines: {node: '>=6'}

  cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}

  cli-width@3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}

  cli@1.0.1:
    resolution: {integrity: sha512-41U72MB56TfUMGndAKK8vJ78eooOD4Z5NOL4xEfjc0c23s+6EYKXlXsmACBVclLP1yOfWCgEganVzddVrSNoTg==}
    engines: {node: '>=0.2.5'}

  cliui@3.2.0:
    resolution: {integrity: sha512-0yayqDxWQbqk3ojkYqUKqaAQ6AfNKeKWRNA8kR0WXzAsdHpP4BIaOmMAG87JGuO6qcobyW4GjxHd9PmhEd+T9w==}

  cliui@4.1.0:
    resolution: {integrity: sha512-4FG+RSG9DL7uEwRUZXZn3SS34DiDPfzP0VOiEwtUWlE+AR2EIg+hSyvrIgUUfhdgR/UkAeW2QHgeP+hWrXs7jQ==}

  cliui@6.0.0:
    resolution: {integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==}

  cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clone-deep@4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}

  clone@0.2.0:
    resolution: {integrity: sha512-g62n3Kb9cszeZvmvBUqP/dsEJD/+80pDA8u8KqHnAPrVnQ2Je9rVV6opxkhuWCd1kCn2gOibzDKxCtBvD3q5kA==}

  clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==}
    engines: {node: '>=0.8'}

  clone@2.1.2:
    resolution: {integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==}
    engines: {node: '>=0.8'}

  cls-hooked@4.2.2:
    resolution: {integrity: sha512-J4Xj5f5wq/4jAvcdgoGsL3G103BtWpZrMo8NEinRltN+xpTZdI+M38pyQqhuFU/P792xkMFvnKSf+Lm81U1bxw==}
    engines: {node: ^4.7 || >=6.9 || >=7.3 || >=8.2.1}

  cluster-key-slot@1.1.2:
    resolution: {integrity: sha512-RMr0FhtfXemyinomL4hrWcYJxmX6deFdCxpJzhDttxgO1+bcCnkk+9drydLVDmAMG7NE6aN/fl4F7ucU/90gAA==}
    engines: {node: '>=0.10.0'}

  cmd-shim@6.0.3:
    resolution: {integrity: sha512-FMabTRlc5t5zjdenF6mS0MBeFZm0XqHqeOkcskKFb/LYCcRQ5fVgLOHVc4Lq9CqABd9zhjwPjMBCJvMCziSVtA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  co@4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  code-point-at@1.1.0:
    resolution: {integrity: sha512-RpAVKQA5T63xEj6/giIbUEtZwJ4UFIc3ZtvEkiaUERylqe8xb5IvqcgOurZLahv93CLKfxcw5YI+DZcUBRyLXA==}
    engines: {node: '>=0.10.0'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-support@1.1.3:
    resolution: {integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==}
    hasBin: true

  columnify@1.6.0:
    resolution: {integrity: sha512-lomjuFZKfM6MSAnV9aCZC9sc0qGbmZdfygNv+nCpqVkSKdCxCklLtd16O0EILGkImHw9ZpHkAnHaB+8Zxq5W6Q==}
    engines: {node: '>=8.0.0'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.11.0:
    resolution: {integrity: sha512-b0553uYA5YAEGgyYIGYROzKQ7X5RAqedkfjiZxwi0kL1g3bOaBNNZfYkzt/CL0umgD5wc9Jec2FbB98CjkMRvQ==}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  common-ancestor-path@1.0.1:
    resolution: {integrity: sha512-L3sHRo1pXXEqX8VU28kfgUY+YGsk09hPqZiZmLacNib6XNTCM8ubYeT7ryXQw8asB1sKgcU5lkB7ONug08aB8w==}

  commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==}

  compare-func@2.0.0:
    resolution: {integrity: sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA==}

  component-bind@1.0.0:
    resolution: {integrity: sha512-WZveuKPeKAG9qY+FkYDeADzdHyTYdIboXS59ixDeRJL5ZhxpqUnxSOwop4FQjMsiYm3/Or8cegVbpAHNA7pHxw==}

  component-emitter@1.2.1:
    resolution: {integrity: sha512-jPatnhd33viNplKjqXKRkGU345p263OIWzDL2wH3LGIGp5Kojo+uXizHmOADRvhGFFTnJqX3jBAKP6vvmSDKcA==}

  component-emitter@1.3.1:
    resolution: {integrity: sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==}

  component-inherit@0.0.3:
    resolution: {integrity: sha512-w+LhYREhatpVqTESyGFg3NlP6Iu0kEKUHETY9GoZP/pQyW4mHFZuFWRUCIqVPZ36ueVLtoOEZaAqbCF2RDndaA==}

  compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}

  compression@1.7.4:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  concat-stream@1.6.2:
    resolution: {integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==}
    engines: {'0': node >= 0.8}

  concat-stream@2.0.0:
    resolution: {integrity: sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==}
    engines: {'0': node >= 6.0}

  connection-parse@0.0.7:
    resolution: {integrity: sha512-bTTG28diWg7R7/+qE5NZumwPbCiJOT8uPdZYu674brDjBWQctbaQbYlDKhalS+4i5HxIx+G8dZsnBHKzWpp01A==}

  console-control-strings@1.1.0:
    resolution: {integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==}

  content-disposition@0.5.2:
    resolution: {integrity: sha512-kRGRZw3bLlFISDBgwTSA1TMBFN6J6GWDeubmDE3AF+3+yXL8hTWv8r5rkLbqYXY4RjPk/EzHnClI3zQf1cFmHA==}
    engines: {node: '>= 0.6'}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  conventional-changelog-angular@7.0.0:
    resolution: {integrity: sha512-ROjNchA9LgfNMTTFSIWPzebCwOGFdgkEq45EnvvrmSLvCtAw0HSmrCs7/ty+wAeYUZyNay0YMUNYFTRL72PkBQ==}
    engines: {node: '>=16'}

  conventional-changelog-core@5.0.1:
    resolution: {integrity: sha512-Rvi5pH+LvgsqGwZPZ3Cq/tz4ty7mjijhr3qR4m9IBXNbxGGYgTVVO+duXzz9aArmHxFtwZ+LRkrNIMDQzgoY4A==}
    engines: {node: '>=14'}

  conventional-changelog-preset-loader@3.0.0:
    resolution: {integrity: sha512-qy9XbdSLmVnwnvzEisjxdDiLA4OmV3o8db+Zdg4WiFw14fP3B6XNz98X0swPPpkTd/pc1K7+adKgEDM1JCUMiA==}
    engines: {node: '>=14'}

  conventional-changelog-writer@6.0.1:
    resolution: {integrity: sha512-359t9aHorPw+U+nHzUXHS5ZnPBOizRxfQsWT5ZDHBfvfxQOAik+yfuhKXG66CN5LEWPpMNnIMHUTCKeYNprvHQ==}
    engines: {node: '>=14'}
    hasBin: true

  conventional-commits-filter@3.0.0:
    resolution: {integrity: sha512-1ymej8b5LouPx9Ox0Dw/qAO2dVdfpRFq28e5Y0jJEU8ZrLdy0vOSkkIInwmxErFGhg6SALro60ZrwYFVTUDo4Q==}
    engines: {node: '>=14'}

  conventional-commits-parser@4.0.0:
    resolution: {integrity: sha512-WRv5j1FsVM5FISJkoYMR6tPk07fkKT0UodruX4je86V4owk451yjXAKzKAPOs9l7y59E2viHUS9eQ+dfUA9NSg==}
    engines: {node: '>=14'}
    hasBin: true

  conventional-recommended-bump@7.0.1:
    resolution: {integrity: sha512-Ft79FF4SlOFvX4PkwFDRnaNiIVX7YbmqGU0RwccUaiGvgp3S0a8ipR2/Qxk31vclDNM+GSdJOVs2KrsUCjblVA==}
    engines: {node: '>=14'}
    hasBin: true

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie-parser@1.4.7:
    resolution: {integrity: sha512-nGUvgXnotP3BsjiLX2ypbQnWoGUPIIfHQNZkkC668ntrzGWEZVW70HDEB1qnNGMicPje6EttlIgzo51YSwNQGw==}
    engines: {node: '>= 0.8.0'}

  cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}

  cookie@0.3.1:
    resolution: {integrity: sha512-+IJOX0OqlHCszo2mBUq+SrEbCj6w7Kpffqx60zYbPTFaO4+yYgRjHwcZNpWvaTylDHaV7PPmBHzSecZiMhtPgw==}
    engines: {node: '>= 0.6'}

  cookie@0.4.2:
    resolution: {integrity: sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==}
    engines: {node: '>= 0.6'}

  cookie@0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==}
    engines: {node: '>= 0.6'}

  cookie@0.7.2:
    resolution: {integrity: sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==}
    engines: {node: '>= 0.6'}

  cookie@1.0.2:
    resolution: {integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==}
    engines: {node: '>=18'}

  cookiejar@2.0.6:
    resolution: {integrity: sha512-X9IsySmsr1heROBZCpyEYhqJyU7CXNJoVxIlQ5bBb7DskYUx0mQ+g2f7yPYajceZeGJWHQbIfGB6j0hywV/ARQ==}

  cookiejar@2.1.4:
    resolution: {integrity: sha512-LDx6oHrK+PhzLKJU9j5S7/Y3jM/mUHvD/DeI1WQmJn652iPC5Y4TBzC9l+5OMOXlyTTA+SmVUPm0HQUwpD5Jqw==}

  core-js-compat@3.45.1:
    resolution: {integrity: sha512-tqTt5T4PzsMIZ430XGviK4vzYSoeNJ6CXODi6c/voxOT6IZqBht5/EKaSNnYiEjjRYxjVz7DQIsOsY0XNi8PIA==}

  core-js@2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.

  core-util-is@1.0.2:
    resolution: {integrity: sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}

  cosmiconfig@9.0.0:
    resolution: {integrity: sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg==}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  crc@4.3.2:
    resolution: {integrity: sha512-uGDHf4KLLh2zsHa8D8hIQ1H/HtFQhyHrc0uhHBcoKGol/Xnb+MPYfUMw7cvON6ze/GUESTudKayDcJC5HnJv1A==}
    engines: {node: '>=12'}
    peerDependencies:
      buffer: '>=6.0.3'
    peerDependenciesMeta:
      buffer:
        optional: true

  create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}

  cron-parser@4.9.0:
    resolution: {integrity: sha512-p0SaNjrHOnQeR8/VnfGbmg9te2kfyYSQ7Sc/j/6DtPL3JQvKxmjO9TSjNFpujqV3vEYYBvNNvXSxzyksBWAx1Q==}
    engines: {node: '>=12.0.0'}

  cross-spawn@5.1.0:
    resolution: {integrity: sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==}

  cross-spawn@6.0.6:
    resolution: {integrity: sha512-VqCUuhcd1iB+dsv8gxPttb5iZh/D0iubSP21g36KXdEuf6I5JiioesUVjpCdHV9MZRUfVFlvwtIUyPfxo5trtw==}
    engines: {node: '>=4.8'}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  dargs@7.0.0:
    resolution: {integrity: sha512-2iy1EkLdlBzQGvbweYRFxmFath8+K7+AKB0TlhHWkNuH+TmovaMH/Wp7V7R4u7f4SnX3OgLsU9t1NI9ioDnUpg==}
    engines: {node: '>=8'}

  dashdash@1.14.1:
    resolution: {integrity: sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==}
    engines: {node: '>=0.10'}

  data-uri-to-buffer@1.2.0:
    resolution: {integrity: sha512-vKQ9DTQPN1FLYiiEEOQ6IBGFqvjCa5rSK3cWMy/Nespm5d/x3dGFT9UBZnkLxCwua/IXBi2TYnwTEpsOvhC4UQ==}

  data-uri-to-buffer@3.0.1:
    resolution: {integrity: sha512-WboRycPNsVw3B3TL559F7kuBUM4d8CgMEvk6xEJlOp7OBPjt6G7z8WMWlD2rOFZLk6OYfFIUGsCOWzcQH9K2og==}
    engines: {node: '>= 6'}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==}
    engines: {node: '>= 0.4'}

  dateformat@3.0.3:
    resolution: {integrity: sha512-jyCETtSl3VMZMWeRo7iY1FL19ges1t55hMo5yaam4Jrsm5EPL89UQkoQRyiI+Yf4k8r2ZpdngkV8hr1lIdjb3Q==}

  debug@2.2.0:
    resolution: {integrity: sha512-X0rGvJcskG1c3TgSCPqHJ0XJgwlcvOC7elJ5Y0hYuKBZoVqWpAMfLOeIh2UI/DCQ5ruodIjvsugZtjUYUw2pUw==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.1.0:
    resolution: {integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.7:
    resolution: {integrity: sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize-keys@1.1.1:
    resolution: {integrity: sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==}
    engines: {node: '>=0.10.0'}

  decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}

  decamelize@4.0.0:
    resolution: {integrity: sha512-9iE1PgSik9HeIIw2JO94IidnE3eBoQrFJ3w7sFuzSX4DpmZ3v5sZpUiV5Swcf6mQEF+Y0ru8Neo+p+nyh2J+hQ==}
    engines: {node: '>=10'}

  decompress-response@3.3.0:
    resolution: {integrity: sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==}
    engines: {node: '>=4'}

  decompress-response@4.2.1:
    resolution: {integrity: sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==}
    engines: {node: '>=8'}

  dedent@1.5.3:
    resolution: {integrity: sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-eql@4.1.4:
    resolution: {integrity: sha512-SUwdGfqdKOwxCPeVYjwSyRpJ7Z+fhpwIAtmCUdZIWZ/YP5R9WAsyuSgpLVDi9bjWoN2LXHNss/dk3urXtdQxGg==}
    engines: {node: '>=6'}

  deep-extend@0.6.0:
    resolution: {integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==}
    engines: {node: '>=4.0.0'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  default-require-extensions@3.0.1:
    resolution: {integrity: sha512-eXTJmRbm2TIt9MgWTsOH1wEuhew6XGZcMeGKCtLedIg/NCsg1iBePXkceTdK4Fii7pzmN9tGsZhKzZ4h7O/fxw==}
    engines: {node: '>=8'}

  defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==}
    engines: {node: '>=8'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  degenerator@1.0.4:
    resolution: {integrity: sha512-EMAC+riLSC64jKfOs1jp8J7M4ZXstUUwTdwFBEv6HOzL/Ae+eAzMKEK0nJnpof2fnw9IOjmE6u6qXFejVyk8AA==}

  degenerator@3.0.4:
    resolution: {integrity: sha512-Z66uPeBfHZAHVmue3HPfyKu2Q0rC2cRxbTOsvmU/po5fvvcx27W4mIu9n0PUlQih4oUYvcG1BsbtVv8x7KDOSw==}
    engines: {node: '>= 6'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  delegates@1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}

  denque@1.5.1:
    resolution: {integrity: sha512-XwE+iZ4D6ZUB7mfYRMb5wByE8L74HCn30FBN7sWnXksWc1LO1bPDl67pBR9o/kC4z/xSNAwkMYcGgqDV3BE3Hw==}
    engines: {node: '>=0.10'}

  denque@2.1.0:
    resolution: {integrity: sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==}
    engines: {node: '>=0.10'}

  depd@1.0.1:
    resolution: {integrity: sha512-OEWAMbCkK9IWQ8pfTvHBhCSqHgR+sk5pbiYqq0FqfARG4Cy+cRsCbITx6wh5pcsmfBPiJAcbd98tfdz5fnBbag==}
    engines: {node: '>= 0.6'}

  depd@1.1.1:
    resolution: {integrity: sha512-Jlk9xvkTDGXwZiIDyoM7+3AsuvJVoyOpRupvEVy9nX3YO3/ieZxhlgh8GpLNZ8AY7HjO6y2YwpMSh1ejhu3uIw==}
    engines: {node: '>= 0.6'}

  depd@1.1.2:
    resolution: {integrity: sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==}
    engines: {node: '>= 0.6'}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  deprecate@1.0.0:
    resolution: {integrity: sha512-3OFRdih9G2VYAfA/H6vizXyCvwLGLr9hdAh1z39HY3lAKbi2/BELQDVhbZ6AaTOjdh5BAi/GJ1QphQBHjCyXVw==}

  deprecation@2.3.1:
    resolution: {integrity: sha512-xmHIy4F3scKVwMsQ4WnVaS8bHOx0DmVwRywosKhaILI0ywMDWPtBSku2HNxRvF7jtwDRsoEwYQSfbxj8b7RlJQ==}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  destroy@1.0.4:
    resolution: {integrity: sha512-3NdhDuEXnfun/z7x9GOElY49LoqVHoGScmOKwmxhsS8N5Y+Z8KyPPDnaSzqWgYt/ji4mqwfTS34Htrk0zPIXVg==}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-indent@5.0.0:
    resolution: {integrity: sha512-rlpvsxUtM0PQvy9iZe640/IWwWYyBsTApREbA1pHOpmOUIl9MkP/U4z7vTtg4Oaojvqhxt7sdufnT0EzGaR31g==}
    engines: {node: '>=4'}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  dezalgo@1.0.4:
    resolution: {integrity: sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==}

  dicer@0.2.5:
    resolution: {integrity: sha512-FDvbtnq7dzlPz0wyYlOExifDEZcu8h+rErEXgfxqmLfRfC/kJidEFh4+effJRO3P0xmfqyPbSMG0LveNRfTKVg==}
    engines: {node: '>=0.8.0'}

  diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  diff@4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==}
    engines: {node: '>=0.3.1'}

  diff@5.2.0:
    resolution: {integrity: sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==}
    engines: {node: '>=0.3.1'}

  dijkstrajs@1.0.3:
    resolution: {integrity: sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  dom-walk@0.1.2:
    resolution: {integrity: sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==}

  dot-prop@5.3.0:
    resolution: {integrity: sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==}
    engines: {node: '>=8'}

  dotenv-expand@11.0.7:
    resolution: {integrity: sha512-zIHwmZPRshsCdpMDyVsqGmgyP0yT8GAgXUnkdAoJisxvf33k7yO6OuoKmcTGuXPWSsm8Oh88nZicRLA9Y0rUeA==}
    engines: {node: '>=12'}

  dotenv@16.4.7:
    resolution: {integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==}
    engines: {node: '>=12'}

  dotenv@16.6.1:
    resolution: {integrity: sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==}
    engines: {node: '>=12'}

  dottie@2.0.6:
    resolution: {integrity: sha512-iGCHkfUc5kFekGiqhe8B/mdaurD+lakO9txNnTvKtA6PISrw86LgqHvRzWYPyoE2Ph5aMIrCw9/uko6XHTKCwA==}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  duplexer2@0.1.4:
    resolution: {integrity: sha512-asLFVfWWtJ90ZyOUHMqk7/S2w2guQKxUI2itj3d92ADHhxUSbCMGi1f1cBcJ7xM1To+pE/Khbwo1yuNbMEPKeA==}

  duplexer@0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}

  duplexify@3.7.1:
    resolution: {integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==}

  duplexify@4.1.3:
    resolution: {integrity: sha512-M3BmBhwJRZsSx38lZyhE53Csddgzl5R7xGJNk7CVddZD6CcmwMCH8J+7AprIrQKH7TonKxaCjcv27Qmf+sQ+oA==}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  ecc-jsbn@0.1.2:
    resolution: {integrity: sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==}

  ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==}

  ee-first@1.1.0:
    resolution: {integrity: sha512-n4X/DaHVKHyDy1Rwuzm1UPjTRIBSarj1BBZ5R5HLOFLn58yhw510qoF1zk94jjkw3mXScdsmMtYCNR1jsAJlEA==}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  ejs@3.1.10:
    resolution: {integrity: sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  electron-to-chromium@1.5.215:
    resolution: {integrity: sha512-TIvGp57UpeNetj/wV/xpFNpWGb0b/ROw372lHPx5Aafx02gjTBtWnEEcaSX3W2dLM3OSdGGyHX/cHl01JQsLaQ==}

  emitter-listener@1.1.2:
    resolution: {integrity: sha512-Bt1sBAGFHY9DKY+4/2cV6izcKJUf5T7/gkdmkxzX/qv9CcGH8xSwVRW5mtX03SWJtRTWSOpzCuWN9rBFYZepZQ==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}

  encoding-negotiator@2.0.1:
    resolution: {integrity: sha512-GSK7qphNR4iPcejfAlZxKDoz3xMhnspwImK+Af5WhePS9jUpK/Oh7rUdyENWu+9rgDflOCTmAojBsgsvM8neAQ==}
    engines: {node: '>=10.13.0'}

  encoding@0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}

  end-of-stream@1.4.5:
    resolution: {integrity: sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==}

  engine.io-client@3.2.1:
    resolution: {integrity: sha512-y5AbkytWeM4jQr7m/koQLc5AxpRKC1hEVUb/s1FUAWEJq5AzJJ4NLvzuKPuxtDi5Mq755WuDvZ6Iv2rXj4PTzw==}

  engine.io-parser@2.1.3:
    resolution: {integrity: sha512-6HXPre2O4Houl7c4g7Ic/XzPnHBvaEmN90vtRO9uLmwtRqQmTOw0QMevL1TOfL2Cpu1VzsaTmMotQgMdkzGkVA==}

  engine.io-parser@5.2.3:
    resolution: {integrity: sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==}
    engines: {node: '>=10.0.0'}

  engine.io@3.2.1:
    resolution: {integrity: sha512-+VlKzHzMhaU+GsCIg4AoXF1UdDFjHHwMmMKqMJNDNLlUlejz58FCy4LBqB2YVJskHGYl06BatYWKP2TVdVXE5w==}

  engine.io@6.6.4:
    resolution: {integrity: sha512-ZCkIjSYNDyGn0R6ewHDtXgns/Zre/NT6Agvq1/WobF7JXgFff4SeDroKiCO3fNJreU9YG429Sc81o4w5ok/W5g==}
    engines: {node: '>=10.2.0'}

  enquirer@2.3.6:
    resolution: {integrity: sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg==}
    engines: {node: '>=8.6'}

  env-paths@2.2.1:
    resolution: {integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==}
    engines: {node: '>=6'}

  envinfo@7.13.0:
    resolution: {integrity: sha512-cvcaMr7KqXVh4nyzGTVqTum+gAiL265x5jUWQIDLq//zOGbW+gSW/C+OWLleY/rs9Qole6AZLMXPbtIFQbqu+Q==}
    engines: {node: '>=4'}
    hasBin: true

  err-code@2.0.3:
    resolution: {integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==}

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  es-abstract@1.24.0:
    resolution: {integrity: sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==}
    engines: {node: '>= 0.4'}

  es6-error@4.1.1:
    resolution: {integrity: sha512-Um/+FxMr9CISWh0bi5Zv0iOD+4cFh5qLeks1qhAopKVAJw3drgKbKySikp7wGhDL0HPeaja0P5ULZrxLkniUVg==}

  es6-promise@3.3.1:
    resolution: {integrity: sha512-SOp9Phqvqn7jtEUxPWdWfWoLmyt2VaJ6MpvP9Comy1MceMXqE6bxvaTu4iaxpYYPzhny28Lc+M87/c2cPK6lDg==}

  es6-promise@4.2.8:
    resolution: {integrity: sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w==}

  es6-promisify@5.0.0:
    resolution: {integrity: sha512-C+d6UdsYDk0lMebHNR4S2NybQMMngAOnOwYBQjTOiv0MkoJMP0Myw2mgpDLBcpfCmRLxyFqYhS/CfOENq4SJhQ==}

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  escodegen@1.14.3:
    resolution: {integrity: sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==}
    engines: {node: '>=4.0'}
    hasBin: true

  eslint-plugin-sonarjs@3.0.5:
    resolution: {integrity: sha512-dI62Ff3zMezUToi161hs2i1HX1ie8Ia2hO0jtNBfdgRBicAG4ydy2WPt0rMTrAe3ZrlqhpAO3w1jcQEdneYoFA==}
    peerDependencies:
      eslint: ^8.0.0 || ^9.0.0

  eslint-plugin-unicorn@58.0.0:
    resolution: {integrity: sha512-fc3iaxCm9chBWOHPVjn+Czb/wHS0D2Mko7wkOdobqo9R2bbFObc4LyZaLTNy0mhZOP84nKkLhTUQxlLOZ7EjKw==}
    engines: {node: ^18.20.0 || ^20.10.0 || >=21.0.0}
    peerDependencies:
      eslint: '>=9.22.0'

  eslint-scope@8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.35.0:
    resolution: {integrity: sha512-QePbBFMJFjgmlE+cXAlbHZbHpdFVS2E/6vzCy7aKlebddvl1vadiC4JFV5u/wqTkNUwEV8WrQi257jf5f06hrg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esprima@3.1.3:
    resolution: {integrity: sha512-AWwVMNxwhN8+NIPQzAQZCm7RkLC4RbM3B1OobMuyp3i+w73X57KCKaVIxaRZb+DYCojq7rspo+fmuQfAboyhFg==}
    engines: {node: '>=4'}
    hasBin: true

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  execa@1.0.0:
    resolution: {integrity: sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==}
    engines: {node: '>=6'}

  execa@5.0.0:
    resolution: {integrity: sha512-ov6w/2LCiuyO4RLYGdpFGjkcs0wMTgGE8PrkTHikeUy5iJekXyPIKUjifk5CsE0pt7sMCrMZ3YNqoCj6idQOnQ==}
    engines: {node: '>=10'}

  exif-parser@0.1.12:
    resolution: {integrity: sha512-c2bQfLNbMzLPmzQuOr8fy0csy84WmwnER81W88DzTp9CYNPJ6yzOj2EZAh9pywYpqHnshVLHQJ8WzldAyfY+Iw==}

  exit@0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==}
    engines: {node: '>= 0.8.0'}

  expand-template@2.0.3:
    resolution: {integrity: sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==}
    engines: {node: '>=6'}

  exponential-backoff@3.1.2:
    resolution: {integrity: sha512-8QxYTVXUkuy7fIIoitQkPwGonB8F3Zj8eEO8Sqg9Zv/bkI7RJAzowee4gr81Hak/dUTpA2Z7VfQgoijjPNlUZA==}

  express-csv@0.6.0:
    resolution: {integrity: sha512-rJxnda4SqxYyAJ8fnAoF0zzxMvHgjFCKnkStrHE0VVLSNgjZESRQprC0Buax0D4V9EuR4HRmZfCH5IS46cxEow==}
    engines: {node: 0.6 || 0.8 || 0.10}

  express-http-proxy@2.1.1:
    resolution: {integrity: sha512-4aRQRqDQU7qNPV5av0/hLcyc0guB9UP71nCYrQEYml7YphTo8tmWf3nDZWdTJMMjFikyz9xKXaURor7Chygdwg==}
    engines: {node: '>=6.0.0'}

  express-mung@0.5.1:
    resolution: {integrity: sha512-5M8Oi2s9ePuP86YMyLT+hefWPPGxuxW3Vnuni/Hq3r2nGE0L4qulG0HkWuc4PIOpzZuZTwONwyfBFtR2xlnfgg==}

  express-prom-bundle@6.6.0:
    resolution: {integrity: sha512-tZh2P2p5a8/yxQ5VbRav011Poa4R0mHqdFwn9Swe/obXDe5F0jY9wtRAfNYnqk4LXY7akyvR/nrvAHxQPWUjsQ==}
    engines: {node: '>=10'}
    peerDependencies:
      prom-client: '>=12.0.0'

  express-prom-bundle@7.0.2:
    resolution: {integrity: sha512-ffFV4HGHvCKnkNJFqm42sYztRJE5mLgOj8MpGey1HOatuFhtcwXoBD2m5gca7ZbcyjkIf7lOH5ZdrhlrBf0sGw==}
    engines: {node: '>=18'}
    peerDependencies:
      prom-client: '>=15.0.0'

  express-validator@5.3.1:
    resolution: {integrity: sha512-g8xkipBF6VxHbO1+ksC7nxUU7+pWif0+OZXjZTybKJ/V0aTVhuCoHbyhIPgSYVldwQLocGExPtB2pE0DqK4jsw==}
    engines: {node: '>= 6.0.0'}

  express@4.16.2:
    resolution: {integrity: sha512-4mc9RUEAUpPMFR6gpXcnPt0/q2Zil35FTUr07ixWYX90RmUKL3jUbvTvJzkc/uL3r+A7kuWSiIqOyVUSWoZXWQ==}
    engines: {node: '>= 0.10.0'}

  express@4.21.1:
    resolution: {integrity: sha512-YSFlK1Ee0/GC8QaO91tHcDxJiE/X4FbpAyQWkxAvG6AXCuR65YzK8ua6D9hvi/TzUfZMpc+BwuM1IPw8fmQBiQ==}
    engines: {node: '>= 0.10.0'}

  extend@3.0.0:
    resolution: {integrity: sha512-5mYyg57hpD+sFaJmgNL9BidQ5C7dmJE3U5vzlRWbuqG+8dytvYEoxvKs6Tj5cm3LpMsFvRt20qz1ckezmsOUgQ==}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  extsprintf@1.3.0:
    resolution: {integrity: sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==}
    engines: {'0': node >=0.6.0}

  factory-girl@5.0.2:
    resolution: {integrity: sha512-8aRCPqMjGgsq3Piz+hCK8ukqYg8rcn1GQHe/P1b4Dvzg/k7vrGfF98vQu0k+8BNuu5+DDjLDWwbKUclkxPV04w==}

  fast-decode-uri-component@1.0.1:
    resolution: {integrity: sha512-WKgKWg5eUxvRZGwW8FvfbaH7AXSh2cL+3j5fMGzUMCxWBJ3dV3a7Wz8y2f/uQ0e3B6WmodD3oS54jTQ9HVTIIg==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-json-stringify@1.21.0:
    resolution: {integrity: sha512-xY6gyjmHN3AK1Y15BCbMpeO9+dea5ePVsp3BouHCdukcx0hOHbXwFhRodhcI0NpZIgDChSeAKkHW9YjKvhwKBA==}

  fast-json-stringify@6.0.1:
    resolution: {integrity: sha512-s7SJE83QKBZwg54dIbD5rCtzOBVD43V1ReWXXYqBgwCwHLYAAT0RQc/FmrQglXqWPpz6omtryJQOau5jI4Nrvg==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-printf@1.6.10:
    resolution: {integrity: sha512-GwTgG9O4FVIdShhbVF3JxOgSBY2+ePGsu2V/UONgoCPzF9VY6ZdBMKsHKCYQHZwNk3qNouUolRDsgVxcVA5G1w==}
    engines: {node: '>=10.0'}

  fast-querystring@1.1.2:
    resolution: {integrity: sha512-g6KuKWmFXc0fID8WWH0jit4g0AGBoJhCkJMb1RmbsSEUNvQ+ZC8D6CUZ+GtF8nMzSPXnhiePyyqqipzNNEnHjg==}

  fast-redact@2.1.0:
    resolution: {integrity: sha512-0LkHpTLyadJavq9sRzzyqIoMZemWli77K2/MGOkafrR64B9ItrvZ9aT+jluvNDsv0YEHjSNhlMBtbokuoqii4A==}
    engines: {node: '>=6'}

  fast-redact@3.5.0:
    resolution: {integrity: sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==}
    engines: {node: '>=6'}

  fast-safe-stringify@1.1.13:
    resolution: {integrity: sha512-i5cPAGwAviF/sYF1z3Sq1DaZt/hye7MQAHqpg9eCF1qCOolpSNeYSBYFRVIdzOW7L4A+/XznDxbLQSyygnUCDQ==}

  fast-safe-stringify@2.1.1:
    resolution: {integrity: sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==}

  fast-uri@3.1.0:
    resolution: {integrity: sha512-iPeeDKJSWf4IEOasVVrknXpaBV0IApz/gp7S2bb7Z4Lljbl2MGJRqInZiUrQwV16cpzw/D3S5j5Julj/gT52AA==}

  fast-xml-parser@4.4.1:
    resolution: {integrity: sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==}
    hasBin: true

  fastify-compress@2.0.1:
    resolution: {integrity: sha512-3i3ZuRPZ34AjExGSxr5/YzrlnAftFfB0H7+mhelSbd6oHNl0lFDosdrVNemR23+xjzVPPCv4aGMxSvdB3nQc+Q==}

  fastify-plugin@1.6.1:
    resolution: {integrity: sha512-APBcb27s+MjaBIerFirYmBLatoPCgmHZM6XP0K+nDL9k0yX8NJPWDY1RAC3bh6z+AB5ULS2j31BUfLMT3uaZ4A==}

  fastify-plugin@5.0.1:
    resolution: {integrity: sha512-HCxs+YnRaWzCl+cWRYFnHmeRFyR5GVnJTAaCJQiYzQSDwK9MgJdyAsuL3nh0EWRCYMgQ5MeziymvmAhUHYHDUQ==}

  fastify@2.15.3:
    resolution: {integrity: sha512-2O+A9SjHpbH/SgDDMA+xIznhx/rDeNuwPIiZSFVU7fwOiiFfQjHmfu21jp22wMmsZ5PYKYFR+pze2TzoAUmOtw==}
    engines: {node: '>=6'}

  fastify@5.3.0:
    resolution: {integrity: sha512-vDpCJa4KRkHrdDMpDNtyPaIDi/ptCwoJ0M8RiefuIMvyXTgG63xYGe9DYYiCpydjh0ETIaLoSyKBNKkh7ew1eA==}

  fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}

  fclone@1.0.11:
    resolution: {integrity: sha512-GDqVQezKzRABdeqflsgMr7ktzgF9CyS+p2oe0jJqUY6izSSbhPIQJDpoU4PtGcD7VPM9xh/dVrTu6z1nwgmEGw==}

  figures@3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  file-type@3.9.0:
    resolution: {integrity: sha512-RLoqTXE8/vPmMuTI88DAzhMYC99I8BWv7zYP4A1puo5HIjEJ5EX48ighy4ZyKMG9EDXxBgW6e++cn7d1xuFghA==}
    engines: {node: '>=0.10.0'}

  file-uri-to-path@1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}

  file-uri-to-path@2.0.0:
    resolution: {integrity: sha512-hjPFI8oE/2iQPVe4gbrJ73Pp+Xfub2+WI2LlXDbsaJBwT5wuMh35WNWVYYTpnz895shtwfyutMFLFywpQAFdLg==}
    engines: {node: '>= 6'}

  filelist@1.0.4:
    resolution: {integrity: sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  finalhandler@1.1.0:
    resolution: {integrity: sha512-ejnvM9ZXYzp6PUPUyQBMBf0Co5VX2gr5H2VQe2Ui2jWXNlxv+PYZo8wpAymJNJdLsG1R4p+M4aynF8KuoUEwRw==}
    engines: {node: '>= 0.8'}

  finalhandler@1.3.1:
    resolution: {integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==}
    engines: {node: '>= 0.8'}

  find-cache-dir@3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==}
    engines: {node: '>=8'}

  find-my-way@2.2.5:
    resolution: {integrity: sha512-GjRZZlGcGmTh9t+6Xrj5K0YprpoAFCAiCPgmAH9Kb09O4oX6hYuckDfnDipYj+Q7B1GtYWSzDI5HEecNYscLQg==}
    engines: {node: '>=6'}

  find-my-way@9.3.0:
    resolution: {integrity: sha512-eRoFWQw+Yv2tuYlK2pjFS2jGXSxSppAs3hSQjfxVKxM5amECzIgYYc1FEI8ZmhSh/Ig+FrKEz43NLRKJjYCZVg==}
    engines: {node: '>=20'}

  find-up-simple@1.0.1:
    resolution: {integrity: sha512-afd4O7zpqHeRyg4PfDQsXmlDe2PfdHtJt6Akt8jOWaApLOZk5JXs6VMR29lz03pRe9mpykrRCYIYxaJYcfpncQ==}
    engines: {node: '>=18'}

  find-up@1.1.2:
    resolution: {integrity: sha512-jvElSjyuo4EMQGoTwo1uJU5pQMwTW5lS1x05zzfJuTIyLR3zwO27LYrxNg+dlvKpGOuGy/MzBdXh80g0ve5+HA==}
    engines: {node: '>=0.10.0'}

  find-up@2.1.0:
    resolution: {integrity: sha512-NWzkk0jSJtTt08+FBFMvXoeZnOJD+jTtsRmBYbAIzJdX6l7dLgR7CTubCM5/eDdPUBvLCeVasP1brfVR/9/EZQ==}
    engines: {node: '>=4'}

  find-up@3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==}
    engines: {node: '>=6'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==}
    hasBin: true

  flatstr@1.0.12:
    resolution: {integrity: sha512-4zPxDyhCyiN2wIAtSLI6gc82/EjqZc1onI4Mz/l0pWrAlsSfYH/2ZIcU+e3oA2wDwbzIWNKwa23F8rh6+DRWkw==}

  flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}

  follow-redirects@1.15.11:
    resolution: {integrity: sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution: {integrity: sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==}
    engines: {node: '>= 0.4'}

  foreground-child@2.0.0:
    resolution: {integrity: sha512-dCIq9FpEcyQyXKCkyzmlPTFNgrCzPudOe+mhvJU5zAtlBnGVy2yKxtfsxK2tQBThwq225jcvBjpw1Gr40uzZCA==}
    engines: {node: '>=8.0.0'}

  foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}

  forever-agent@0.6.1:
    resolution: {integrity: sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==}

  form-data@1.0.0-rc3:
    resolution: {integrity: sha512-Z5JWXWsFDI8x73Rt/Dc7SK/EvKBzudhqIVBtEhcAhtoevCTqO3YJmctGBLzT0Ggg39xFcefkXt00t1TYLz6D0w==}
    engines: {node: '>= 0.10'}

  form-data@2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==}
    engines: {node: '>= 0.12'}

  form-data@2.5.5:
    resolution: {integrity: sha512-jqdObeR2rxZZbPSGL+3VckHMYtu+f9//KXBsVny6JSX/pa38Fy+bGjuG8eW/H6USNQWhLi8Num++cU2yOCNz4A==}
    engines: {node: '>= 0.12'}

  form-data@4.0.4:
    resolution: {integrity: sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==}
    engines: {node: '>= 6'}

  formidable@1.0.17:
    resolution: {integrity: sha512-95MFT5qipMvUiesmuvGP1BI4hh5XWCzyTapiNJ/k8JBQda7rPy7UCWYItz2uZEdTgGNy1eInjzlL9Wx1O9fedg==}
    engines: {node: '>=0.8.0'}
    deprecated: 'Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau'

  formidable@1.2.6:
    resolution: {integrity: sha512-KcpbcpuLNOwrEjnbpMC0gS+X8ciDoZE1kkqzat4a8vrprf+s9pKNQ/QIwWfbfs4ltgmFl3MD177SNTkve3BwGQ==}
    deprecated: 'Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau'

  formidable@2.1.5:
    resolution: {integrity: sha512-Oz5Hwvwak/DCaXVVUtPn4oLMLLy1CdclLKO1LFgU7XzDpVMUU5UjlSLpGMocyQNNk8F6IJW9M/YdooSn2MRI+Q==}

  formidable@3.5.4:
    resolution: {integrity: sha512-YikH+7CUTOtP44ZTnUhR7Ic2UASBPOqmaRkRKxRbywPTe5VxF7RRCck4af9wutiZ/QKM5nME9Bie2fFaPz5Gug==}
    engines: {node: '>=14.0.0'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  from2@2.3.0:
    resolution: {integrity: sha512-OMcX/4IC/uqEPVgGeyfN22LJk6AZrMkRZHxcHBMBvHScDGgwTm2GT2Wkgtocyd3JfZffjj2kYUDXXII0Fk9W0g==}

  fromentries@1.3.2:
    resolution: {integrity: sha512-cHEpEQHUg0f8XdtZCc2ZAhrHzKzT0MrFUTcvx+hfxYu7rGMDc5SKoXFh+n4YigxsHXRzc6OrCshdR1bWH6HHyg==}

  front-matter@4.0.2:
    resolution: {integrity: sha512-I8ZuJ/qG92NWX8i5x1Y8qyj3vizhXS31OxjKDu3LKP+7/qBgfIKValiZIEwoVoJKUHlhWtYrktkxV1XsX+pPlg==}

  fs-constants@1.0.0:
    resolution: {integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==}

  fs-extra@11.3.1:
    resolution: {integrity: sha512-eXvGGwZ5CL17ZSwHWd3bbgk7UUpF6IFHtP57NYYakPvHOs8GDgDe5KJI36jIJzDkJ6eJjuzRA8eBQb6SkKue0g==}
    engines: {node: '>=14.14'}

  fs-extra@8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}

  fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}

  fs-minipass@3.0.3:
    resolution: {integrity: sha512-XUBA9XClHbnJWSfBzjkm6RvPsyg3sryZt06BEQoXcF7EK/xpGaQYJgQKDJSUH5SGZ76Y7pFx1QBnXz09rU5Fbw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fstream@1.0.12:
    resolution: {integrity: sha512-WvJ193OHa0GHPEL+AycEJgxvBEwyfRkN1vhjca23OaPVMCaLCXTd5qAu82AjTcgP1UJmytkOKb63Ypde7raDIg==}
    engines: {node: '>=0.6'}
    deprecated: This package is no longer supported.

  ftp@0.3.10:
    resolution: {integrity: sha512-faFVML1aBx2UoDStmLwv2Wptt4vw5x03xxX172nhA5Y5HBshW5JweqQ2W4xL4dezQTG8inJsuYcpPHHU3X5OTQ==}
    engines: {node: '>=0.8.0'}

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==}
    engines: {node: '>= 0.4'}

  functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  gauge@2.7.4:
    resolution: {integrity: sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==}
    deprecated: This package is no longer supported.

  gaxios@6.7.1:
    resolution: {integrity: sha512-LDODD4TMYx7XXdpwxAVRAIAuB0bzv0s+ywFonY46k126qzQHT9ygyoa9tncmOiQmmDrik65UYsEkv3lbfqQ3yQ==}
    engines: {node: '>=14'}

  gcp-metadata@6.1.1:
    resolution: {integrity: sha512-a4tiq7E0/5fTjxPAaH4jpjkSv/uCaU2p5KC6HVGrvl0cDjA8iBZv4vv1gyzlmK0ZUKqwpOyQMKzZQe3lTit77A==}
    engines: {node: '>=14'}

  gelf-stream-renewed@1.2.2:
    resolution: {integrity: sha512-p/rPS8tx1XiGThhh3Ky/SJ/zQ4E3OXuFY2BSyhBa6WBagGHbndRahzcGe1TTip5TwyfP0F9eiy/Bsu0iyc+lDg==}

  gelfling@0.3.1:
    resolution: {integrity: sha512-vli3D2RYpLW6XhryNrv7HMjFNbj+ks/CCVDjokxOtZ+p6QYRadj8Zc0ps+LolSsh/I97XO0OduP/ShOej08clA==}

  generic-pool@3.9.0:
    resolution: {integrity: sha512-hymDOu5B53XvN4QT9dBmZxPX4CWhBPPLguTZ9MMFeFa/Kg0xWVfylOVNlJji/E7yTZWFd/q9GO5TxDLq156D7g==}
    engines: {node: '>= 4'}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@1.0.3:
    resolution: {integrity: sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w==}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-func-name@2.0.2:
    resolution: {integrity: sha512-8vXOvuE167CtIc3OyItco7N/dpRtBbYOsPsXCz7X/PMnlGjYjSGuZJgM1Y7mmew7BKf9BqvLX2tnOVy1BBUsxQ==}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-package-type@0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}

  get-pkg-repo@4.2.1:
    resolution: {integrity: sha512-2+QbHjFRfGB74v/pYWjd5OhU3TDIC2Gv/YKUTk/tCvAz0pkn/Mz6P3uByuBimLOcPvN2jYdScl3xGFSrx0jEcA==}
    engines: {node: '>=6.9.0'}
    hasBin: true

  get-port@5.1.1:
    resolution: {integrity: sha512-g/Q1aTSDOxFpchXC4i8ZWvxA1lnPqx/JHqcpIw0/LX9T8x/GBbi6YnlN5nhaKIFkT8oFsscUKgDJYxfwfS6QsQ==}
    engines: {node: '>=8'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-stream@2.3.1:
    resolution: {integrity: sha512-AUGhbbemXxrZJRD5cDvKtQxLuYaIbNtDTK8YqupCI393Q2KSTreEsLUN3ZxAWFGiKTzL6nKuzfcIvieflUX9qA==}
    engines: {node: '>=0.10.0'}

  get-stream@4.1.0:
    resolution: {integrity: sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==}
    engines: {node: '>=6'}

  get-stream@6.0.0:
    resolution: {integrity: sha512-A1B3Bh1UmL0bidM/YX2NsCOTnGJePL9rO/M+Mw3m9f2gUpfokS0hi5Eah0WSUEWZdZhIZtMjkIYS7mDfOqNHbg==}
    engines: {node: '>=10'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==}
    engines: {node: '>= 0.4'}

  get-uri@2.0.4:
    resolution: {integrity: sha512-v7LT/s8kVjs+Tx0ykk1I+H/rbpzkHvuIq87LmeXptcf5sNWm9uQiwjNAt94SJPA1zOlCntmnOlJvVWKmzsxG8Q==}

  get-uri@3.0.2:
    resolution: {integrity: sha512-+5s0SJbGoyiJTZZ2JTpFPLMPSch72KEqGOTvQsBqg0RBWvwhWUSYZFAtz3TPW0GXJuLBJPts1E241iHg+VRfhg==}
    engines: {node: '>= 6'}

  getpass@0.1.7:
    resolution: {integrity: sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==}

  git-raw-commits@3.0.0:
    resolution: {integrity: sha512-b5OHmZ3vAgGrDn/X0kS+9qCfNKWe4K/jFnhwzVWWg0/k5eLa3060tZShrRg8Dja5kPc+YjS0Gc6y7cRr44Lpjw==}
    engines: {node: '>=14'}
    hasBin: true

  git-remote-origin-url@2.0.0:
    resolution: {integrity: sha512-eU+GGrZgccNJcsDH5LkXR3PB9M958hxc7sbA8DFJjrv9j4L2P/eZfKhM+QD6wyzpiv+b1BpK0XrYCxkovtjSLw==}
    engines: {node: '>=4'}

  git-semver-tags@5.0.1:
    resolution: {integrity: sha512-hIvOeZwRbQ+7YEUmCkHqo8FOLQZCEn18yevLHADlFPZY02KJGsu5FZt9YW/lybfK2uhWFI7Qg/07LekJiTv7iA==}
    engines: {node: '>=14'}
    hasBin: true

  git-up@7.0.0:
    resolution: {integrity: sha512-ONdIrbBCFusq1Oy0sC71F5azx8bVkvtZtMJAsv+a6lz5YAmbNnLD6HAB4gptHZVLPR8S2/kVN6Gab7lryq5+lQ==}

  git-url-parse@14.0.0:
    resolution: {integrity: sha512-NnLweV+2A4nCvn4U/m2AoYu0pPKlsmhK9cknG7IMwsjFY1S2jxM+mAhsDxyxfCIGfGaD+dozsyX4b6vkYc83yQ==}

  gitconfiglocal@1.0.0:
    resolution: {integrity: sha512-spLUXeTAVHxDtKsJc8FkFVgFtMdEN9qPGpL23VfSHx4fP4+Ds097IXLvymbnDH8FnmxX5Nr9bPw3A+AQ6mWEaQ==}

  github-from-package@0.0.0:
    resolution: {integrity: sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  glob@11.0.3:
    resolution: {integrity: sha512-2Nim7dha1KVkaiF4q6Dj+ngPPMdfvLJEOpZk/jKiUAkqKebpGAWQXAq9z1xu9HKu5lWfqw/FASuccEjyznjPaA==}
    engines: {node: 20 || >=22}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  glob@8.1.0:
    resolution: {integrity: sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==}
    engines: {node: '>=12'}
    deprecated: Glob versions prior to v9 are no longer supported

  glob@9.3.5:
    resolution: {integrity: sha512-e1LleDykUz2Iu+MTYdkSsuWX8lvAjAcs0Xef0lNIu0S2wOAzuTxCJtcd9S3cijlwYF18EsU3rzb8jPVobxDh9Q==}
    engines: {node: '>=16 || 14 >=14.17'}

  global@4.4.0:
    resolution: {integrity: sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globals@16.4.0:
    resolution: {integrity: sha512-ob/2LcVVaVGCYN+r14cnwnoDPUufjiYgSqRhiFD0Q1iI4Odora5RE8Iv1D24hAz5oMophRGkGz+yuvQmmUMnMw==}
    engines: {node: '>=18'}

  globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}

  globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}

  google-auth-library@9.15.1:
    resolution: {integrity: sha512-Jb6Z0+nvECVz+2lzSMt9u98UsoakXxA2HGHMCxh+so3n90XgYWkq5dur19JAJV7ONiJY22yBTyJB1TSkvPq9Ng==}
    engines: {node: '>=14'}

  google-logging-utils@0.0.2:
    resolution: {integrity: sha512-NEgUnEcBiP5HrPzufUkBzJOD/Sxsco3rLNo1F1TNf7ieU8ryUzBhqba8r756CjLX7rn3fHl6iLEwPYuqpoKgQQ==}
    engines: {node: '>=14'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  graphlib@2.1.8:
    resolution: {integrity: sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==}

  gtoken@7.1.0:
    resolution: {integrity: sha512-pCcEwRi+TKpMlxAQObHDQ56KawURgyAf6jtIY046fJ5tIv3zDe/LEIubckAO8fj6JnAxLdmWkUfNyulQ2iKdEw==}
    engines: {node: '>=14.0.0'}

  handlebars@4.7.8:
    resolution: {integrity: sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ==}
    engines: {node: '>=0.4.7'}
    hasBin: true

  har-schema@2.0.0:
    resolution: {integrity: sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==}
    engines: {node: '>=4'}

  har-validator@5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported

  hard-rejection@2.1.0:
    resolution: {integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==}
    engines: {node: '>=6'}

  has-ansi@2.0.0:
    resolution: {integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==}
    engines: {node: '>=0.10.0'}

  has-bigints@1.1.0:
    resolution: {integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==}
    engines: {node: '>= 0.4'}

  has-binary2@1.0.3:
    resolution: {integrity: sha512-G1LWKhDSvhGeAQ8mPVQlqNcOB2sJdwATtZKl2pDKKHfpf/rYj24lkinxf69blJbnsvtqqNU+L3SL50vzZhXOnw==}

  has-cors@1.1.0:
    resolution: {integrity: sha512-g5VNKdkFuUuVCP9gYfDJHjK2nqdQJ7aDLTnycnc2+RvsOQbuLdF5pm7vuE5J76SEBIQjs4kQY/BWq74JUmjbXA==}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.2.0:
    resolution: {integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  has-unicode@2.0.1:
    resolution: {integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==}

  hasha@5.2.2:
    resolution: {integrity: sha512-Hrp5vIK/xr5SkeN2onO32H0MgNZ0f17HRNH39WfL0SYUNOTZ5Lz1TJ8Pajo/87dYGEFlLMm7mIc/k/s6Bvz9HQ==}
    engines: {node: '>=8'}

  hashids@2.3.0:
    resolution: {integrity: sha512-ljM73TE/avEhNnazxaj0Dw3BbEUuLC5yYCQ9RSkSUcT4ZSU6ZebdKCIBJ+xT/DnSYW36E9k82GH1Q6MydSIosQ==}

  hashring@3.2.0:
    resolution: {integrity: sha512-xCMovURClsQZ+TR30icCZj+34Fq1hs0y6YCASD6ZqdRfYRybb5Iadws2WS+w09mGM/kf9xyA5FCdJQGcgcraSA==}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  hosted-git-info@2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==}

  hosted-git-info@4.1.0:
    resolution: {integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==}
    engines: {node: '>=10'}

  hosted-git-info@7.0.2:
    resolution: {integrity: sha512-puUZAUKT5m8Zzvs72XWy3HtvVbTWljRE66cP60bxJzAqf2DgICo7lYTY2IHUmLnNpjYvw5bvmoHvPc0QO2a62w==}
    engines: {node: ^16.14.0 || >=18.0.0}

  html-entities@2.6.0:
    resolution: {integrity: sha512-kig+rMn/QOVRvr7c86gQ8lWXq+Hkv6CbAH1hLu+RG338StTpE8Z0b44SDVaqVu7HGKf27frdmUYEs9hTUX/cLQ==}

  html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  http-cache-semantics@4.2.0:
    resolution: {integrity: sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==}

  http-errors@1.6.2:
    resolution: {integrity: sha512-STnYGcKMXL9CGdtpeTFnLmgMSHTTNQJSHxiC4DETHKf934Q160Ht5pljrNeH24S0O9xUN+9vsDJZdZtk5js6Ww==}
    engines: {node: '>= 0.6'}

  http-errors@1.6.3:
    resolution: {integrity: sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==}
    engines: {node: '>= 0.6'}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  http-proxy-agent@2.1.0:
    resolution: {integrity: sha512-qwHbBLV7WviBl0rQsOzH6o5lwyOIvwp/BdFnvVxXORldu5TmjFfjzBcWUWS5kWAZhmv+JtiDhSuQCp4sBfbIgg==}
    engines: {node: '>= 4.5.0'}

  http-proxy-agent@4.0.1:
    resolution: {integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==}
    engines: {node: '>= 6'}

  http-proxy-agent@5.0.0:
    resolution: {integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==}
    engines: {node: '>= 6'}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==}
    engines: {node: '>= 14'}

  http-signature@1.2.0:
    resolution: {integrity: sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==}
    engines: {node: '>=0.8', npm: '>=1.3.7'}

  http-status-codes@1.4.0:
    resolution: {integrity: sha512-JrT3ua+WgH8zBD3HEJYbeEgnuQaAnUeRRko/YojPAJjGmIfGD3KPU/asLdsLwKjfxOmQe5nXMQ0pt/7MyapVbQ==}

  http-status-codes@2.3.0:
    resolution: {integrity: sha512-RJ8XvFvpPM/Dmc5SV+dC4y5PCeOhT3x1Hq0NU3rjGeg5a/CqlhZ7uudknPwZFz4aeAXDcbAyaeP7GAo9lvngtA==}

  https-proxy-agent@2.2.4:
    resolution: {integrity: sha512-OmvfoQ53WLjtA9HeYP9RNrWMJzzAz1JGaSFr1nijg0PVR1JaD/xbJq1mdEIIlxGpXp9eSe/O2LgU9DJmTPd0Eg==}
    engines: {node: '>= 4.5.0'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==}
    engines: {node: '>= 14'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}

  i18n@0.15.1:
    resolution: {integrity: sha512-yue187t8MqUPMHdKjiZGrX+L+xcUsDClGO0Cz4loaKUOK9WrGw5pgan4bv130utOwX7fHE9w2iUeHFalVQWkXA==}
    engines: {node: '>=10'}

  iconv-lite@0.4.19:
    resolution: {integrity: sha512-oTZqweIP51xaGPI4uPa56/Pri/480R+mo7SeU+YETByQNhDG55ycFyNLIgta9vXhILrxXDmF7ZGhqZIcuN0gJQ==}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.4.8:
    resolution: {integrity: sha512-D90rbOiZuEJGtmIBK9wcRpW//ZKLD8bTPOAx5oEsu+O+HhSOstX/HCZFBvNkuyDuiNHunb81cfsqaYzZxcUMYA==}
    engines: {node: '>=0.8.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  ignore-by-default@1.0.1:
    resolution: {integrity: sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==}

  ignore-walk@6.0.5:
    resolution: {integrity: sha512-VuuG0wCnjhnylG1ABXT3dAuIpTNDs/G8jlpmwXY03fXoXy/8ZK8/T+hMzt8L4WnrLCJgdybqgPagnF/f97cg3A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  ignore@7.0.5:
    resolution: {integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  import-local@3.1.0:
    resolution: {integrity: sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  indent-string@5.0.0:
    resolution: {integrity: sha512-m6FAo/spmsW2Ab2fU35JTYwtOKa2yAwXSwgjSv1TJzh4Mh7mC3lzAOVLBprb72XsTrgkEIsl7YrFNAiDiRhIGg==}
    engines: {node: '>=12'}

  index-to-position@1.1.0:
    resolution: {integrity: sha512-XPdx9Dq4t9Qk1mTMbWONJqU7boCoumEH7fRET37HX5+khDUl3J2W6PdALxhILYlIYx2amlwYcRPp28p0tSiojg==}
    engines: {node: '>=18'}

  indexof@0.0.1:
    resolution: {integrity: sha512-i0G7hLJ1z0DE8dsqJa2rycj9dBmNKgXBvotXtZYXakU9oivfB9Uj2ZBC27qqef2U58/ZLwalxa1X/RDCdkHtVg==}

  individual@3.0.0:
    resolution: {integrity: sha512-rUY5vtT748NMRbEMrTNiFfy29BgGZwGXUi2NFUVMWQrogSLzlJvQV9eeMWi+g1aVaQ53tpyLAQtd5x/JH0Nh1g==}

  inflection@1.13.4:
    resolution: {integrity: sha512-6I/HUDeYFfuNCVS3td055BaXBwKYuzw7K3ExVMStBowKo9oOAMJIXIHvdyR3iboTCp1b+1i5DSkIZTcwIktuDw==}
    engines: {'0': node >= 0.4.0}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}

  ini@4.1.3:
    resolution: {integrity: sha512-X7rqawQBvfdjS10YU1y1YVreA3SsLrW9dX2CewP2EbBJM4ypVNLDkO5y04gejPwKIY9lR+7r9gn3rFPt/kmWFg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  init-package-json@6.0.3:
    resolution: {integrity: sha512-Zfeb5ol+H+eqJWHTaGca9BovufyGeIfr4zaaBorPmJBMrJ+KBnN+kQx2ZtXdsotUTgldHmHQV44xvUWOUA7E2w==}
    engines: {node: ^16.14.0 || >=18.0.0}

  inquirer@8.2.7:
    resolution: {integrity: sha512-UjOaSel/iddGZJ5xP/Eixh6dY1XghiBw4XK13rCCIJcJfyhhoul/7KhLLUGtebEj6GDYM6Vnx/mVsjx2L/mFIA==}
    engines: {node: '>=12.0.0'}

  internal-slot@1.1.0:
    resolution: {integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==}
    engines: {node: '>= 0.4'}

  into-stream@4.0.0:
    resolution: {integrity: sha512-i29KNyE5r0Y/UQzcQ0IbZO1MYJ53Jn0EcFRZPj5FzWKYH17kDFEOwuA+3jroymOI06SW1dEDnly9A1CAreC5dg==}
    engines: {node: '>=6'}

  inversify-express-utils@6.3.2:
    resolution: {integrity: sha512-zIFMJVPTcXzxZBmwtWV2b26MGwPzbZ/XM5SPnrE0SqNQ3QqgI8LMV3nbCLhaPrSji5KToo77UXK5yFpPdVgWJQ==}

  inversify-express-utils@6.4.6:
    resolution: {integrity: sha512-IWvWS8pcg2jwMxrSH3o12BFdpZbO1DM/0U/f5GyCV07sbsNVNaHjgBHRpHTvOHhyxakxoXQLknXF47RI5JqPHA==}

  inversify-inject-decorators@3.1.0:
    resolution: {integrity: sha512-/seBlVp5bXrLQS3DpKEmlgeZL6C7Tf/QITd+IMQrbBBGuCbxb7k3hRAWu9XSreNpFzLgSboz3sClLSEmGwHphw==}

  inversify@5.0.1:
    resolution: {integrity: sha512-Ieh06s48WnEYGcqHepdsJUIJUXpwH5o5vodAX+DK2JA/gjy4EbEcQZxw+uFfzysmKjiLXGYwNG3qDZsKVMcINQ==}

  inversify@6.0.3:
    resolution: {integrity: sha512-s/svzcRQ/scaGUUyaVtFSL1dvOaRgyvE7VvpGcJwXmFz7CCzfSfxC/Uyl7iSHDEmBabJ2gbDES72DaygtMmwvg==}

  invert-kv@1.0.0:
    resolution: {integrity: sha512-xgs2NH9AE66ucSq4cNG1nhSFghr5l6tdL15Pk+jl46bmmBapgoaY/AacXyaDznAqmGL99TiLSQgO/XazFSKYeQ==}
    engines: {node: '>=0.10.0'}

  invert-kv@2.0.0:
    resolution: {integrity: sha512-wPVv/y/QQ/Uiirj/vh3oP+1Ww+AWehmi1g5fFWGPF6IpCBCDVrhgHRMvrLfdYcwDh3QJbGXDW4JAuzxElLSqKA==}
    engines: {node: '>=4'}

  ioredis@5.4.2:
    resolution: {integrity: sha512-0SZXGNGZ+WzISQ67QDyZ2x0+wVxjjUndtD8oSeik/4ajifeiRufed8fCb8QW8VMyi4MXcS+UO1k/0NGhvq1PAg==}
    engines: {node: '>=12.22.0'}

  ioredis@5.5.0:
    resolution: {integrity: sha512-7CutT89g23FfSa8MDoIFs2GYYa0PaNiW/OrT+nRyjRXHDZd17HmIgy+reOQ/yhh72NznNjGuS8kbCAcA4Ro4mw==}
    engines: {node: '>=12.22.0'}

  ip-address@10.0.1:
    resolution: {integrity: sha512-NWv9YLW4PoW2B7xtzaS3NCot75m6nK7Icdv0o3lfMceJVRfSoQwqD4wEH5rLwoKJwUiZ/rfpiVBhnaF0FK4HoA==}
    engines: {node: '>= 12'}

  ip-regex@1.0.3:
    resolution: {integrity: sha512-HjpCHTuxbR/6jWJroc/VN+npo5j0T4Vv2TAI5qdEHQx7hsL767MeccGFSsLtF694EiZKTSEqgoeU6DtGFCcuqQ==}
    engines: {node: '>=0.10.0'}

  ip-regex@4.3.0:
    resolution: {integrity: sha512-B9ZWJxHHOHUhUjCPrMpLD4xEq35bUTClHM1S6CBU5ixQnkZmwipwgc96vAd7AAGM9TGHvJR+Uss+/Ak6UphK+Q==}
    engines: {node: '>=8'}

  ip@1.1.9:
    resolution: {integrity: sha512-cyRxvOEpNHNtchU3Ln9KC/auJgup87llfQpQ+t5ghoC/UhL16SWzbueiCsdTnWmqAWl7LadfuwhlqmtOaqMHdQ==}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  ipaddr.js@2.2.0:
    resolution: {integrity: sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==}
    engines: {node: '>= 10'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-async-function@2.1.1:
    resolution: {integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==}
    engines: {node: '>= 0.4'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==}
    engines: {node: '>= 0.4'}

  is-builtin-module@5.0.0:
    resolution: {integrity: sha512-f4RqJKBUe5rQkJ2eJEJBXSticB3hGbN9j0yxxMQFqIW89Jp9WYFtzfTcRlstDKVUTRzSOTLKRfO9vIztenwtxA==}
    engines: {node: '>=18.20'}

  is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  is-ci@3.0.1:
    resolution: {integrity: sha512-ZYvCgrefwqoQ6yTyYUbQu64HsITZ3NfKX1lzaEYdkTDcfKzzCI/wthRRYKkdjHKFVgNiXKAKm65Zo1pk2as/QQ==}
    hasBin: true

  is-cidr@4.0.2:
    resolution: {integrity: sha512-z4a1ENUajDbEl/Q6/pVBpTR1nBjjEE1X7qb7bmWYanNnPoKAvUCPFKeXV6Fe4mgTkWKBqiHIcwsI3SndiO5FeA==}
    engines: {node: '>=10'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==}
    engines: {node: '>= 0.4'}

  is-deflate@1.0.0:
    resolution: {integrity: sha512-YDoFpuZWu1VRXlsnlYMzKyVRITXj7Ej/V9gXQ2/pAe7X1J7M/RNOqaIYi6qUn+B7nGyB9pDXrv02dsB58d2ZAQ==}

  is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@1.0.0:
    resolution: {integrity: sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@2.0.0:
    resolution: {integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==}
    engines: {node: '>=4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-function@1.0.2:
    resolution: {integrity: sha512-lw7DUp0aWXYg+CBCN+JKkcE0Q2RayZnSvnZBlwgxHBQhqt5pZNVy4Ri7H9GmmXkdu7LUthszM+Tor1u/2iBcpQ==}

  is-generator-function@1.1.0:
    resolution: {integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-gzip@1.0.0:
    resolution: {integrity: sha512-rcfALRIb1YewtnksfRIHGcIY93QnK8BIQ/2c9yDYcG/Y6+vRoJuTWBmmSEbyLLYtXm7q35pHOHbZFQBaLrhlWQ==}
    engines: {node: '>=0.10.0'}

  is-interactive@1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}

  is-lambda@1.0.1:
    resolution: {integrity: sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==}

  is-map@2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}

  is-negative-zero@2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}

  is-number-object@1.1.1:
    resolution: {integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-obj@2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==}
    engines: {node: '>=8'}

  is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==}
    engines: {node: '>=0.10.0'}

  is-plain-obj@2.1.0:
    resolution: {integrity: sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==}
    engines: {node: '>=8'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}

  is-regex@1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==}
    engines: {node: '>= 0.4'}

  is-ssh@1.4.1:
    resolution: {integrity: sha512-JNeu1wQsHjyHgn9NcWTaXq6zWSR6hqE0++zhfZlkFBbScNkyvxCdeV8sRkSBaeLKxmbpR21brail63ACNxJ0Tg==}

  is-stream@1.1.0:
    resolution: {integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==}
    engines: {node: '>=0.10.0'}

  is-stream@2.0.0:
    resolution: {integrity: sha512-XCoy+WlUr7d1+Z8GgSuXmpuUFC9fOhRXglJMx+dwLKTkL44Cjd4W1Z5P+BQZpr+cR93aGP4S/s7Ftw6Nd/kiEw==}
    engines: {node: '>=8'}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-string@1.1.1:
    resolution: {integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==}
    engines: {node: '>= 0.4'}

  is-text-path@1.0.1:
    resolution: {integrity: sha512-xFuJpne9oFz5qDaodwmmG08e3CawH/2ZV8Qqza1Ko7Sk8POWbkRdwIoAWVhqvq0XeUzANEhKo2n0IXUGBm7A/w==}
    engines: {node: '>=0.10.0'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==}
    engines: {node: '>= 0.4'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==}

  is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}

  is-utf8@0.2.1:
    resolution: {integrity: sha512-rMYPYvCzsXywIsldgLaSoPlw5PfoB/ssr7hY4pLfcodrA5M/eArza1a9VmTiNIBNMjOGr1Ow9mTyU2o69U6U9Q==}

  is-weakmap@2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==}
    engines: {node: '>= 0.4'}

  is-windows@1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}

  is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}

  is-zip@1.0.0:
    resolution: {integrity: sha512-aym/dLqHZVMW/+bbNrA/eTeWTyW4fE6koLSoFSsM2GF3/pho7aPCcmHFWFLvzHu7MDuf67domYn36GDwU/cJkQ==}
    engines: {node: '>=0.10.0'}

  isarray@0.0.1:
    resolution: {integrity: sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isarray@2.0.1:
    resolution: {integrity: sha512-c2cu3UxbI+b6kR3fy0nRnAhodsvR9dx7U5+znCOzdj6IfP3upFURTr0Xl5BlQZNKZjEtxrmVyfSdeE3O57smoQ==}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isexe@3.1.1:
    resolution: {integrity: sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==}
    engines: {node: '>=16'}

  isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  isstream@0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}

  istanbul-lib-hook@3.0.0:
    resolution: {integrity: sha512-Pt/uge1Q9s+5VAZ+pCo16TYMWPBIl+oaNIjgLQxcX0itS6ueeaA+pEfThZpH8WxhFgCiEb8sAJY6MdUKgiIWaQ==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@4.0.3:
    resolution: {integrity: sha512-BXgQl9kf4WTCPCCpmFGoJkz/+uhvm7h7PFKUYxh7qarQd3ER33vHG//qaE8eN25l07YqZPpHXU9I09l/RD5aGQ==}
    engines: {node: '>=8'}

  istanbul-lib-processinfo@2.0.3:
    resolution: {integrity: sha512-NkwHbo3E00oybX6NGJi6ar0B29vxyvNwoC7eJ4G4Yq28UfY758Hgn/heV8VRFhevPED4LXfFz0DQ8z/0kw9zMg==}
    engines: {node: '>=8'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}

  istanbul-reports@3.2.0:
    resolution: {integrity: sha512-HGYWWS/ehqTV3xN10i23tkPkpH46MLCIMFNCaaKNavAXTF1RkqxawEPtnjnGZ6XKSInBKkiOA5BKS+aZiY3AvA==}
    engines: {node: '>=8'}

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jackspeak@4.1.1:
    resolution: {integrity: sha512-zptv57P3GpL+O0I7VdMJNBZCu+BPHVQUk55Ft8/QCJjTVxrnJHuVuX/0Bl2A6/+2oyR/ZMEuFKwmzqqZ/U5nPQ==}
    engines: {node: 20 || >=22}

  jake@10.9.4:
    resolution: {integrity: sha512-wpHYzhxiVQL+IV05BLE2Xn34zW1S223hvjtqk0+gsPrwd/8JNLXJgZZM/iPFsYc1xyphF+6M6EvdE5E9MBGkDA==}
    engines: {node: '>=10'}
    hasBin: true

  jest-diff@29.7.0:
    resolution: {integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-get-type@29.6.3:
    resolution: {integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jimp@0.2.28:
    resolution: {integrity: sha512-9HT7DA279xkTlry2oG30s6AtOUglNiY2UdyYpj0yNI4/NBv8PmdNC0gcldgMU4HqvbUlrM3+v+6GaHnTkH23JQ==}

  jose@4.15.9:
    resolution: {integrity: sha512-1vUQX+IdDMVPj4k8kOxgUqlcK518yluMuGZwqlr44FS1ppZB/5GWh4rZG89erpOBOJjU/OBsnCVFfapsRz6nEA==}

  jpeg-js@0.1.2:
    resolution: {integrity: sha512-CiRVjMKBUp6VYtGwzRjrdnro41yMwKGOWdP9ATXqmixdz2n7KHNwdqthTYSSbOKj/Ha79Gct1sA8ZQpse55TYA==}

  jpeg-js@0.2.0:
    resolution: {integrity: sha512-Ni9PffhJtYtdD7VwxH6V2MnievekGfUefosGCHadog0/jAevRu6HPjYeMHbUemn0IPE8d4wGa8UsOGsX+iKy2g==}

  js-big-integer@1.0.2:
    resolution: {integrity: sha512-bRdSfbPC6w9lvTEHqYsNwpDObQ4BR624q6t8Kk+y27uzDd9vh1IjHbXyrVF83aCEjhlF/yqG7EwsAItBkI4YFQ==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsbn@0.1.1:
    resolution: {integrity: sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==}

  jsesc@3.0.2:
    resolution: {integrity: sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==}
    engines: {node: '>=6'}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-bigint@0.2.3:
    resolution: {integrity: sha512-pG8elXWCTAIwH1W8FwjDbj2FBJSi2WE5PdV0dm+c+7LAmH6XL6fwDsdQGgAgOZljcF3Kj9Uzop2TfGfPDSOUqA==}

  json-bigint@1.0.0:
    resolution: {integrity: sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==}

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-parse-better-errors@1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-parse-even-better-errors@3.0.2:
    resolution: {integrity: sha512-fi0NG4bPjCHunUJffmLd0gxssIgkNmArMvis4iNah6Owg1MCJjWhEcDLmsK6iGkJq3tHwbDkTlce70/tmXN4cQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  json-refs@2.1.7:
    resolution: {integrity: sha512-cx0jWTOF5v2JowwFOoGRTeYUCNojY6akj15w9Yi0WhYCmpHeQgu4r25S9H3TEDkLEOHvb1vIusXYtc46dn/Vmg==}
    engines: {node: '>=0.8'}
    hasBin: true

  json-refs@3.0.12:
    resolution: {integrity: sha512-6RbO1Y3e0Hty/tEpXtQG6jUx7g1G8e39GIOuPugobPC8BX1gZ0OGZQpBn1FLWGkuWF35GRGADvhwdEIFpwIjyA==}
    engines: {node: '>=0.8'}
    hasBin: true

  json-schema-ref-resolver@2.0.1:
    resolution: {integrity: sha512-HG0SIB9X4J8bwbxCbnd5FfPEbcXAJYTi1pBJeP/QPON+w8ovSME8iRG+ElHNxZNX2Qh6eYn1GdzJFS4cDFfx0Q==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-schema@0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json-stringify-nice@1.1.4:
    resolution: {integrity: sha512-5Z5RFW63yxReJ7vANgW6eZFGWaQvnPE3WNmZoOJrSkGju2etKA2L5rrOa1sm877TVTFt57A80BH1bArcmlLfPw==}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonc-parser@3.2.0:
    resolution: {integrity: sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==}

  jsonfile@4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==}

  jsonfile@6.2.0:
    resolution: {integrity: sha512-FGuPw30AdOIUTRMC2OMRtQV+jkVj2cfPqSeWXv1NEAJ1qZ5zb1X6z1mFhbfOB/iy3ssJCD+3KuZ8r8C3uVFlAg==}

  jsonparse@1.3.1:
    resolution: {integrity: sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==}
    engines: {'0': node >= 0.2.0}

  jsonwebtoken@8.5.1:
    resolution: {integrity: sha512-XjwVfRS6jTMsqYs0EsuJ4LGxXV14zQybNd4L2r0UvbVnSF9Af8x7p5MzbJ90Ioz/9TI41/hTCvznF/loiSzn8w==}
    engines: {node: '>=4', npm: '>=1.4.28'}

  jsonwebtoken@9.0.2:
    resolution: {integrity: sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==}
    engines: {node: '>=12', npm: '>=6'}

  jsprim@1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==}
    engines: {node: '>=0.6.0'}

  jsx-ast-utils-x@0.1.0:
    resolution: {integrity: sha512-eQQBjBnsVtGacsG9uJNB8qOr3yA8rga4wAaGG1qRcBzSIvfhERLrWxMAM1hp5fcS6Abo8M4+bUBTekYR0qTPQw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  just-diff-apply@5.5.0:
    resolution: {integrity: sha512-OYTthRfSh55WOItVqwpefPtNt2VdKsq5AnAK6apdtR6yCH8pr0CmSr710J0Mf+WdQy7K/OzMy7K2MgAfdQURDw==}

  just-diff@6.0.2:
    resolution: {integrity: sha512-S59eriX5u3/QhMNq3v/gm8Kd0w8OS6Tz2FS1NG4blv+z0MuQcBRJyFWjdovM0Rad4/P4aUPFtnkNjMjyMlMSYA==}

  just-extend@6.2.0:
    resolution: {integrity: sha512-cYofQu2Xpom82S6qD778jBDpwvvy39s1l/hrYij2u9AMdQcGRpaBu6kY4mVhuno5kJVi1DAz4aiphA2WI1/OAw==}

  jwa@1.4.2:
    resolution: {integrity: sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw==}

  jwa@2.0.1:
    resolution: {integrity: sha512-hRF04fqJIP8Abbkq5NKGN0Bbr3JxlQ+qhZufXVr0DvujKy93ZCbXZMHDL4EOtodSbCWxOqR8MS1tXA5hwqCXDg==}

  jwks-rsa@3.1.0:
    resolution: {integrity: sha512-v7nqlfezb9YfHHzYII3ef2a2j1XnGeSE/bK3WfumaYCqONAIstJbrEGapz4kadScZzEt7zYCN7bucj8C0Mv/Rg==}
    engines: {node: '>=14'}

  jws@3.2.2:
    resolution: {integrity: sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==}

  jws@4.0.0:
    resolution: {integrity: sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==}

  kafka-node@5.0.0:
    resolution: {integrity: sha512-dD2ga5gLcQhsq1yNoQdy1MU4x4z7YnXM5bcG9SdQuiNr5KKuAmXixH1Mggwdah5o7EfholFbcNDPSVA6BIfaug==}
    engines: {node: '>=8.5.1'}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  lcid@1.0.0:
    resolution: {integrity: sha512-YiGkH6EnGrDGqLMITnGjXtGmNtjoXw9SVUzcaos8RBi7Ps0VBylkq+vOcY9QE5poLasPCR849ucFUkl0UzUyOw==}
    engines: {node: '>=0.10.0'}

  lcid@2.0.0:
    resolution: {integrity: sha512-avPEb8P8EGnwXKClwsNUgryVjllcRqtMYa49NTsbQagYuT1DcXnl1915oxWjoyGrXR6zH/Y0Zc96xWsPcoDKeA==}
    engines: {node: '>=6'}

  lerna@8.2.2:
    resolution: {integrity: sha512-GkqBELTG4k7rfzAwRok2pKBvhNo046Hfwcj7TuhDah3q58/BBBAqvIFLfqEI5fglnNOs6maMSn6/MWjccQE55A==}
    engines: {node: '>=18.0.0'}
    hasBin: true

  levn@0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==}
    engines: {node: '>= 0.8.0'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  libnpmaccess@8.0.6:
    resolution: {integrity: sha512-uM8DHDEfYG6G5gVivVl+yQd4pH3uRclHC59lzIbSvy7b5FEwR+mU49Zq1jEyRtRFv7+M99mUW9S0wL/4laT4lw==}
    engines: {node: ^16.14.0 || >=18.0.0}

  libnpmpublish@9.0.9:
    resolution: {integrity: sha512-26zzwoBNAvX9AWOPiqqF6FG4HrSCPsHFkQm7nT+xU1ggAujL/eae81RnCv4CJ2In9q9fh10B88sYSzKCUh/Ghg==}
    engines: {node: ^16.14.0 || >=18.0.0}

  light-my-request@3.8.0:
    resolution: {integrity: sha512-cIOWmNsgoStysmkzcv2EwvLwMb2hEm6oqKMerG/b5ey9F0we2Qony8cAZgBktmGPYUvPyKsDCzMcYU6fXbpWew==}

  light-my-request@6.6.0:
    resolution: {integrity: sha512-CHYbu8RtboSIoVsHZ6Ye4cj4Aw/yg2oAFimlF7mNvfDV192LR7nDiKtSIfCuLT7KokPSTn/9kfVLm5OGN0A28A==}

  limiter@1.1.5:
    resolution: {integrity: sha512-FWWMIEOxz3GwUI4Ts/IvgVy6LPvoMPgjMdQ185nN6psJyBJ4yOpzqm695/h5umdLJg2vW3GR5iG11MAkR2AzJA==}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  lines-and-columns@2.0.3:
    resolution: {integrity: sha512-cNOjgCnLB+FnvWWtyRTzmB3POJ+cXxTA81LoW7u8JdmhfXzriropYwpjShnz1QLLWsQwY7nIxoDmcPTwphDK9w==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  listenercount@1.0.1:
    resolution: {integrity: sha512-3mk/Zag0+IJxeDrxSgaDPy4zZ3w05PRZeJNnlWhzFz5OkX49J4krc+A8X2d2M69vGMBEX0uyl8M+W+8gH+kBqQ==}

  load-bmfont@1.4.2:
    resolution: {integrity: sha512-qElWkmjW9Oq1F9EI5Gt7aD9zcdHb9spJCW1L/dmPf7KzCCEJxq8nhHz5eCgI9aMf7vrG/wyaCqdsI+Iy9ZTlog==}

  load-json-file@1.1.0:
    resolution: {integrity: sha512-cy7ZdNRXdablkXYNI049pthVeXFurRyb9+hA/dZzerZ0pGTx42z+y+ssxBaVV2l70t1muq5IdKhn4UtcoGUY9A==}
    engines: {node: '>=0.10.0'}

  load-json-file@4.0.0:
    resolution: {integrity: sha512-Kx8hMakjX03tiGTLAIdJ+lL0htKnXjEZN6hk/tozf/WOuYGdZBJrZ+rCJRbVCugsjB3jMLn9746NsQIf5VjBMw==}
    engines: {node: '>=4'}

  load-json-file@6.2.0:
    resolution: {integrity: sha512-gUD/epcRms75Cw8RT1pUdHugZYM5ce64ucs2GEISABwkRsOQr0q2wm/MV2TKThycIe5e0ytRweW2RZxclogCdQ==}
    engines: {node: '>=8'}

  locate-path@2.0.0:
    resolution: {integrity: sha512-NCI2kiDkyR7VeEKm27Kda/iQHyKJe1Bu0FlTbYp3CqJu+9IFe9bLyAjMxf5ZDDbEg+iMPzB5zYyUTSm8wVTKmA==}
    engines: {node: '>=4'}

  locate-path@3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==}
    engines: {node: '>=6'}

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash-compat@3.10.2:
    resolution: {integrity: sha512-k8SE/OwvWfYZqx3MA/Ry1SHBDWre8Z8tCs0Ba0bF5OqVNvymxgFZ/4VDtbTxzTvcoG11JpTMFsaeZp/yGYvFnA==}

  lodash._arraypool@2.4.1:
    resolution: {integrity: sha512-tC2aLC7bbkDXKNrjDu9OLiVx9pFIvjinID2eD9PzNdAQGZScWUd/h8faqOw5d6oLsOvFRCRbz1ASoB+deyMVUw==}

  lodash._basebind@2.4.1:
    resolution: {integrity: sha512-VGHm6DH+1UiuafQdE/DNMqxOcSyhRu0xO9+jPDq7xITRn5YOorGrHVQmavMVXCYmTm80YRTZZCn/jTW7MokwLg==}

  lodash._baseclone@2.4.1:
    resolution: {integrity: sha512-+zJVXs0VxC/Au+/7foiKzw8UaWvfSfPh20XhqK/6HFQiUeclL5fz05zY7G9yDAFItAKKZwB4cgpzGvxiwuG1wQ==}

  lodash._basecreate@2.4.1:
    resolution: {integrity: sha512-8JJ3FnMPm54t3BwPLk8q8mPyQKQXm/rt9df+awr4NGtyJrtcCXM3Of1I86S6jVy1b4yAyFBb8wbKPEauuqzRmQ==}

  lodash._basecreatecallback@2.4.1:
    resolution: {integrity: sha512-SLczhg860fGW7AKlYcuOFstDtJuQhaANlJ4Y/jrOoRxhmVtK41vbJDH3OefVRSRkSCQo4HI82QVkAVsoGa5gSw==}

  lodash._basecreatewrapper@2.4.1:
    resolution: {integrity: sha512-x2ja1fa/qmzbizuXgVM4QAP9svtMbdxjG8Anl9bCeDAwLOVQ1vLrA0hLb/NkpbGi9evjtkl0aWLTEoOlUdBPQA==}

  lodash._createwrapper@2.4.1:
    resolution: {integrity: sha512-5TCfLt1haQpsa7bgLYRKNNE4yqhO4ZxIayN1btQmazMchO6Q8JYFRMqbJ3W+uNmMm4R0Jw7KGkZX5YfDDnywuw==}

  lodash._getarray@2.4.1:
    resolution: {integrity: sha512-iIrScwY3atGvLVbQL/+CNUznaPwBJg78S/JO4cTUFXRkRsZgEBhscB27cVoT4tsIOUyFu/5M/0umfHNGJ6wYwg==}

  lodash._isnative@2.4.1:
    resolution: {integrity: sha512-BOlKGKNHhCHswGOWtmVb5zBygyxN7EmTuzVOSQI6QSoGhG+kvv71gICFS1TBpnqvT1n53txK8CDK3u5D2/GZxQ==}

  lodash._maxpoolsize@2.4.1:
    resolution: {integrity: sha512-xKDem1BxoIfcCtaJHotjtyfdIvZO9qrF+mv3G1+ngQmaI3MJt3Qm46i9HLk/CbzABbavUrr1/EomQT8KxtsrYA==}

  lodash._objecttypes@2.4.1:
    resolution: {integrity: sha512-XpqGh1e7hhkOzftBfWE7zt+Yn9mVHFkDhicVttvKLsoCMLVVL+xTQjfjB4X4vtznauxv0QZ5ZAeqjvat0dh62Q==}

  lodash._releasearray@2.4.1:
    resolution: {integrity: sha512-wwCwWX8PK/mYR5VZjcU5JFl6py/qrfLGMxzpKOfSqgA1PaZ6Z625CZLCxH1KsqyxSkOFmNm+mEYjeDpXlM4hrg==}

  lodash._setbinddata@2.4.1:
    resolution: {integrity: sha512-Vx0XKzpg2DFbQw4wrp1xSWd2sfl3W/BG6bucSRZmftS1AzbWRemCmBQDxyQTNhlLNec428PXkuuja+VNBZgu2A==}

  lodash._shimkeys@2.4.1:
    resolution: {integrity: sha512-lBrglYxLD/6KAJ8IEa5Lg+YHgNAL7FyKqXg4XOUI+Du/vtniLs1ZqS+yHNKPkK54waAgkdUnDOYaWf+rv4B+AA==}

  lodash._slice@2.4.1:
    resolution: {integrity: sha512-+odPJa4PE2UgYnQgJgkLs0UD03QU78R2ivhrFnG9GdtYOZdE6ObxOj7KiUEUlqOOgatFT+ZqSypFjDSduTigKg==}

  lodash.assign@2.4.1:
    resolution: {integrity: sha512-AqQ4AJz5buSx9ELXWt5dONwJyVPd4NTADMKhoVYWCugjoVf172/LpvVhwmSJn4g8/Dc0S8hxTe8rt5Dob3X9KQ==}

  lodash.bind@2.4.1:
    resolution: {integrity: sha512-hn2VWYZ+N9aYncRad4jORvlGgpFrn+axnPIWRvFxjk6CWcZH5b5alI8EymYsHITI23Z9wrW/+ORq+azrVFpOfw==}

  lodash.clonedeep@2.4.1:
    resolution: {integrity: sha512-zj5vReFLkR+lJOBKP1wyteZ13zut/KSmXtdCBgxcy/m4UTitcBxpeVZT7gwk8BQrztPI5dIgO4bhBppXV4rpTQ==}

  lodash.clonedeep@4.5.0:
    resolution: {integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==}

  lodash.defaults@4.2.0:
    resolution: {integrity: sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ==}

  lodash.flattendeep@4.4.0:
    resolution: {integrity: sha512-uHaJFihxmJcEX3kT4I23ABqKKalJ/zDrDg0lsFtc1h+3uw49SIJ5beyhx5ExVRti3AvKoOJngIj7xz3oylPdWQ==}

  lodash.foreach@2.4.1:
    resolution: {integrity: sha512-AvOobAkE7qBtIiHU5QHQIfveWH5Usr9pIcFIzBv7u4S6bvb3FWpFrh9ltqBY7UeL5lw6e8d+SggiUXQVyh+FpA==}

  lodash.forown@2.4.1:
    resolution: {integrity: sha512-VC+CKm/zSs5t3i/MHv71HZoQphuqOvez1xhjWBwHU5zAbsCYrqwHr+MyQyMk14HzA3hSRNA5lCqDMSw5G2Qscg==}

  lodash.get@4.4.2:
    resolution: {integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==}
    deprecated: This package is deprecated. Use the optional chaining (?.) operator instead.

  lodash.identity@2.4.1:
    resolution: {integrity: sha512-VRYX+8XipeLjorag5bz3YBBRJ+5kj8hVBzfnaHgXPZAVTYowBdY5l0M5ZnOmlAMCOXBFabQtm7f5VqjMKEji0w==}

  lodash.includes@4.3.0:
    resolution: {integrity: sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==}

  lodash.isarguments@3.1.0:
    resolution: {integrity: sha512-chi4NHZlZqZD18a0imDHnZPrDeBbTtVN7GXMwuGdRH9qotxAjYs3aVLKc7zNOG9eddR5Ksd8rvFEBc9SsggPpg==}

  lodash.isarray@2.4.1:
    resolution: {integrity: sha512-yRDd0z+APziDqbk0MqR6Qfwj/Qn3jLxFJbI9U8MuvdTnqIXdZ5YXyGLnwuzCpZmjr26F1GNOjKLMMZ10i/wy6A==}

  lodash.isboolean@3.0.3:
    resolution: {integrity: sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==}

  lodash.isequal@4.5.0:
    resolution: {integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==}
    deprecated: This package is deprecated. Use require('node:util').isDeepStrictEqual instead.

  lodash.isfunction@2.4.1:
    resolution: {integrity: sha512-6XcAB3izeQxPOQQNAJbbdjXbvWEt2Pn9ezPrjr4CwoLwmqsLVbsiEXD19cmmt4mbzOCOCdHzOQiUivUOJLra7w==}

  lodash.isinteger@4.0.4:
    resolution: {integrity: sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==}

  lodash.ismatch@4.4.0:
    resolution: {integrity: sha512-fPMfXjGQEV9Xsq/8MTSgUf255gawYRbjwMyDbcvDhXgV7enSZA0hynz6vMPnpAb5iONEzBHBPsT+0zes5Z301g==}

  lodash.isnumber@3.0.3:
    resolution: {integrity: sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==}

  lodash.isobject@2.4.1:
    resolution: {integrity: sha512-sTebg2a1PoicYEZXD5PBdQcTlIJ6hUslrlWr7iV0O7n+i4596s2NQ9I5CaZ5FbXSfya/9WQsrYLANUJv9paYVA==}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}

  lodash.isstring@4.0.1:
    resolution: {integrity: sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==}

  lodash.keys@2.4.1:
    resolution: {integrity: sha512-ZpJhwvUXHSNL5wYd1RM6CUa2ZuqorG9ngoJ9Ix5Cce+uX7I5O/E06FCJdhSZ33b5dVyeQDnIlWH7B2s5uByZ7g==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.noop@2.4.1:
    resolution: {integrity: sha512-uNcV98/blRhInPUGQEnj9ekXXfG+q+rfoNSFZgl/eBfog9yBDW9gfUv2AHX/rAF7zZRlzWhbslGhbGQFZlCkZA==}

  lodash.once@4.1.1:
    resolution: {integrity: sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==}

  lodash.support@2.4.1:
    resolution: {integrity: sha512-6SwqWwGFHhTXEiqB/yQgu8FYd//tm786d49y7kizHVCJH7zdzs191UQn3ES3tkkDbUddNRfkCRYqJFHtbLnbCw==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}

  long-timeout@0.1.1:
    resolution: {integrity: sha512-BFRuQUqc7x2NWxfJBCyUrN8iYUYznzL9JROmRz1gZ6KlOIgmoD+njPVbb+VNn2nGMKggMsK79iUNErillsrx7w==}

  long@1.1.2:
    resolution: {integrity: sha512-pjR3OP1X2VVQhCQlrq3s8UxugQsuoucwMOn9Yj/kN/61HMc+lDFJS5bvpNEHneZ9NVaSm8gNWxZvtGS7lqHb3Q==}
    engines: {node: '>=0.6'}

  loupe@2.3.7:
    resolution: {integrity: sha512-zSMINGVYkdpYSOBmLi0D1Uo7JU9nVdQKrHxC8eYlV+9YKK9WePqAlL7lSlorG/U2Fw1w0hTBmaa/jrQ3UbPHtA==}

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lru-cache@11.2.1:
    resolution: {integrity: sha512-r8LA6i4LP4EeWOhqBaZZjDWwehd1xUJPCJd9Sv300H0ZmcUER4+JPh7bqqZeqs1o5pgtgvXm+d9UGrB5zZGDiQ==}
    engines: {node: 20 || >=22}

  lru-cache@4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  lru-memoizer@2.3.0:
    resolution: {integrity: sha512-GXn7gyHAMhO13WSKrIiNfztwxodVsP8IoZ3XfrJV4yH2x0/OeTO/FIaAHTY5YekdGgW94njfuKmyyt1E0mR6Ug==}

  luxon@3.7.2:
    resolution: {integrity: sha512-vtEhXh/gNjI9Yg1u4jX/0YVPMvxzHuGgCm6tC5kZyb08yjGWGnqAjGJvcXbqQR2P3MyMEFnRbpcdFS6PBcLqew==}
    engines: {node: '>=12'}

  make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}

  make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==}
    engines: {node: '>=8'}

  make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}

  make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}

  make-fetch-happen@13.0.1:
    resolution: {integrity: sha512-cKTUFc/rbKUd/9meOvgrpJ2WrNzymt6jfRDdwg5UCnVzv9dTpEj9JS5m3wtziXVCjluIXyL8pcaukYqezIzZQA==}
    engines: {node: ^16.14.0 || >=18.0.0}

  make-plural@7.4.0:
    resolution: {integrity: sha512-4/gC9KVNTV6pvYg2gFeQYTW3mWaoJt7WZE5vrp1KnQDgW92JtYZnzmZT81oj/dUTqAIu0ufI2x3dkgu3bB1tYg==}

  map-age-cleaner@0.1.3:
    resolution: {integrity: sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w==}
    engines: {node: '>=6'}

  map-obj@1.0.1:
    resolution: {integrity: sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==}
    engines: {node: '>=0.10.0'}

  map-obj@4.3.0:
    resolution: {integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==}
    engines: {node: '>=8'}

  math-interval-parser@2.0.1:
    resolution: {integrity: sha512-VmlAmb0UJwlvMyx8iPhXUDnVW1F9IrGEd9CIOmv+XL8AErCUUuozoDMrgImvnYt2A+53qVX/tPW6YJurMKYsvA==}
    engines: {node: '>=0.10.0'}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  maxmind@4.3.22:
    resolution: {integrity: sha512-dfLO11mE77ELTEIXNezfW0eslodsFLsZ1lQkLauP+5Zsg1m7kCGtljqRyVOd9E5Ne2RJgvY6UU09qvnVocOZvA==}
    engines: {node: '>=12', npm: '>=6'}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  mem@4.3.0:
    resolution: {integrity: sha512-qX2bG48pTqYRVmDB37rn/6PT7LcR8T7oAX3bf99u1Tt1nzxYfxkgqDwUwolPlXweM0XzBOBFzSx4kfp7KP1s/w==}
    engines: {node: '>=6'}

  meow@8.1.2:
    resolution: {integrity: sha512-r85E3NdZ+mpYk1C6RjPFEMSE+s1iZMuHtsHAqY0DT3jZczl0diWUZ8g6oU7h0M9cD2EL+PzaYghhCLzR0ZNn5Q==}
    engines: {node: '>=10'}

  merge-descriptors@1.0.1:
    resolution: {integrity: sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  method-override@3.0.0:
    resolution: {integrity: sha512-IJ2NNN/mSl9w3kzWB92rcdHpz+HjkxhDJWNDBqSlas+zQdP8wBiJzITPg08M/k2uVvMow7Sk41atndNtt/PHSA==}
    engines: {node: '>= 0.10'}

  methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  middie@4.1.0:
    resolution: {integrity: sha512-eylPpZA+K3xO9kpDjagoPkEUkNcWV3EAo5OEz0MqsekUpT7KbnQkk8HNZkh4phx2vvOAmNNZuLRWF9lDDHPpVQ==}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-db@1.54.0:
    resolution: {integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.3.4:
    resolution: {integrity: sha512-sAaYXszED5ALBt665F0wMQCUXpGuZsGdopoqcHPdL39ZYdi7uHoZlhrfZfhv8WzivhBzr/oXwaj+yiK5wY8MXQ==}
    hasBin: true

  mime@1.4.1:
    resolution: {integrity: sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ==}
    hasBin: true

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mime@2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==}
    engines: {node: '>=4.0.0'}
    hasBin: true

  mime@3.0.0:
    resolution: {integrity: sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==}
    engines: {node: '>=10.0.0'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mimic-response@1.0.1:
    resolution: {integrity: sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==}
    engines: {node: '>=4'}

  mimic-response@2.1.0:
    resolution: {integrity: sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==}
    engines: {node: '>=8'}

  min-document@2.19.0:
    resolution: {integrity: sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==}

  min-indent@1.0.1:
    resolution: {integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==}
    engines: {node: '>=4'}

  minimatch@10.0.3:
    resolution: {integrity: sha512-IPZ167aShDZZUMdRk66cyQAW3qr0WzbHkPdMYa8bzZhlHhO3jALbKdxcaak7W9FfT2rZNpQuUu4Od7ILEpXSaw==}
    engines: {node: 20 || >=22}

  minimatch@3.0.5:
    resolution: {integrity: sha512-tUpxzX0VAzJHjLu0xUfFv1gwVp9ba3IOuRAVH2EGuRW8a5emA2FlACLqiT/lDVtS1W+TGNwqz3sWaNyLgDJWuw==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}

  minimatch@8.0.4:
    resolution: {integrity: sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist-options@4.1.0:
    resolution: {integrity: sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==}
    engines: {node: '>= 6'}

  minimist@0.0.8:
    resolution: {integrity: sha512-miQKw5Hv4NS1Psg2517mV4e4dYNaO3++hjAvLOAzKqZ61rH8NS1SK+vbfBWZ5PY/Me/bEWhUwqMghEW5Fb9T7Q==}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass-collect@2.0.1:
    resolution: {integrity: sha512-D7V8PO9oaz7PWGLbCACuI1qEOsq7UKfLotx/C0Aet43fCUB/wfQ7DYeq2oR/svFJGYDHPr38SHATeaj/ZoKHKw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minipass-fetch@3.0.5:
    resolution: {integrity: sha512-2N8elDQAtSnFV0Dk7gt15KHsS0Fyz6CbYZ360h0WTYV1Ty46li3rAXVOQj1THMNLdmrD9Vt5pBPtWtVkpwGBqg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  minipass-flush@1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}

  minipass-pipeline@1.2.4:
    resolution: {integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==}
    engines: {node: '>=8'}

  minipass-sized@1.0.3:
    resolution: {integrity: sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==}
    engines: {node: '>=8'}

  minipass@2.9.0:
    resolution: {integrity: sha512-wxfUjg9WebH+CUDX/CdbRlh5SmfZiy/hpkxaRI16Y9W56Pa75sWgd/rvFilSgrauD9NyFymP/+JFV3KwzIsJeg==}

  minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}

  minipass@4.2.8:
    resolution: {integrity: sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==}
    engines: {node: '>=8'}

  minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}

  mkdirp@0.5.1:
    resolution: {integrity: sha512-SknJC52obPfGQPnjIkXbmA6+5H15E+fR+E4iR2oQ3zzCLbd7/ONua69R/Gw7AgkTLsRG+r5fzksYwWe1AgTyWA==}
    deprecated: Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)
    hasBin: true

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mmdb-lib@2.1.1:
    resolution: {integrity: sha512-yx8H/1H5AfnufiLnzzPqPf4yr/dKU9IFT1rPVwSkrKWHsQEeVVd6+X+L0nUbXhlEFTu3y/7hu38CFmEVgzvyeg==}
    engines: {node: '>=10', npm: '>=6'}

  mocha-typescript@1.1.12:
    resolution: {integrity: sha512-vnq0iOYdeP0w7xaWTeQxniy/CJ/uQGI5rHerTB/PVeUm2GNKcy2tJFGikmAw73boJIYsdQ/H5TR09E4QCUib6A==}
    deprecated: mocha-typescript has been deprecated, use @testdeck/mocha instead
    hasBin: true

  mocha@10.7.3:
    resolution: {integrity: sha512-uQWxAu44wwiACGqjbPYmjo7Lg8sFrS3dQe7PP2FQI+woptP4vZXSMcfMyFL/e1yFEeEpV4RtyTpZROOKmxis+A==}
    engines: {node: '>= 14.0.0'}
    hasBin: true

  modify-values@1.0.1:
    resolution: {integrity: sha512-xV2bxeN6F7oYjZWTe/YPAy6MN2M+sL4u/Rlm2AHCIVGfo2p1yGmBHQ6vHehl4bRTZBdHu3TSkWdYgkwpYzAGSw==}
    engines: {node: '>=0.10.0'}

  moment-timezone@0.5.48:
    resolution: {integrity: sha512-f22b8LV1gbTO2ms2j2z13MuPogNoh5UzxL3nzNAYKGraILnbGc9NEE6dyiiiLv46DGRb8A4kg8UKWLjPthxBHw==}

  moment@2.19.3:
    resolution: {integrity: sha512-SiZ1HUDMfBpfCzL1Hm1vxUFkYDbHx8/RiWBLF+5qoVWTlBGtR15+wVQB7eSD/0w3ueDxzojlX9LQtiKVpLMsFQ==}

  moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  moo@0.5.2:
    resolution: {integrity: sha512-iSAJLHYKnX41mKcJKjqvnAN9sf0LMDTXDEvFv+ffuRR9a1MIuXLjMNL6EsnDHSkKLTWNqQQ5uo61P4EbU4NU+Q==}

  ms@0.7.1:
    resolution: {integrity: sha512-lRLiIR9fSNpnP6TC4v8+4OU7oStC01esuNowdQ34L+Gk8e5Puoc88IqJ+XAY/B3Mn2ZKis8l8HX90oU8ivzUHg==}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  multer@1.4.4:
    resolution: {integrity: sha512-2wY2+xD4udX612aMqMcB8Ws2Voq6NIUPEtD1be6m411T4uDH/VtL9i//xvcyFlTVfRdaBsk7hV5tgrGQqhuBiw==}
    engines: {node: '>= 0.10.0'}
    deprecated: Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10.

  multimatch@5.0.0:
    resolution: {integrity: sha512-ypMKuglUrZUD99Tk2bUQ+xNQj43lPEfAeX2o9cTteAmShXy2VHDJpuwu1o0xqoKCt9jLVAvwyFKdLTPXKAfJyA==}
    engines: {node: '>=10'}

  mustache@4.2.0:
    resolution: {integrity: sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==}
    hasBin: true

  mute-stream@0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}

  mute-stream@1.0.0:
    resolution: {integrity: sha512-avsJQhyd+680gKXyG/sQc0nXaC6rBkPOfyHYcFb9+hdkqQkR9bdnkJ0AMZhke0oesPqIO+mFFJ+IdBc7mst4IA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  nan@2.23.0:
    resolution: {integrity: sha512-1UxuyYGdoQHcGg87Lkqm3FzefucTa0NAiOcuRsDmysep3c1LVCRK2krrUDafMWtjSG04htvAmvg96+SDknOmgQ==}

  nanoid@2.1.11:
    resolution: {integrity: sha512-s/snB+WGm6uwi0WjsZdaVcuf3KJXlfGl2LcxgwkEwJF0D/BWzVWAZW/XY4bFaiR7s0Jk3FPvlnepg1H1b1UwlA==}

  napi-build-utils@1.0.2:
    resolution: {integrity: sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg==}

  native-promise-only@0.8.1:
    resolution: {integrity: sha512-zkVhZUA3y8mbz652WrL5x0fB0ehrBkulWT3TomAQ9iDtyXZvzKeEA6GPxAItBYeNYl5yngKRX612qHOhvMkDeg==}

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  negotiator@0.6.4:
    resolution: {integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  nested-error-stacks@2.1.1:
    resolution: {integrity: sha512-9iN1ka/9zmX1ZvLV9ewJYEk9h7RyRRtqdK0woXcqohu8EWIerfPUjYJPg0ULy0UqP7cslmdGc8xKDJcojlKiaw==}

  netmask@1.0.6:
    resolution: {integrity: sha512-3DWDqAtIiPSkBXZyYEjwebfK56nrlQfRGt642fu8RPaL+ePu750+HCMHxjJCG3iEHq/0aeMvX6KIzlv7nuhfrA==}
    engines: {node: '>= 0.4.0'}

  netmask@2.0.2:
    resolution: {integrity: sha512-dBpDMdxv9Irdq66304OLfEmQ9tbNRFnFTuZiLo+bD+r332bBmMJ8GBLXklIXXgxd3+v9+KUnZaUR5PJMa75Gsg==}
    engines: {node: '>= 0.4.0'}

  nice-try@1.0.5:
    resolution: {integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==}

  nise@5.1.9:
    resolution: {integrity: sha512-qOnoujW4SV6e40dYxJOb3uvuoPHtmLzIk4TFo+j0jPJoC+5Z9xja5qH5JZobEPsa8+YYphMrOSwnrshEhG2qww==}

  node-abi@2.30.1:
    resolution: {integrity: sha512-/2D0wOQPgaUWzVSVgRMx+trKJRC2UG4SUc4oCJoXx9Uxjtp0Vy3/kt7zcbxHF8+Z/pK3UloLWzBISg72brfy1w==}

  node-cache@5.1.2:
    resolution: {integrity: sha512-t1QzWwnk4sjLWaQAS8CHgOJ+RAfmHpxFWmc36IWTiWHQfs0w5JDMBS1b1ZxQteo0vVVuWJvIUKHDkkeK7vIGCg==}
    engines: {node: '>= 8.0.0'}

  node-fetch@2.6.7:
    resolution: {integrity: sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-gyp@10.3.1:
    resolution: {integrity: sha512-Pp3nFHBThHzVtNY7U6JfPjvT/DTE8+o/4xKsLQtBoU+j2HLsGlhcfzflAoUreaJbNmYnX+LlLi0qjV8kpyO6xQ==}
    engines: {node: ^16.14.0 || >=18.0.0}
    hasBin: true

  node-machine-id@1.1.12:
    resolution: {integrity: sha512-QNABxbrPa3qEIfrE6GOJ7BYIuignnJw7iQ2YPbc3Nla1HzRJjXzZOiikfF8m7eAMfichLt3M4VgLOetqgDmgGQ==}

  node-mailjet@3.3.1:
    resolution: {integrity: sha512-MMKE5e1vKv3/GMUa6GRZu4rloSNx3Aa/XlOzjr1P7jo9HFSDgzM1V7Tyi/p2/zPzt1nS5BT2vwiaV+YA8l0BcA==}

  node-preload@0.2.1:
    resolution: {integrity: sha512-RM5oyBy45cLEoHqCeh+MNuFAxO0vTFBLskvQbOKnEE7YTTSN4tbN8QWDIPQ6L+WvKsB/qLEGpYe2ZZ9d4W9OIQ==}
    engines: {node: '>=8'}

  node-releases@2.0.20:
    resolution: {integrity: sha512-7gK6zSXEH6neM212JgfYFXe+GmZQM+fia5SsusuBIUgnPheLFBmIPhtFoAQRj8/7wASYQnbDlHPVwY0BefoFgA==}

  node-schedule@2.1.1:
    resolution: {integrity: sha512-OXdegQq03OmXEjt2hZP33W2YPs/E5BcFQks46+G2gAxs4gHOIVD1u7EqlYLYSKsaIpyKCK9Gbk0ta1/gjRSMRQ==}
    engines: {node: '>=6'}

  nodemon@2.0.22:
    resolution: {integrity: sha512-B8YqaKMmyuCO7BowF1Z1/mkPqLk6cs/l63Ojtd6otKjMx47Dq1utxfRxcavH1I7VSaL8n5BUaoutadnsX3AAVQ==}
    engines: {node: '>=8.10.0'}
    hasBin: true

  noop-logger@0.1.1:
    resolution: {integrity: sha512-6kM8CLXvuW5crTxsAtva2YLrRrDaiTIkIePWs9moLHqbFWT94WpNFjwS/5dfLfECg5i/lkmw3aoqVidxt23TEQ==}

  nopt@7.2.1:
    resolution: {integrity: sha512-taM24ViiimT/XntxbPyJQzCG+p4EKOpgD3mxFwW38mGjVUrfERQOeY4EDHjdnptttfHuHQXFx+lTP08Q+mLa/w==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  normalize-package-data@2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==}

  normalize-package-data@3.0.3:
    resolution: {integrity: sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==}
    engines: {node: '>=10'}

  normalize-package-data@6.0.2:
    resolution: {integrity: sha512-V6gygoYb/5EmNI+MEGrWkC+e6+Rr7mTmfHrxDbLzxQogBkgzo76rkok0Am6thgSF7Mv2nLOajAJj5vDJZEFn7g==}
    engines: {node: ^16.14.0 || >=18.0.0}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  notp@2.0.3:
    resolution: {integrity: sha512-oBig/2uqkjQ5AkBuw4QJYwkEWa/q+zHxI5/I5z6IeP2NT0alpJFsP/trrfCC+9xOAgQSZXssNi962kp5KBmypQ==}
    engines: {node: '> v0.6.0'}

  npm-bundled@3.0.1:
    resolution: {integrity: sha512-+AvaheE/ww1JEwRHOrn4WHNzOxGtVp+adrg2AeZS/7KuxGUYFuBta98wYpfHBbJp6Tg6j1NKSEVHNcfZzJHQwQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-install-checks@6.3.0:
    resolution: {integrity: sha512-W29RiK/xtpCGqn6f3ixfRYGk+zRyr+Ew9F2E20BfXxT5/euLdA/Nm7fO7OeTGuAmTs30cpgInyJ0cYe708YTZw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-normalize-package-bin@3.0.1:
    resolution: {integrity: sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-package-arg@11.0.2:
    resolution: {integrity: sha512-IGN0IAwmhDJwy13Wc8k+4PEbTPhpJnMtfR53ZbOyjkvmEcLS4nCwp6mvMWjS5sUjeiW3mpx6cHmuhKEu9XmcQw==}
    engines: {node: ^16.14.0 || >=18.0.0}

  npm-packlist@8.0.2:
    resolution: {integrity: sha512-shYrPFIS/JLP4oQmAwDyk5HcyysKW8/JLTEA32S0Z5TzvpaeeX2yMFfoK1fjEBnCBvVyIB/Jj/GBFdm0wsgzbA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-pick-manifest@9.1.0:
    resolution: {integrity: sha512-nkc+3pIIhqHVQr085X9d2JzPzLyjzQS96zbruppqC9aZRm/x8xx6xhI98gHtsfELP2bE+loHq8ZaHFHhe+NauA==}
    engines: {node: ^16.14.0 || >=18.0.0}

  npm-registry-fetch@17.1.0:
    resolution: {integrity: sha512-5+bKQRH0J1xG1uZ1zMNvxW0VEyoNWgJpY9UDuluPFLKDfJ9u2JmmjmTJV1srBGQOROfdBMiVvnH2Zvpbm+xkVA==}
    engines: {node: ^16.14.0 || >=18.0.0}

  npm-run-path@2.0.2:
    resolution: {integrity: sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==}
    engines: {node: '>=4'}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  npmlog@4.1.2:
    resolution: {integrity: sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==}
    deprecated: This package is no longer supported.

  nuid@1.1.6:
    resolution: {integrity: sha512-Eb3CPCupYscP1/S1FQcO5nxtu6l/F3k0MQ69h7f5osnsemVk5pkc8/5AyalVT+NCfra9M71U8POqF6EZa6IHvg==}
    engines: {node: '>= 8.16.0'}

  number-is-nan@1.0.1:
    resolution: {integrity: sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ==}
    engines: {node: '>=0.10.0'}

  nx@20.8.2:
    resolution: {integrity: sha512-mDKpbH3vEpUFDx0rrLh+tTqLq1PYU8KiD/R7OVZGd1FxQxghx2HOl32MiqNsfPcw6AvKlXhslbwIESV+N55FLQ==}
    hasBin: true
    peerDependencies:
      '@swc-node/register': ^1.8.0
      '@swc/core': ^1.3.85
    peerDependenciesMeta:
      '@swc-node/register':
        optional: true
      '@swc/core':
        optional: true

  nyc@15.1.0:
    resolution: {integrity: sha512-jMW04n9SxKdKi1ZMGhvUTHBN0EICCRkHemEoE5jm6mTYcqcdas0ATzgUgejlQUHMvpnOZqGB5Xxsv9KxJW1j8A==}
    engines: {node: '>=8.9'}
    hasBin: true

  oauth-sign@0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-component@0.0.3:
    resolution: {integrity: sha512-S0sN3agnVh2SZNEIGc0N1X4Z5K0JeFbGBrnuZpsxuUh5XLF0BnvWkMjRXo/zGKLd/eghvNIKcx1pQkmUjXIyrA==}

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}

  obuf@1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}

  on-exit-leak-free@2.1.2:
    resolution: {integrity: sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==}
    engines: {node: '>=14.0.0'}

  on-finished@2.2.1:
    resolution: {integrity: sha512-9HvMYLv7im5uzOAcg1lon2cEUxycCF4OI+zPz1R/x3MvBv5s2F+DuxrGwkPe+UwvStDQpWbrkXfLZv12mHbl4A==}
    engines: {node: '>= 0.8'}

  on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  open@8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==}
    engines: {node: '>=12'}

  optional@0.1.4:
    resolution: {integrity: sha512-gtvrrCfkE08wKcgXaVwQVgwEQ8vel2dc5DDBn9RLQZ3YtmtkBss6A2HY6BnJH4N/4Ku97Ri/SF8sNWE2225WJw==}

  optionator@0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  ora@5.3.0:
    resolution: {integrity: sha512-zAKMgGXUim0Jyd6CXK9lraBnD3H5yPGBPPOkC23a2BG6hsm4Zu6OQSjQuEtV0BHDf4aKHcUFvJiGRrFuW3MG8g==}
    engines: {node: '>=10'}

  ora@5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==}
    engines: {node: '>=10'}

  os-homedir@1.0.2:
    resolution: {integrity: sha512-B5JU3cabzk8c67mRRd3ECmROafjYMXbuzlwtqdM8IbS8ktlTix8aFGb2bAGKrSRIlnfKwovGUUr72JUPyOb6kQ==}
    engines: {node: '>=0.10.0'}

  os-locale@1.4.0:
    resolution: {integrity: sha512-PRT7ZORmwu2MEFt4/fv3Q+mEfN4zetKxufQrkShY2oGvUms9r8otu5HfdyIFHkYXjO7laNsoVGmM2MANfuTA8g==}
    engines: {node: '>=0.10.0'}

  os-locale@3.1.0:
    resolution: {integrity: sha512-Z8l3R4wYWM40/52Z+S265okfFj8Kt2cC2MKY+xNi3kFs+XGI7WXu/I309QQQYbRW4ijiZ+yxs9pqEhJh0DqW3Q==}
    engines: {node: '>=6'}

  own-keys@1.0.1:
    resolution: {integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==}
    engines: {node: '>= 0.4'}

  p-defer@1.0.0:
    resolution: {integrity: sha512-wB3wfAxZpk2AzOfUMJNL+d36xothRSyj8EXOa4f6GMqYDN9BJaaSISbsk+wS9abmnebVw95C2Kb5t85UmpCxuw==}
    engines: {node: '>=4'}

  p-finally@1.0.0:
    resolution: {integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==}
    engines: {node: '>=4'}

  p-is-promise@2.1.0:
    resolution: {integrity: sha512-Y3W0wlRPK8ZMRbNq97l4M5otioeA5lm1z7bkNkxCka8HSPjR0xRWmpCmc9utiaLP9Jb1eD8BgeIxTW4AIF45Pg==}
    engines: {node: '>=6'}

  p-limit@1.3.0:
    resolution: {integrity: sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==}
    engines: {node: '>=4'}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@2.0.0:
    resolution: {integrity: sha512-nQja7m7gSKuewoVRen45CtVfODR3crN3goVQ0DDZ9N3yHxgpkuBhZqsaiotSQRrADUrne346peY7kT3TSACykg==}
    engines: {node: '>=4'}

  p-locate@3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==}
    engines: {node: '>=6'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  p-map-series@2.1.0:
    resolution: {integrity: sha512-RpYIIK1zXSNEOdwxcfe7FdvGcs7+y5n8rifMhMNWvaxRNMPINJHF5GDeuVxWqnfrcHPSCnp7Oo5yNXHId9Av2Q==}
    engines: {node: '>=8'}

  p-map@3.0.0:
    resolution: {integrity: sha512-d3qXVTF/s+W+CdJ5A29wywV2n8CQQYahlgz2bFiA+4eVNJbHJodPZ+/gXwPGh0bOqA+j8S+6+ckmvLGPk1QpxQ==}
    engines: {node: '>=8'}

  p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}

  p-pipe@3.1.0:
    resolution: {integrity: sha512-08pj8ATpzMR0Y80x50yJHn37NF6vjrqHutASaX5LiH5npS9XPvrUmscd9MF5R4fuYRHOxQR1FfMIlF7AzwoPqw==}
    engines: {node: '>=8'}

  p-queue@6.6.2:
    resolution: {integrity: sha512-RwFpb72c/BhQLEXIZ5K2e+AhgNVmIejGlTgiB9MzZ0e93GRvqZ7uSi0dvRF7/XIXDeNkra2fNHBxTyPDGySpjQ==}
    engines: {node: '>=8'}

  p-reduce@2.1.0:
    resolution: {integrity: sha512-2USApvnsutq8uoxZBGbbWM0JIYLiEMJ9RlaN7fAzVNb9OZN0SHjjTTfIcb667XynS5Y1VhwDJVDa72TnPzAYWw==}
    engines: {node: '>=8'}

  p-timeout@3.2.0:
    resolution: {integrity: sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==}
    engines: {node: '>=8'}

  p-try@1.0.0:
    resolution: {integrity: sha512-U1etNYuMJoIz3ZXSrrySFjsXQTWOx2/jdi86L+2pRvph/qMKL6sbcCYdH23fqsbm8TH2Gn0OybpT4eSFlCVHww==}
    engines: {node: '>=4'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  p-waterfall@2.1.1:
    resolution: {integrity: sha512-RRTnDb2TBG/epPRI2yYXsimO0v3BXC8Yd3ogr1545IaqKK17VGhbWVeGGN+XfCm/08OK8635nH31c8bATkHuSw==}
    engines: {node: '>=8'}

  pac-proxy-agent@2.0.2:
    resolution: {integrity: sha512-cDNAN1Ehjbf5EHkNY5qnRhGPUCp6SnpyVof5fRzN800QV1Y2OkzbH9rmjZkbBRa8igof903yOnjIl6z0SlAhxA==}

  pac-proxy-agent@5.0.0:
    resolution: {integrity: sha512-CcFG3ZtnxO8McDigozwE3AqAw15zDvGH+OjXO4kzf7IkEKkQ4gxQ+3sdF50WmhQ4P/bVusXcqNE2S3XrNURwzQ==}
    engines: {node: '>= 8'}

  pac-resolver@3.0.0:
    resolution: {integrity: sha512-tcc38bsjuE3XZ5+4vP96OfhOugrX+JcnpUbhfuc4LuXBLQhoTthOstZeoQJBDnQUDYzYmdImKsbz0xSl1/9qeA==}

  pac-resolver@5.0.1:
    resolution: {integrity: sha512-cy7u00ko2KVgBAjuhevqpPeHIkCIqPe1v24cydhWjmeuzaBfmUWFCZJ1iAh5TuVzVZoUzXIW7K8sMYOZ84uZ9Q==}
    engines: {node: '>= 8'}

  package-hash@4.0.0:
    resolution: {integrity: sha512-whdkPIooSu/bASggZ96BWVvZTRMOFxnyUG5PnTSGKoJE2gd5mbVNmR2Nj20QFzxYYgAXpoqC+AiXzl+UMRh7zQ==}
    engines: {node: '>=8'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}

  pacote@18.0.6:
    resolution: {integrity: sha512-+eK3G27SMwsB8kLIuj4h1FUhHtwiEUo21Tw8wNjmvdlpOEr613edv+8FUsTj/4F/VN5ywGE19X18N7CC2EJk6A==}
    engines: {node: ^16.14.0 || >=18.0.0}
    hasBin: true

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-bmfont-ascii@1.0.6:
    resolution: {integrity: sha512-U4RrVsUFCleIOBsIGYOMKjn9PavsGOXxbvYGtMOEfnId0SVNsgehXh1DxUdVPLoxd5mvcEtvmKs2Mmf0Mpa1ZA==}

  parse-bmfont-binary@1.0.6:
    resolution: {integrity: sha512-GxmsRea0wdGdYthjuUeWTMWPqm2+FAd4GI8vCvhgJsFnoGhTrLhXDDupwTo7rXVAgaLIGoVHDZS9p/5XbSqeWA==}

  parse-bmfont-xml@1.1.6:
    resolution: {integrity: sha512-0cEliVMZEhrFDwMh4SxIyVJpqYoOWDJ9P895tFuS+XuNzI5UBmBk5U5O4KuJdTnZpSBI4LFA2+ZiJaiwfSwlMA==}

  parse-conflict-json@3.0.1:
    resolution: {integrity: sha512-01TvEktc68vwbJOtWZluyWeVGWjP+bZwXtPDMQVbBKzbJ/vZBif0L69KH1+cHv1SZ6e0FKLvjyHe8mqsIqYOmw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  parse-headers@2.0.6:
    resolution: {integrity: sha512-Tz11t3uKztEW5FEVZnj1ox8GKblWn+PvHY9TmJV5Mll2uHEwRdR/5Li1OlXoECjLYkApdhWy44ocONwXLiKO5A==}

  parse-json@2.2.0:
    resolution: {integrity: sha512-QR/GGaKCkhwk1ePQNYDRKYZ3mwU9ypsKhB0XyFnLQdomyEqk3e8wpW3V5Jp88zbxK4n5ST1nqo+g9juTpownhQ==}
    engines: {node: '>=0.10.0'}

  parse-json@4.0.0:
    resolution: {integrity: sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==}
    engines: {node: '>=4'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  parse-json@8.3.0:
    resolution: {integrity: sha512-ybiGyvspI+fAoRQbIPRddCcSTV9/LsJbf0e/S85VLowVGzRmokfneg2kwVW/KU5rOXrPSbF1qAKPMgNTqqROQQ==}
    engines: {node: '>=18'}

  parse-path@7.1.0:
    resolution: {integrity: sha512-EuCycjZtfPcjWk7KTksnJ5xPMvWGA/6i4zrLYhRG0hGvC3GPU/jGUj3Cy+ZR0v30duV3e23R95T1lE2+lsndSw==}

  parse-png@1.1.2:
    resolution: {integrity: sha512-Ge6gDV9T5zhkWHmjvnNiyhPTCIoY7W+FC7qWPtuL2lIGZAFxxqTRG/ouEXsH9qkw+HzYiPEU/tFcxOCEDTP1Yw==}
    engines: {node: '>=4'}

  parse-url@8.1.0:
    resolution: {integrity: sha512-xDvOoLU5XRrcOZvnI6b8zA6n9O9ejNk/GExuz1yBuWUGn9KA97GI6HTs6u02wKara1CeVmZhH+0TZFdWScR89w==}

  parseqs@0.0.5:
    resolution: {integrity: sha512-B3Nrjw2aL7aI4TDujOzfA4NsEc4u1lVcIRE0xesutH8kjeWF70uk+W5cBlIQx04zUH9NTBvuN36Y9xLRPK6Jjw==}

  parseuri@0.0.5:
    resolution: {integrity: sha512-ijhdxJu6l5Ru12jF0JvzXVPvsC+VibqeaExlNoMhWN6VQ79PGjkmc7oA4W1lp00sFkNyj0fx6ivPLdV51/UMog==}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  path-exists@2.1.0:
    resolution: {integrity: sha512-yTltuKuhtNeFJKa1PiRzfLAU5182q1y4Eb4XCJ3PBqyzEDkAZRzBrKKBct682ls9reBVHf9udYLN5Nd+K1B9BQ==}
    engines: {node: '>=0.10.0'}

  path-exists@3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==}
    engines: {node: '>=4'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@2.0.1:
    resolution: {integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==}
    engines: {node: '>=4'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-loader@1.0.12:
    resolution: {integrity: sha512-n7oDG8B+k/p818uweWrOixY9/Dsr89o2TkCm6tOTex3fpdo2+BFDgR+KpB37mGKBRsBAlR8CIJMFN0OEy/7hIQ==}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-scurry@2.0.0:
    resolution: {integrity: sha512-ypGJsmGtdXUOeM5u93TyeIEfEhM6s+ljAhrk5vAvSx8uyY/02OvrZnA0YNGUrPXfpJMgI1ODd3nwz8Npx4O4cg==}
    engines: {node: 20 || >=22}

  path-to-regexp@0.1.10:
    resolution: {integrity: sha512-7lf7qcQidTku0Gu3YDPc8DJ1q7OOucfa/BSsIwjuh56VU7katFvuM8hULfkwB3Fns/rsVF7PwPKVw1sl5KQS9w==}

  path-to-regexp@0.1.7:
    resolution: {integrity: sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==}

  path-to-regexp@1.9.0:
    resolution: {integrity: sha512-xIp7/apCFJuUHdDLWe8O1HIkb0kQrOMb/0u6FXQjemHn/ii5LrIzU6bdECnsiTF/GjZkMEKg1xdiZwNqDYlZ6g==}

  path-to-regexp@4.0.5:
    resolution: {integrity: sha512-l+fTaGG2N9ZRpCEUj5fG1VKdDLaiqwCIvPngpnxzREhcdobhZC4ou4w984HBu72DqAJ5CfcdV6tjqNOunfpdsQ==}

  path-to-regexp@6.3.0:
    resolution: {integrity: sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==}

  path-to-regexp@8.3.0:
    resolution: {integrity: sha512-7jdwVIRtsP8MYpdXSwOS0YdD0Du+qOoF/AEPIt88PcCFrZCzx41oxku1jD88hZBwbNUIEfpqvuhjFaMAqMTWnA==}

  path-type@1.1.0:
    resolution: {integrity: sha512-S4eENJz1pkiQn9Znv33Q+deTOKmbl+jj1Fl+qiP/vYezj+S8x+J3Uo0ISrx/QoEvIlOaDWJhPaRd1flJ9HXZqg==}
    engines: {node: '>=0.10.0'}

  path-type@3.0.0:
    resolution: {integrity: sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==}
    engines: {node: '>=4'}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  pathval@1.1.1:
    resolution: {integrity: sha512-Dp6zGqpTdETdR63lehJYPeIOqpiNBNtc7BpWSLrOje7UaIsE5aY92r/AunQA7rsXvet3lrJ3JnZX29UPTKXyKQ==}

  peek-stream@1.1.3:
    resolution: {integrity: sha512-FhJ+YbOSBb9/rIl2ZeE/QHEsWn7PqNYt8ARAY3kIgNGOk13g9FGyIY6JIl/xB/3TFRVoTv5as0l11weORrTekA==}

  performance-now@2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}

  pg-cloudflare@1.2.7:
    resolution: {integrity: sha512-YgCtzMH0ptvZJslLM1ffsY4EuGaU0cx4XSdXLRFae8bPP4dS5xL1tNB3k2o/N64cHJpwU7dxKli/nZ2lUa5fLg==}

  pg-connection-string@2.9.1:
    resolution: {integrity: sha512-nkc6NpDcvPVpZXxrreI/FOtX3XemeLl8E0qFr6F2Lrm/I8WOnaWNhIPK2Z7OHpw7gh5XJThi6j6ppgNoaT1w4w==}

  pg-int8@1.0.1:
    resolution: {integrity: sha512-WCtabS6t3c8SkpDBUlb1kjOs7l66xsGdKpIPZsg4wR+B3+u9UAum2odSsF9tnvxg80h4ZxLWMy4pRjOsFIqQpw==}
    engines: {node: '>=4.0.0'}

  pg-numeric@1.0.2:
    resolution: {integrity: sha512-BM/Thnrw5jm2kKLE5uJkXqqExRUY/toLHda65XgFTBTFYZyopbKjBe29Ii3RbkvlsMoFwD+tHeGaCjjv0gHlyw==}
    engines: {node: '>=4'}

  pg-pool@3.10.1:
    resolution: {integrity: sha512-Tu8jMlcX+9d8+QVzKIvM/uJtp07PKr82IUOYEphaWcoBhIYkoHpLXN3qO59nAI11ripznDsEzEv8nUxBVWajGg==}
    peerDependencies:
      pg: '>=8.0'

  pg-protocol@1.10.3:
    resolution: {integrity: sha512-6DIBgBQaTKDJyxnXaLiLR8wBpQQcGWuAESkRBX/t6OwA8YsqP+iVSiond2EDy6Y/dsGk8rh/jtax3js5NeV7JQ==}

  pg-types@2.2.0:
    resolution: {integrity: sha512-qTAAlrEsl8s4OiEQY69wDvcMIdQN6wdz5ojQiOy6YRMuynxenON0O5oCpJI6lshc6scgAY8qvJ2On/p+CXY0GA==}
    engines: {node: '>=4'}

  pg-types@4.1.0:
    resolution: {integrity: sha512-o2XFanIMy/3+mThw69O8d4n1E5zsLhdO+OPqswezu7Z5ekP4hYDqlDjlmOpYMbzY2Br0ufCwJLdDIXeNVwcWFg==}
    engines: {node: '>=10'}

  pg@8.14.1:
    resolution: {integrity: sha512-0TdbqfjwIun9Fm/r89oB7RFQ0bLgduAhiIqIXOsyKoiC/L54DbuAAzIEN/9Op0f1Po9X7iCPXGoa/Ah+2aI8Xw==}
    engines: {node: '>= 8.0.0'}
    peerDependencies:
      pg-native: '>=3.0.1'
    peerDependenciesMeta:
      pg-native:
        optional: true

  pgpass@1.0.5:
    resolution: {integrity: sha512-FdW9r/jQZhSeohs1Z3sI1yxFQNFvMcnmfuj4WBMUTxOrAyLMaTcE1aAMBiTlbMNaXvBCQuVi0R7hd8udDSP7ug==}

  phin@3.7.1:
    resolution: {integrity: sha512-GEazpTWwTZaEQ9RhL7Nyz0WwqilbqgLahDM3D0hxWwmVDI52nXEybHqiN6/elwpkJBhcuj+WbBu+QfT0uhPGfQ==}
    engines: {node: '>= 8'}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}

  pify@3.0.0:
    resolution: {integrity: sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==}
    engines: {node: '>=4'}

  pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  pify@5.0.0:
    resolution: {integrity: sha512-eW/gHNMlxdSP6dmG6uJip6FXN0EQBwm2clYYd8Wul42Cwu/DK8HEftzsapcNdYe2MfLiIwZqsDk2RDEsTE79hA==}
    engines: {node: '>=10'}

  pinkie-promise@2.0.1:
    resolution: {integrity: sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw==}
    engines: {node: '>=0.10.0'}

  pinkie@2.0.4:
    resolution: {integrity: sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg==}
    engines: {node: '>=0.10.0'}

  pino-abstract-transport@2.0.0:
    resolution: {integrity: sha512-F63x5tizV6WCh4R6RHyi2Ml+M70DNRXt/+HANowMflpgGFMAym/VKm6G7ZOQRjqN7XbGxK1Lg9t6ZrtzOaivMw==}

  pino-std-serializers@2.5.0:
    resolution: {integrity: sha512-wXqbqSrIhE58TdrxxlfLwU9eDhrzppQDvGhBEr1gYbzzM4KKo3Y63gSjiDXRKLVS2UOXdPNR2v+KnQgNrs+xUg==}

  pino-std-serializers@7.0.0:
    resolution: {integrity: sha512-e906FRY0+tV27iq4juKzSYPbUj2do2X2JX4EzSca1631EB2QJQUqGbDuERal7LCtOpxl6x3+nvo9NPZcmjkiFA==}

  pino@5.17.0:
    resolution: {integrity: sha512-LqrqmRcJz8etUjyV0ddqB6OTUutCgQULPFg2b4dtijRHUsucaAdBgSUW58vY6RFSX+NT8963F+q0tM6lNwGShA==}
    hasBin: true

  pino@9.9.5:
    resolution: {integrity: sha512-d1s98p8/4TfYhsJ09r/Azt30aYELRi6NNnZtEbqFw6BoGsdPVf5lKNK3kUwH8BmJJfpTLNuicjUQjaMbd93dVg==}
    hasBin: true

  pixelmatch@4.0.2:
    resolution: {integrity: sha512-J8B6xqiO37sU/gkcMglv6h5Jbd9xNER7aHzpfRdNmV4IbQBzBpe4l9XmbG+xPF/znacgu2jfEw+wHffaq/YkXA==}
    hasBin: true

  pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}

  pluralize@8.0.0:
    resolution: {integrity: sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==}
    engines: {node: '>=4'}

  pngjs@3.4.0:
    resolution: {integrity: sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w==}
    engines: {node: '>=4.0.0'}

  pop-iterate@1.0.1:
    resolution: {integrity: sha512-HRCx4+KJE30JhX84wBN4+vja9bNfysxg1y28l0DuJmkoaICiv2ZSilKddbS48pq50P8d2erAhqDLbp47yv3MbQ==}

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==}
    engines: {node: '>= 0.4'}

  postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}

  postgres-array@2.0.0:
    resolution: {integrity: sha512-VpZrUqU5A69eQyW2c5CA1jtLecCsN2U/bD6VilrFDWq5+5UIEVO7nazS3TEcHf1zuPYO/sqGvUvW62g86RXZuA==}
    engines: {node: '>=4'}

  postgres-array@3.0.4:
    resolution: {integrity: sha512-nAUSGfSDGOaOAEGwqsRY27GPOea7CNipJPOA7lPbdEpx5Kg3qzdP0AaWC5MlhTWV9s4hFX39nomVZ+C4tnGOJQ==}
    engines: {node: '>=12'}

  postgres-bytea@1.0.0:
    resolution: {integrity: sha512-xy3pmLuQqRBZBXDULy7KbaitYqLcmxigw14Q5sj8QBVLqEwXfeybIKVWiqAXTlcvdvb0+xkOtDbfQMOf4lST1w==}
    engines: {node: '>=0.10.0'}

  postgres-bytea@3.0.0:
    resolution: {integrity: sha512-CNd4jim9RFPkObHSjVHlVrxoVQXz7quwNFpz7RY1okNNme49+sVyiTvTRobiLV548Hx/hb1BG+iE7h9493WzFw==}
    engines: {node: '>= 6'}

  postgres-date@1.0.7:
    resolution: {integrity: sha512-suDmjLVQg78nMK2UZ454hAG+OAW+HQPZ6n++TNDUX+L0+uUlLywnoxJKDou51Zm+zTCjrCl0Nq6J9C5hP9vK/Q==}
    engines: {node: '>=0.10.0'}

  postgres-date@2.1.0:
    resolution: {integrity: sha512-K7Juri8gtgXVcDfZttFKVmhglp7epKb1K4pgrkLxehjqkrgPhfG6OO8LHLkfaqkbpjNRnra018XwAr1yQFWGcA==}
    engines: {node: '>=12'}

  postgres-interval@1.2.0:
    resolution: {integrity: sha512-9ZhXKM/rw350N1ovuWHbGxnGh/SNJ4cnxHiM0rxE4VN41wsg8P8zWn9hv/buK00RP4WvlOyr/RBDiptyxVbkZQ==}
    engines: {node: '>=0.10.0'}

  postgres-interval@3.0.0:
    resolution: {integrity: sha512-BSNDnbyZCXSxgA+1f5UU2GmwhoI0aU5yMxRGO8CdFEcY2BQF9xm/7MqKnYoM1nJDk8nONNWDk9WeSmePFhQdlw==}
    engines: {node: '>=12'}

  postgres-range@1.1.4:
    resolution: {integrity: sha512-i/hbxIE9803Alj/6ytL7UHQxRvZkI9O4Sy+J3HGc4F4oo/2eQAjTSNJ0bfxyse3bH0nuVesCk+3IRLaMtG3H6w==}

  prebuild-install@5.3.0:
    resolution: {integrity: sha512-aaLVANlj4HgZweKttFNUVNRxDukytuIuxeK2boIMHjagNJCiVKWFsKF4tCE3ql3GbrD2tExPQ7/pwtEJcHNZeg==}
    engines: {node: '>=6'}
    hasBin: true

  prelude-ls@1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==}
    engines: {node: '>= 0.8.0'}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  pretty-format@29.7.0:
    resolution: {integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  proc-log@4.2.0:
    resolution: {integrity: sha512-g8+OnU/L2v+wyiVK+D5fA34J7EH8jZ8DDlvwhRCMxmMj7UCBvxiO1mGeN+36JXIKF4zevU4kRBd8lVgG9vLelA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  process-on-spawn@1.1.0:
    resolution: {integrity: sha512-JOnOPQ/8TZgjs1JIH/m9ni7FfimjNa/PRx7y/Wb5qdItsnhO0jE4AT7fC0HjC28DUQWDr50dwSYZLdRMlqDq3Q==}
    engines: {node: '>=8'}

  process-warning@4.0.1:
    resolution: {integrity: sha512-3c2LzQ3rY9d0hc1emcsHhfT9Jwz0cChib/QN89oME2R451w5fy3f0afAhERFZAwrbDU43wk12d0ORBpDVME50Q==}

  process-warning@5.0.0:
    resolution: {integrity: sha512-a39t9ApHNx2L4+HBnQKqxxHNs1r7KF+Intd8Q/g1bUh6q0WIp9voPXJ/x0j+ZL45KF1pJd9+q2jLIRMfvEshkA==}

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  proggy@2.0.0:
    resolution: {integrity: sha512-69agxLtnI8xBs9gUGqEnK26UfiexpHy+KUpBQWabiytQjnn5wFY8rklAi7GRfABIuPNnQ/ik48+LGLkYYJcy4A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  prom-client@14.2.0:
    resolution: {integrity: sha512-sF308EhTenb/pDRPakm+WgiN+VdM/T1RaHj1x+MvAuT8UiQP8JmOEbxVqtkbfR4LrvOg5n7ic01kRBDGXjYikA==}
    engines: {node: '>=10'}

  prom-client@15.0.0:
    resolution: {integrity: sha512-UocpgIrKyA2TKLVZDSfm8rGkL13C19YrQBAiG3xo3aDFWcHedxRxI3z+cIcucoxpSO0h5lff5iv/SXoxyeopeA==}
    engines: {node: ^16 || ^18 || >=20}

  promise-all-reject-late@1.0.1:
    resolution: {integrity: sha512-vuf0Lf0lOxyQREH7GDIOUMLS7kz+gs8i6B+Yi8dC68a2sychGrHTJYghMBD6k7eUcH0H5P73EckCA48xijWqXw==}

  promise-call-limit@3.0.2:
    resolution: {integrity: sha512-mRPQO2T1QQVw11E7+UdCJu7S61eJVWknzml9sC1heAdj1jxl0fWMBypIt9ZOcLFf8FkG995ZD7RnVk7HH72fZw==}

  promise-inflight@1.0.1:
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true

  promise-retry@2.0.1:
    resolution: {integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==}
    engines: {node: '>=10'}

  promzard@1.0.2:
    resolution: {integrity: sha512-2FPputGL+mP3jJ3UZg/Dl9YOkovB7DX0oOr+ck5QbZ5MtORtds8k/BZdn+02peDLI8/YWbmzx34k5fA+fHvCVQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  properties@1.2.1:
    resolution: {integrity: sha512-qYNxyMj1JeW54i/EWEFsM1cVwxJbtgPp8+0Wg9XjNaK6VE/c4oRi6PNu5p7w1mNXEIQIjV5Wwn8v8Gz82/QzdQ==}
    engines: {node: '>=0.10'}

  protocols@2.0.2:
    resolution: {integrity: sha512-hHVTzba3wboROl0/aWRRG9dMytgH6ow//STBZh43l/wQgmMhYhOFi0EHWAPtoCz9IAUymsyP0TSBHkhgMEGNnQ==}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  proxy-agent@2.3.1:
    resolution: {integrity: sha512-CNKuhC1jVtm8KJYFTS2ZRO71VCBx3QSA92So/e6NrY6GoJonkx3Irnk4047EsCcswczwqAekRj3s8qLRGahSKg==}

  proxy-agent@5.0.0:
    resolution: {integrity: sha512-gkH7BkvLVkSfX9Dk27W6TyNOWWZWRilRfk1XxGNWOYJ2TuedAv1yFpCaU9QSBmBe716XOTNpYNOzhysyw8xn7g==}
    engines: {node: '>= 8'}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  pseudomap@1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==}

  psl@1.15.0:
    resolution: {integrity: sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==}

  pstree.remy@1.1.8:
    resolution: {integrity: sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==}

  pump@1.0.3:
    resolution: {integrity: sha512-8k0JupWme55+9tCVE+FS5ULT3K6AbgqrGa58lTT49RpyfwwcGedHqaC5LlQNdEAumn/wFsu6aPwkuPMioy8kqw==}

  pump@2.0.1:
    resolution: {integrity: sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==}

  pump@3.0.3:
    resolution: {integrity: sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==}

  pumpify@2.0.1:
    resolution: {integrity: sha512-m7KOje7jZxrmutanlkS1daj1dS6z6BgslzOXmcSEpIlCxM3VJH7lG5QLeck/6hgF6F4crFf01UtQmNsJfweTAw==}

  punycode@1.4.1:
    resolution: {integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  q@2.0.3:
    resolution: {integrity: sha512-gv6vLGcmAOg96/fgo3d9tvA4dJNZL3fMyBqVRrGxQ+Q/o4k9QzbJ3NQF9cOO/71wRodoXhaPgphvMFU68qVAJQ==}
    deprecated: |-
      You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.

      (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)

  qrcode-terminal@0.12.0:
    resolution: {integrity: sha512-EXtzRZmC+YGmGlDFbXKxQiMZNwCLEO6BANKXG4iCtSIM0yqc/pappSx3RIKr4r0uh5JsBckOXeKrB3Iz7mdQpQ==}
    hasBin: true

  qrcode@1.3.3:
    resolution: {integrity: sha512-SH7V13AcJusH3GT8bMNOGz4w0L+LjcpNOU/NiOgtBhT/5DoWeZE6D5ntMJnJ84AMkoaM4kjJJoHoh9g++8lWFg==}
    engines: {node: '>=4'}
    hasBin: true

  qs@2.3.3:
    resolution: {integrity: sha512-f5M0HQqZWkzU8GELTY8LyMrGkr3bPjKoFtTkwUEqJQbcljbeK8M7mliP9Ia2xoOI6oMerp+QPS7oYJtpGmWe/A==}

  qs@2.4.2:
    resolution: {integrity: sha512-Ur2glV49dt6jknphzkWeLUNCy7pmwGxGaEJuuxVVBioSwQzT00cZPLEtRqr4cg/iO/6N+RbfB0lFD2EovyeEng==}

  qs@4.0.0:
    resolution: {integrity: sha512-8MPmJ83uBOPsQj5tQCv4g04/nTiY+d17yl9o3Bw73vC6XlEm2POIRRlOgWJ8i74bkGLII670cDJJZkgiZ2sIkg==}

  qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}

  qs@6.14.0:
    resolution: {integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==}
    engines: {node: '>=0.6'}

  qs@6.5.1:
    resolution: {integrity: sha512-eRzhrN1WSINYCDCbrz796z37LOe3m5tmW7RQf6oBntukAG1nmovJvhnwHHRMAfeoItc1m2Hk02WER2aQ/iqs+A==}
    engines: {node: '>=0.6'}

  qs@6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==}
    engines: {node: '>=0.6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quick-format-unescaped@3.0.3:
    resolution: {integrity: sha512-dy1yjycmn9blucmJLXOfZDx1ikZJUi6E8bBZLnhPG5gBrVhHXx2xVyqqgKBubVNEXmx51dBACMHpoMQK/N/AXQ==}

  quick-format-unescaped@4.0.4:
    resolution: {integrity: sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==}

  quick-lru@4.0.1:
    resolution: {integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==}
    engines: {node: '>=8'}

  randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.0.2:
    resolution: {integrity: sha512-YArjFuWQDQtHaA3ACQVKS90Kz0YD5eIe92Uhy9s19LRKAEemlgYXRxmUr4Il0h/035DTPo23pDopJWlIsH100g==}
    engines: {node: '>= 0.8'}

  raw-body@2.3.2:
    resolution: {integrity: sha512-Ss0DsBxqLxCmQkfG5yazYhtbVVTJqS9jTsZG2lhrNwqzOk2SUC7O/NB/M//CkEBqsrtmlNgJCPccJGuYSFr6Vg==}
    engines: {node: '>= 0.8'}

  raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}

  rc@1.2.8:
    resolution: {integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==}
    hasBin: true

  react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}

  read-chunk@1.0.1:
    resolution: {integrity: sha512-5NLTTdX45dKFtG8CX5pKmvS9V5u9wBE+gkklN7xhDuhq3pA2I4O7ALfKxosCMcLHOhkxj6GNacZhfXtp5nlCdg==}
    engines: {node: '>=0.10.0'}

  read-cmd-shim@4.0.0:
    resolution: {integrity: sha512-yILWifhaSEEytfXI76kB9xEEiG1AiozaCJZ83A87ytjRiN+jVibXjedjCRNjoZviinhG+4UkalO3mWTd8u5O0Q==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  read-package-json-fast@3.0.2:
    resolution: {integrity: sha512-0J+Msgym3vrLOUB3hzQCuZHII0xkNGCtz/HJH9xZshwv9DbDwkw1KaE3gx/e2J5rpEY5rtOy6cyhKOPrkP7FZw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  read-package-up@11.0.0:
    resolution: {integrity: sha512-MbgfoNPANMdb4oRBNg5eqLbB2t2r+o5Ua1pNt8BqGp4I0FJZhuVSOj3PaBPni4azWuSzEdNn2evevzVmEk1ohQ==}
    engines: {node: '>=18'}

  read-pkg-up@1.0.1:
    resolution: {integrity: sha512-WD9MTlNtI55IwYUS27iHh9tK3YoIVhxis8yKhLpTqWtml739uXc9NWTpxoHkfZf3+DkCCsXox94/VWZniuZm6A==}
    engines: {node: '>=0.10.0'}

  read-pkg-up@3.0.0:
    resolution: {integrity: sha512-YFzFrVvpC6frF1sz8psoHDBGF7fLPc+llq/8NB43oagqWkx8ar5zYtsTORtOjw9W2RHLpWP+zTWwBvf1bCmcSw==}
    engines: {node: '>=4'}

  read-pkg-up@7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==}
    engines: {node: '>=8'}

  read-pkg@1.1.0:
    resolution: {integrity: sha512-7BGwRHqt4s/uVbuyoeejRn4YmFnYZiFl4AuaeXHlgZf3sONF0SOGlxs2Pw8g6hCKupo08RafIO5YXFNOKTfwsQ==}
    engines: {node: '>=0.10.0'}

  read-pkg@3.0.0:
    resolution: {integrity: sha512-BLq/cCO9two+lBgiTYNqD6GdtK8s4NpaWrl6/rCO9w0TUS8oJl7cmToOZfRYllKTISY6nt1U7jQ53brmKqY6BA==}
    engines: {node: '>=4'}

  read-pkg@5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==}
    engines: {node: '>=8'}

  read-pkg@9.0.1:
    resolution: {integrity: sha512-9viLL4/n1BJUCT1NXVTdS1jtm80yDEgR5T4yCelII49Mbj0v1rZdKqj7zCiYdbB0CuCgdrvHcNogAKTFPBocFA==}
    engines: {node: '>=18'}

  read@3.0.1:
    resolution: {integrity: sha512-SLBrDU/Srs/9EoWhU5GdbAoxG1GzpQHo/6qiGItaoLJ1thmYpcNIM1qISEUvyHBzfGlWIyd6p2DNi1oV1VmAuw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  readable-stream@1.0.27-1:
    resolution: {integrity: sha512-uQE31HGhpMrqZwtDjRliOs2aC3XBi+DdkhLs+Xa0dvVD5eDiZr3+k8rKVZcyTzxosgtMw7B/twQsK3P1KTZeVg==}

  readable-stream@1.1.14:
    resolution: {integrity: sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==}

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readable-stream@4.7.0:
    resolution: {integrity: sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  real-require@0.2.0:
    resolution: {integrity: sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==}
    engines: {node: '>= 12.13.0'}

  redent@3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}

  redis-errors@1.2.0:
    resolution: {integrity: sha512-1qny3OExCf0UvUV/5wpYKf2YwPcOqXzkwKKSmKHiE6ZMQs5heeE/c8eXK+PNllPvmjgAbfnsbpkGZWy8cBpn9w==}
    engines: {node: '>=4'}

  redis-parser@3.0.0:
    resolution: {integrity: sha512-DJnGAeenTdpMEH6uAJRK/uiyEIH9WVsUmoLwzudwGJUwZPp80PDBWPHXSAGNPwNvIXAbe7MSUB1zQFugFml66A==}
    engines: {node: '>=4'}

  reduce-component@1.0.1:
    resolution: {integrity: sha512-y0wyCcdQul3hI3xHfIs0vg/jSbboQc/YTOAqaxjFG7At+XSexduuOqBVL9SmOLSwa/ldkbzVzdwuk9s2EKTAZg==}

  refa@0.12.1:
    resolution: {integrity: sha512-J8rn6v4DBb2nnFqkqwy6/NnTYMcgLA+sLr0iIO41qpv0n+ngb7ksag2tMRl0inb1bbO/esUwzW1vbJi7K0sI0g==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  reflect-metadata@0.1.13:
    resolution: {integrity: sha512-Ts1Y/anZELhSsjMcU605fU9RE4Oi3p5ORujwbIKXfWa+0Zxs510Qrmrce5/Jowq3cHSZSJqBjypxmHarc+vEWg==}

  reflect-metadata@0.2.2:
    resolution: {integrity: sha512-urBwgfrvVP/eAyXx4hluJivBKzuEbSQs9rKWCrCkbSxNv8mxPcUZKeuoF3Uy4mJl3Lwprp6yy5/39VWigZ4K6Q==}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==}
    engines: {node: '>= 0.4'}

  regenerator-runtime@0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}

  regexp-ast-analysis@0.7.1:
    resolution: {integrity: sha512-sZuz1dYW/ZsfG17WSAG7eS85r5a0dDsvg+7BiiYR5o6lKCAtUrEwdmRmaGF6rwVj3LcmAeYkOWKEPlbPzN3Y3A==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  regexp-tree@0.1.27:
    resolution: {integrity: sha512-iETxpjK6YoRWJG5o6hXLwvjYAoW+FEZn9os0PD/b6AP6xQwsa/Y7lCVgIixBbUPMfhu+i2LtdeAqVTgGlQarfA==}
    hasBin: true

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==}
    engines: {node: '>= 0.4'}

  regjsparser@0.12.0:
    resolution: {integrity: sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==}
    hasBin: true

  release-zalgo@1.0.0:
    resolution: {integrity: sha512-gUAyHVHPPC5wdqX/LG4LWtRYtgjxyX78oanFNTMMyFEfOqdC54s3eE82imuWKbOeqYht2CrNf64Qb8vgmmtZGA==}
    engines: {node: '>=4'}

  request@2.88.0:
    resolution: {integrity: sha512-NAqBSrijGLZdM0WZNsInLJpkJokL72XYjUpnB0iwsRgxh7dB6COrHnTBNwN0E+lHDAJzu7kLAkDeY08z2/A0hg==}
    engines: {node: '>= 4'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  request@2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  require-main-filename@1.0.1:
    resolution: {integrity: sha512-IqSUtOVP4ksd1C/ej5zeEh/BIP2ajqpn8c5x+q99gvcIG/Qf0cud5raVnE/Dwd0ua9TXYDoDc0RE5hBSdz22Ug==}

  require-main-filename@2.0.0:
    resolution: {integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==}

  resize-img@1.1.2:
    resolution: {integrity: sha512-/4nKUmuNPuM6gYTWad136ica81baOVjpesgv8FGaIvP0KWcbCWahOWBKaM4tFoM+aVcSA+qQDg28pcnIzFRpJw==}
    engines: {node: '>=4'}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  resolve.exports@2.0.3:
    resolution: {integrity: sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==}
    engines: {node: '>=10'}

  resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true

  restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}

  ret@0.2.2:
    resolution: {integrity: sha512-M0b3YWQs7R3Z917WRQy1HHA7Ba7D8hvZg6UE5mLykJxQVE2ju0IXbGlaHPPlkY+WN7wFP+wUMXmBFA0aV6vYGQ==}
    engines: {node: '>=4'}

  ret@0.5.0:
    resolution: {integrity: sha512-I1XxrZSQ+oErkRR4jYbAyEEu2I0avBvvMM5JN+6EBprOGRCs63ENqZ3vjavq8fBw2+62G5LF5XelKwuJpcvcxw==}
    engines: {node: '>=10'}

  retry-as-promised@7.1.1:
    resolution: {integrity: sha512-hMD7odLOt3LkTjcif8aRZqi/hybjpLNgSk5oF5FCowfCjok6LukpN2bDX7R5wDmbgBQFn7YoBxSagmtXHaJYJw==}

  retry-request@7.0.2:
    resolution: {integrity: sha512-dUOvLMJ0/JJYEn8NrpOaGNE7X3vpI5XlZS/u0ANjqtcZVKnIxP7IgCFwrKTxENw29emmwug53awKtaMm4i9g5w==}
    engines: {node: '>=14'}

  retry@0.10.1:
    resolution: {integrity: sha512-ZXUSQYTHdl3uS7IuCehYfMzKyIDBNoAuUblvy5oGO5UJSUTmStUUVPXbA9Qxd173Bgre53yCQczQuHgRWAdvJQ==}

  retry@0.12.0:
    resolution: {integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==}
    engines: {node: '>= 4'}

  retry@0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}

  reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}

  rimraf@2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@4.4.1:
    resolution: {integrity: sha512-Gk8NlF062+T9CqNGn6h4tls3k6T1+/nXdOcSZVikNVtlRdYpA7wRJJMoXmuvOnLW844rPjdQ7JgXCYM6PPC/og==}
    engines: {node: '>=14'}
    hasBin: true

  rootpath@0.1.2:
    resolution: {integrity: sha512-R3wLbuAYejpxQjL/SjXo1Cjv4wcJECnMRT/FlcCfTwCBhaji9rWaRCoVEQ1SPiTJ4kKK+yh+bZLAV7SCafoDDw==}

  run-async@2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  rxjs@7.8.2:
    resolution: {integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==}
    engines: {node: '>=0.4'}

  safe-buffer@5.1.1:
    resolution: {integrity: sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-identifier@0.4.2:
    resolution: {integrity: sha512-6pNbSMW6OhAi9j+N8V+U715yBQsaWJ7eyEUaOrawX+isg5ZxhUlV1NipNtgaKHmFGiABwt+ZF04Ii+3Xjkg+8w==}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}

  safe-regex2@2.0.0:
    resolution: {integrity: sha512-PaUSFsUaNNuKwkBijoAPHAK6/eM6VirvyPWlZ7BAQy4D+hCvh4B6lIG+nPdhbFfIbP+gTGBcrdsOaUs0F+ZBOQ==}

  safe-regex2@5.0.0:
    resolution: {integrity: sha512-YwJwe5a51WlK7KbOJREPdjNrpViQBI3p4T50lfwPuDhZnE3XGVTlGvi+aolc5+RvxDD6bnUmjVsU9n1eboLUYw==}

  safe-stable-stringify@2.5.0:
    resolution: {integrity: sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==}
    engines: {node: '>=10'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  scmp@2.0.0:
    resolution: {integrity: sha512-FaHoAk75AYhT+rnBmMpkvHSIcQma4OHzYXOhn1XXtgNomi0FTV8YEXYuh2EIdCg5IKMVyFbXeJT4Cn96+fzABg==}

  scslre@0.3.0:
    resolution: {integrity: sha512-3A6sD0WYP7+QrjbfNA2FN3FsOaGGFoekCVgTyypy53gPxhbkCIjtO6YWgdrfM+n/8sI8JeXZOIxsHjMTNxQ4nQ==}
    engines: {node: ^14.0.0 || >=16.0.0}

  secure-json-parse@2.7.0:
    resolution: {integrity: sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==}

  secure-json-parse@4.0.0:
    resolution: {integrity: sha512-dxtLJO6sc35jWidmLxo7ij+Eg48PM/kleBsxpC8QJE0qJICe+KawkDQmvCMZUr9u7WKVHgMW6vy3fQ7zMiFZMA==}

  semver-store@0.3.0:
    resolution: {integrity: sha512-TcZvGMMy9vodEFSse30lWinkj+JgOBvPn8wRItpQRSayhc+4ssDs335uklkfvQQJgL/WvmHLVj4Ycv2s7QCQMg==}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.0.0:
    resolution: {integrity: sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.16.1:
    resolution: {integrity: sha512-ElCLJdJIKPk6ux/Hocwhk7NFHpI3pVm/IZOYWqUmoxcgeyM+MpxHHKhb8QmlJDX1pU6WrgaHBkVNm73Sv7uc2A==}
    engines: {node: '>= 0.8.0'}

  send@0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}

  sequelize-pool@7.1.0:
    resolution: {integrity: sha512-G9c0qlIWQSK29pR/5U2JF5dDQeqqHRragoyahj/Nx4KOOQ3CPPfzxnfqFPCSB7x5UgjOgnZ61nSxz+fjDpRlJg==}
    engines: {node: '>= 10.0.0'}

  sequelize@6.37.4:
    resolution: {integrity: sha512-+8B0p00EKmxJpwwruDI0drxh4wNSC0YB9pVhOajRzfMI+uIDi5V7rJPC8RTTkLmKUoAIatJZn6lW9gj6bmmYKQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      ibm_db: '*'
      mariadb: '*'
      mysql2: '*'
      oracledb: '*'
      pg: '*'
      pg-hstore: '*'
      snowflake-sdk: '*'
      sqlite3: '*'
      tedious: '*'
    peerDependenciesMeta:
      ibm_db:
        optional: true
      mariadb:
        optional: true
      mysql2:
        optional: true
      oracledb:
        optional: true
      pg:
        optional: true
      pg-hstore:
        optional: true
      snowflake-sdk:
        optional: true
      sqlite3:
        optional: true
      tedious:
        optional: true

  sequelize@6.37.7:
    resolution: {integrity: sha512-mCnh83zuz7kQxxJirtFD7q6Huy6liPanI67BSlbzSYgVNl5eXVdE2CN1FuAeZwG1SNpGsNRCV+bJAVVnykZAFA==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      ibm_db: '*'
      mariadb: '*'
      mysql2: '*'
      oracledb: '*'
      pg: '*'
      pg-hstore: '*'
      snowflake-sdk: '*'
      sqlite3: '*'
      tedious: '*'
    peerDependenciesMeta:
      ibm_db:
        optional: true
      mariadb:
        optional: true
      mysql2:
        optional: true
      oracledb:
        optional: true
      pg:
        optional: true
      pg-hstore:
        optional: true
      snowflake-sdk:
        optional: true
      sqlite3:
        optional: true
      tedious:
        optional: true

  serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}

  serve-static@1.13.1:
    resolution: {integrity: sha512-hSMUZrsPa/I09VYFJwa627JJkNs0NrfL1Uzuup+GqHfToR2KcsXFymXSV90hoyw3M+msjFuQly+YzIH/q0MGlQ==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}

  set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  set-cookie-parser@2.7.1:
    resolution: {integrity: sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==}
    engines: {node: '>= 0.4'}

  setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}

  setprototypeof@1.0.3:
    resolution: {integrity: sha512-9jphSf3UbIgpOX/RKvX02iw/rN2TKdusnsPpGfO/rkcsrd+IRqgHZb4VGnmL0Cynps8Nj2hN45wsi30BzrHDIw==}

  setprototypeof@1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shallow-clone@3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}

  shebang-command@1.2.0:
    resolution: {integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==}
    engines: {node: '>=0.10.0'}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@1.0.0:
    resolution: {integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==}
    engines: {node: '>=0.10.0'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shimmer@1.2.1:
    resolution: {integrity: sha512-sQTKC1Re/rM6XyFM6fIAGHRPVGvyXfgzIDvzoq608vM+jeyVD0Tu1E6Np0Kc2zAIFWIj963V2800iF/9LPieQw==}

  shortid@2.2.16:
    resolution: {integrity: sha512-Ugt+GIZqvGXCIItnsL+lvFJOiN7RYqlGy7QE41O3YC1xbNSeDGIRO7xg2JJXIAj1cAGnOeC1r7/T9pgrtQbv4g==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  sigstore@2.3.1:
    resolution: {integrity: sha512-8G+/XDU8wNsJOQS5ysDVO0Etg9/2uA5gR9l4ZwijjlwxBcrU6RPfwi2+jJmbP+Ap1Hlp/nVAaEO4Fj22/SL2gQ==}
    engines: {node: ^16.14.0 || >=18.0.0}

  simple-concat@1.0.1:
    resolution: {integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==}

  simple-get@2.8.2:
    resolution: {integrity: sha512-Ijd/rV5o+mSBBs4F/x9oDPtTx9Zb6X9brmnXvMW4J7IR15ngi9q5xxqWBKU744jTZiaXtxaPL7uHG6vtN8kUkw==}

  simple-lru-cache@0.0.2:
    resolution: {integrity: sha512-uEv/AFO0ADI7d99OHDmh1QfYzQk/izT1vCmu/riQfh7qjBVUUgRT87E5s5h7CxWCA/+YoZerykpEthzVrW3LIw==}

  simple-update-notifier@1.1.0:
    resolution: {integrity: sha512-VpsrsJSUcJEseSbMHkrsrAVSdvVS5I96Qo1QAQ4FxQ9wXFcB+pjj7FB7/us9+GcgfW4ziHtYMc1J0PLczb55mg==}
    engines: {node: '>=8.10.0'}

  sinon-chai@3.7.0:
    resolution: {integrity: sha512-mf5NURdUaSdnatJx3uhoBOrY9dtL19fiOtAdT1Azxg3+lNJFiuN0uzaU3xX1LeAfL17kHQhTAJgpsfhbMJMY2g==}
    peerDependencies:
      chai: ^4.0.0
      sinon: '>=4.0.0'

  sinon@16.1.3:
    resolution: {integrity: sha512-mjnWWeyxcAf9nC0bXcPmiDut+oE8HYridTNzBbF98AYVLmWwGRp2ISEpyhYflG1ifILT+eNn3BmKUJPxjXUPlA==}

  slash@1.0.0:
    resolution: {integrity: sha512-3TYDR7xWt4dIqV2JauJr+EJeW356RXijHeUlO+8djJ+uBXPn8/2dpzBc8yQhh583sVvc9CvFAeQVgijsH+PNNg==}
    engines: {node: '>=0.10.0'}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  smart-buffer@1.1.15:
    resolution: {integrity: sha512-1+8bxygjTsNfvQe0/0pNBesTOlSHtOeG6b6LYbvsZCCHDKYZ40zcQo6YTnZBWrBSLWOCbrHljLdEmGMYebu7aQ==}
    engines: {node: '>= 0.10.15', npm: '>= 1.3.5'}

  smart-buffer@4.2.0:
    resolution: {integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}

  snappy@6.3.5:
    resolution: {integrity: sha512-lonrUtdp1b1uDn1dbwgQbBsb5BbaiLeKq+AGwOk2No+en+VvJThwmtztwulEQsLinRF681pBqib0NUZaizKLIA==}

  socket.io-adapter@1.1.2:
    resolution: {integrity: sha512-WzZRUj1kUjrTIrUKpZLEzFZ1OLj5FwLlAFQs9kuZJzJi5DKdU7FsWc36SNmA8iDOtwBQyT8FkrriRM8vXLYz8g==}

  socket.io-adapter@2.5.5:
    resolution: {integrity: sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg==}

  socket.io-client@2.1.1:
    resolution: {integrity: sha512-jxnFyhAuFxYfjqIgduQlhzqTcOEQSn+OHKVfAxWaNWa7ecP7xSNk2Dx/3UEsDcY7NcFafxvNvKPmmO7HTwTxGQ==}

  socket.io-parser@3.2.0:
    resolution: {integrity: sha512-FYiBx7rc/KORMJlgsXysflWx/RIvtqZbyGLlHZvjfmPTPeuD/I8MaW7cfFrj5tRltICJdgwflhfZ3NVVbVLFQA==}

  socket.io-parser@4.2.4:
    resolution: {integrity: sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==}
    engines: {node: '>=10.0.0'}

  socket.io@2.1.1:
    resolution: {integrity: sha512-rORqq9c+7W0DAK3cleWNSyfv/qKXV99hV4tZe+gGLfBECw3XEhBy7x85F3wypA9688LKjtwO9pX9L33/xQI8yA==}

  socket.io@4.8.1:
    resolution: {integrity: sha512-oZ7iUCxph8WYRHHcjBEc9unw3adt5CmSNlppj/5Q4k2RIrhl8Z5yY2Xr4j9zj0+wzVZ0bxmYoGSzKJnRl6A4yg==}
    engines: {node: '>=10.2.0'}

  socks-proxy-agent@3.0.1:
    resolution: {integrity: sha512-ZwEDymm204mTzvdqyUqOdovVr2YRd2NYskrYrF2LXyZ9qDiMAoFESGK8CRphiO7rtbo2Y757k2Nia3x2hGtalA==}

  socks-proxy-agent@5.0.1:
    resolution: {integrity: sha512-vZdmnjb9a2Tz6WEQVIurybSwElwPxMZaIc7PzqbJTrezcKNznv6giT7J7tZDZ1BojVaa1jvO/UiUdhDVB0ACoQ==}
    engines: {node: '>= 6'}

  socks-proxy-agent@8.0.5:
    resolution: {integrity: sha512-HehCEsotFqbPW9sJ8WVYB6UbmIMv7kUUORIF2Nncq4VQvBfNBLibW9YZR5dlYCSUhwcD628pRllm7n+E+YTzJw==}
    engines: {node: '>= 14'}

  socks@1.1.10:
    resolution: {integrity: sha512-ArX4vGPULWjKDKgUnW8YzfI2uXW7kzgkJuB0GnFBA/PfT3exrrOk+7Wk2oeb894Qf20u1PWv9LEgrO0Z82qAzA==}
    engines: {node: '>= 0.10.0', npm: '>= 1.3.5'}
    deprecated: If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0

  socks@2.8.7:
    resolution: {integrity: sha512-HLpt+uLy/pxB+bum/9DzAgiKS8CX1EvbWxI4zlmgGCExImLdiad2iCwXT5Z4c9c3Eq8rP2318mPW2c+QbtjK8A==}
    engines: {node: '>= 10.0.0', npm: '>= 3.0.0'}

  sonic-boom@0.7.7:
    resolution: {integrity: sha512-Ei5YOo5J64GKClHIL/5evJPgASXFVpfVYbJV9PILZQytTK6/LCwHvsZJW2Ig4p9FMC2OrBrMnXKgRN/OEoAWfg==}

  sonic-boom@4.2.0:
    resolution: {integrity: sha512-INb7TM37/mAcsGmc9hyyI6+QR3rR1zVRu36B0NeGXKnOOLiZOfER5SA+N7X7k3yUYRzLWafduTDvJAfDswwEww==}

  sort-keys@2.0.0:
    resolution: {integrity: sha512-/dPCrG1s3ePpWm6yBbxZq5Be1dXGLyLn9Z791chDC3NFrpkVbWGzkBwPN1knaciexFXgRJ7hzdnwZ4stHSDmjg==}
    engines: {node: '>=4'}

  sorted-array-functions@1.3.0:
    resolution: {integrity: sha512-2sqgzeFlid6N4Z2fUQ1cvFmTOLRi/sEDzSQ0OKYchqgoPmQBVyM3959qYx3fpS6Esef80KjmpgPeEr028dP3OA==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  spark-md5@1.0.1:
    resolution: {integrity: sha512-Xc4QcWu38P9+4dvhG3gIXalClGHgaQMtJrFDWuGJx9hIfftuN2/kKLW4zbQydqxu2xsUxy6LTOSAW73Lj6+uRQ==}

  spawn-wrap@2.0.0:
    resolution: {integrity: sha512-EeajNjfN9zMnULLwhZZQU3GWBoFNkbngTUPfaawT4RkMiviTxcX0qfhVbGey39mfctfDHkWtuecgQ8NJcyQWHg==}
    engines: {node: '>=8'}

  spdx-correct@3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}

  spdx-exceptions@2.5.0:
    resolution: {integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==}

  spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}

  spdx-license-ids@3.0.22:
    resolution: {integrity: sha512-4PRT4nh1EImPbt2jASOKHX7PB7I+e4IWNLvkKFDxNhJlfjbYlleYQh285Z/3mPTHSAK/AvdMmw5BNNuYH8ShgQ==}

  split2@3.2.2:
    resolution: {integrity: sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==}

  split2@4.2.0:
    resolution: {integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==}
    engines: {node: '>= 10.x'}

  split@1.0.1:
    resolution: {integrity: sha512-mTyOoPbrivtXnwnIxZRFYRrPNtEFKlpB2fvjSnCQUiAA6qAZzqwna5envK4uk6OIeP17CsdF3rSBGYVBsU0Tkg==}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  sshpk@1.18.0:
    resolution: {integrity: sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  ssri@10.0.6:
    resolution: {integrity: sha512-MGrFH9Z4NP9Iyhqn16sDtBpRRNJ0Y2hNa6D65h736fVSaPCHr4DM4sWUNvVaSuC+0OBGhwsrydQwmgfg5LncqQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  stack-chain@1.3.7:
    resolution: {integrity: sha512-D8cWtWVdIe/jBA7v5p5Hwl5yOSOrmZPWDPe2KxQ5UAGD+nxbxU0lKXA4h85Ta6+qgdKVL3vUxsbIZjc1kBG7ug==}

  standard-as-callback@2.1.0:
    resolution: {integrity: sha512-qoRRSyROncaz1z0mvYqIE4lCd9p2R90i6GxW3uZv5ucSu8tU7B5HXUP1gG8pVZsYNVaXjk8ClXHPttLyxAL48A==}

  statuses@1.3.1:
    resolution: {integrity: sha512-wuTCPGlJONk/a1kqZ4fQM2+908lC7fa7nPYpTC1EhnvqLX/IICbeP1OZGDtA374trpSq68YubKUMo8oRhN46yg==}
    engines: {node: '>= 0.6'}

  statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  stop-iteration-iterator@1.1.0:
    resolution: {integrity: sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==}
    engines: {node: '>= 0.4'}

  stream-events@1.0.5:
    resolution: {integrity: sha512-E1GUzBSgvct8Jsb3v2X15pjzN1tYebtbLaMg+eBOUOAxgbLoSbT2NS91ckc5lJD1KfLjId+jXJRgo0qnV5Nerg==}

  stream-shift@1.0.3:
    resolution: {integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==}

  stream-to-buffer@0.1.0:
    resolution: {integrity: sha512-Da4WoKaZyu3nf+bIdIifh7IPkFjARBnBK+pYqn0EUJqksjV9afojjaCCHUemH30Jmu7T2qcKvlZm2ykN38uzaw==}
    engines: {node: '>= 0.8'}

  stream-to@0.2.2:
    resolution: {integrity: sha512-Kg1BSDTwgGiVMtTCJNlo7kk/xzL33ZuZveEBRt6rXw+f1WLK/8kmz2NVCT/Qnv0JkV85JOHcLhD82mnXsR3kPw==}
    engines: {node: '>= 0.10.0'}

  streamsearch@0.1.2:
    resolution: {integrity: sha512-jos8u++JKm0ARcSUTAZXOVC0mSox7Bhn6sBgty73P1f3JGf7yG2clTbBNHUdde/kdvP2FESam+vM6l8jBrNxHA==}
    engines: {node: '>=0.8.0'}

  string-similarity@4.0.4:
    resolution: {integrity: sha512-/q/8Q4Bl4ZKAPjj8WerIBJWALKkaPRfrvhfF8k/B23i4nzrlRj2/go1m90In7nG/3XDSbOo0+pu6RvCTM9RGMQ==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.

  string-to-stream@3.0.1:
    resolution: {integrity: sha512-Hl092MV3USJuUCC6mfl9sPzGloA3K5VwdIeJjYIkXY/8K+mUvaeEabWJgArp+xXrsWxCajeT2pc4axbVhIZJyg==}

  string-width@1.0.2:
    resolution: {integrity: sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==}
    engines: {node: '>=0.10.0'}

  string-width@2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==}
    engines: {node: '>=4'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}

  string@3.3.3:
    resolution: {integrity: sha512-LbvprpPZT/39QKfNrlPX9vXtS7If80vqbPQ7clnHQb5oVOM5hz/cs3iQCCZjvQDwsAWl+HpLQX3gRgN6IC8t3g==}

  string_decoder@0.10.31:
    resolution: {integrity: sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}

  strip-ansi@4.0.0:
    resolution: {integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==}
    engines: {node: '>=4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.2:
    resolution: {integrity: sha512-gmBGslpoQJtgnMAvOVqGZpEz9dyoKTCzy2nfz/n8aIFhN/jCE/rCmcxabB6jOOHV+0WNnylOxaxBQPSvcWklhA==}
    engines: {node: '>=12'}

  strip-bom@2.0.0:
    resolution: {integrity: sha512-kwrX1y7czp1E69n2ajbG65mIo9dqvJ+8aBQXOGVxqwvNbsXdFM6Lq37dLAY3mknUwru8CfcCbfOLL/gMo+fi3g==}
    engines: {node: '>=0.10.0'}

  strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  strip-bom@4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}

  strip-eof@1.0.0:
    resolution: {integrity: sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==}
    engines: {node: '>=0.10.0'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-indent@3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}

  strip-indent@4.0.0:
    resolution: {integrity: sha512-mnVSV2l+Zv6BLpSD/8V87CW/y9EmmbYzGCIavsnsI6/nwn26DwffM/yztm30Z/I2DY9wdS3vXVCMnHDgZaVNoA==}
    engines: {node: '>=12'}

  strip-json-comments@2.0.1:
    resolution: {integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==}
    engines: {node: '>=0.10.0'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strnum@1.1.2:
    resolution: {integrity: sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==}

  strong-log-transformer@2.1.0:
    resolution: {integrity: sha512-B3Hgul+z0L9a236FAUC9iZsL+nVHgoCJnqCbN588DjYxvGXaXaaFbfmQ/JhvKjZwsOukuR72XbHv71Qkug0HxA==}
    engines: {node: '>=4'}
    hasBin: true

  stubs@3.0.0:
    resolution: {integrity: sha512-PdHt7hHUJKxvTCgbKX9C1V/ftOcjJQgz8BZwNfV5c4B6dcGqlpelTbJ999jBGZ2jYiPAwcX5dP6oBwVlBlUbxw==}

  superagent-proxy@1.0.3:
    resolution: {integrity: sha512-79Ujg1lRL2ICfuHUdX+H2MjIw73kB7bXsIkxLwHURz3j0XUmEEEoJ+u/wq+mKwna21Uejsm2cGR3OESA00TIjA==}
    peerDependencies:
      superagent: '>= 0.15.4 || 1 || 2 || 3'

  superagent-proxy@3.0.0:
    resolution: {integrity: sha512-wAlRInOeDFyd9pyonrkJspdRAxdLrcsZ6aSnS+8+nu4x1aXbz6FWSTT9M6Ibze+eG60szlL7JA8wEIV7bPWuyQ==}
    engines: {node: '>=6'}
    peerDependencies:
      superagent: '>= 0.15.4 || 1 || 2 || 3'

  superagent@1.8.5:
    resolution: {integrity: sha512-4h4R6fISQXvgjIqZ8DjONYy3y2XPxgZO0LgHsBI6tDAEhzJLpWuK+thM60SmUiERJOEJzmxlIGx/GP6+azky/A==}
    engines: {node: '>= 0.8'}
    deprecated: Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net

  superagent@10.2.3:
    resolution: {integrity: sha512-y/hkYGeXAj7wUMjxRbB21g/l6aAEituGXM9Rwl4o20+SX3e8YOSV6BxFXl+dL3Uk0mjSL3kCbNkwURm8/gEDig==}
    engines: {node: '>=14.18.0'}

  superagent@3.8.3:
    resolution: {integrity: sha512-GLQtLMCoEIK4eDv6OGtkOoSMt3D+oq0y3dsxMuYuDvaNUvuT8eFBuLmfR0iYYzHC1e8hpzC6ZsxbuP6DIalMFA==}
    engines: {node: '>= 4.0'}
    deprecated: Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net

  superagent@7.1.6:
    resolution: {integrity: sha512-gZkVCQR1gy/oUXr+kxJMLDjla434KmSOKbx5iGD30Ql+AkJQ/YlPKECJy2nhqOsHLjGHzoDTXNSjhnvWhzKk7g==}
    engines: {node: '>=6.4.0 <13 || >=14'}
    deprecated: Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net

  superagent@9.0.2:
    resolution: {integrity: sha512-xuW7dzkUpcJq7QnhOsnNUgtYp3xRwpt2F7abdRYIpCsAt0hhUqia0EdxyXZQQpNmGtsCzYHryaKSV3q3GJnq7w==}
    engines: {node: '>=14.18.0'}
    deprecated: Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net

  supertest@7.0.0:
    resolution: {integrity: sha512-qlsr7fIC0lSddmA3tzojvzubYxvlGtzumcdHgPwbFWMISQwL22MhM2Y3LNt+6w9Yyx7559VW5ab70dgphm8qQA==}
    engines: {node: '>=14.18.0'}
    deprecated: Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net

  supports-color@2.0.0:
    resolution: {integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==}
    engines: {node: '>=0.8.0'}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  swagger-converter@0.1.7:
    resolution: {integrity: sha512-O2hZbWqq8x6j0uZ4qWj5dw45WPoAxKsJLJZqOgTqRtPNi8IqA+rDkDV/48S8qanS3KGv1QcVoPNLivMbyHHdAQ==}

  swagger-tools@0.10.1:
    resolution: {integrity: sha512-R/Zw/Pi0aya5M1vt3lIioJQccY5wbAFkrQ4TuPdC+PcH9z08vvfCbF/OZF7nV9QUIzKOZZu7RLl2JD/wRLlHOg==}
    hasBin: true

  tar-fs@1.16.5:
    resolution: {integrity: sha512-1ergVCCysmwHQNrOS+Pjm4DQ4nrGp43+Xnu4MRGjCnQu/m3hEgLNS78d5z+B8OJ1hN5EejJdCSFZE1oM6AQXAQ==}

  tar-stream@1.6.2:
    resolution: {integrity: sha512-rzS0heiNf8Xn7/mpdSVVSMAWAoy9bfb1WOTYC78Z0UQKeKa/CWS8FOq0lKGNa8DWKAn9gxjCvMLYc5PGXYlK2A==}
    engines: {node: '>= 0.8.0'}

  tar-stream@2.2.0:
    resolution: {integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==}
    engines: {node: '>=6'}

  tar@6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==}
    engines: {node: '>=10'}

  tdigest@0.1.2:
    resolution: {integrity: sha512-+G0LLgjjo9BZX2MfdvPfH+MKLCrxlXSYec5DaPYP1fe6Iyhf0/fSmJ0bFiZ1F8BT6cGXl2LpltQptzjXKWEkKA==}

  teeny-request@9.0.0:
    resolution: {integrity: sha512-resvxdc6Mgb7YEThw6G6bExlXKkv6+YbuzGg9xuXxSgxJF7Ozs+o8Y9+2R3sArdWdW8nOokoQb1yrpFB0pQK2g==}
    engines: {node: '>=14'}

  temp-dir@1.0.0:
    resolution: {integrity: sha512-xZFXEGbG7SNC3itwBzI3RYjq/cEhBkx2hJuKGIUOcEULmkQExXiHat2z/qkISYsuR+IKumhEfKKbV5qXmhICFQ==}
    engines: {node: '>=4'}

  test-exclude@6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}

  text-extensions@1.9.0:
    resolution: {integrity: sha512-wiBrwC1EhBelW12Zy26JeOUkQ5mRu+5o8rpsJk5+2t+Y5vE7e842qtZDQ2g1NpX/29HdyFeJ4nSIhI47ENSxlQ==}
    engines: {node: '>=0.10'}

  thirty-two@0.0.2:
    resolution: {integrity: sha512-0j1A9eqbP8dSEtkqqEJGpYFN2lPgQR1d0qKS2KNAmIxkK6gV37D5hRa5b/mYzVL1fyAVWBkeUDIXybZdCLVBzA==}
    engines: {node: '>=0.2.6'}

  thread-stream@3.1.0:
    resolution: {integrity: sha512-OqyPZ9u96VohAyMfJykzmivOrY2wfMSf3C5TtFJVgN+Hm6aj+voFhlK+kZEIv2FBh1X6Xp3DlnCOfEQ3B2J86A==}

  through2@2.0.5:
    resolution: {integrity: sha512-/mrRod8xqpA+IHSLyGCQ2s8SPHiCDEeQJSep1jqLYeEUClOFG2Qsh+4FU6G9VeqpZnGW/Su8LQGc4YKni5rYSQ==}

  through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  thunkify@2.1.2:
    resolution: {integrity: sha512-w9foI80XcGImrhMQ19pxunaEC5Rp2uzxZZg4XBAFRfiLOplk3F0l7wo+bO16vC2/nlQfR/mXZxcduo0MF2GWLg==}

  tiny-lru@11.2.11:
    resolution: {integrity: sha512-27BIW0dIWTYYoWNnqSmoNMKe5WIbkXsc0xaCQHd3/3xT2XMuMJrzHdrO9QBFR14emBz1Bu0dOAs2sCBBrvgPQA==}
    engines: {node: '>=12'}

  tiny-lru@7.0.6:
    resolution: {integrity: sha512-zNYO0Kvgn5rXzWpL0y3RS09sMK67eGaQj9805jlK9G6pSadfriTczzLHFXa/xcW4mIRfmlB9HyQ/+SgL0V1uow==}
    engines: {node: '>=6'}

  tinycolor2@1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}

  tmp@0.2.5:
    resolution: {integrity: sha512-voyz6MApa1rQGUxT3E+BK7/ROe8itEx7vD8/HEvt4xwXucvQ5G5oeEiHkmHZJuBO21RpOf+YYm9MOivj709jow==}
    engines: {node: '>=14.14'}

  to-array@0.1.4:
    resolution: {integrity: sha512-LhVdShQD/4Mk4zXNroIQZJC+Ap3zgLcDuwEdcmLv9CCO73NWockQDwyUnW/m8VX/EElfL6FcYx7EeutN4HJA6A==}

  to-buffer@1.2.1:
    resolution: {integrity: sha512-tB82LpAIWjhLYbqjx3X4zEeHN6M8CiuOEy2JY8SEQVdYRe3CCHOFaqrBW1doLDrfpWhplcW7BL+bO3/6S3pcDQ==}
    engines: {node: '>= 0.4'}

  to-ico@1.1.5:
    resolution: {integrity: sha512-5kIh7m7bkIlqIESEZkL8gAMMzucXKfPe3hX2FoDY5HEAfD9OJU+Qh9b6Enp74w0qRcxVT5ejss66PHKqc3AVkg==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toad-cache@3.7.0:
    resolution: {integrity: sha512-/m8M+2BJUpoJdgAHoG+baCwBT+tf2VraSfkBgl0Y00qIWt41DJ8R5B8nsEw0I58YwF5IZH6z24/2TobDKnqSWw==}
    engines: {node: '>=12'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  toposort-class@1.0.1:
    resolution: {integrity: sha512-OsLcGGbYF3rMjPUf8oKktyvCiUxSbqMMS39m33MAjLTC1DVIH6x3WSt63/M77ihI09+Sdfk1AXvfhCEeUmC7mg==}

  touch@3.1.1:
    resolution: {integrity: sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA==}
    hasBin: true

  tough-cookie@2.4.3:
    resolution: {integrity: sha512-Q5srk/4vDM54WJsJio3XNn6K2sCG+CQ8G5Wz6bZhRZoAe/+TxjWB/GlFAnYEbkYVlON9FMk/fE3h2RLpPXo4lQ==}
    engines: {node: '>=0.8'}

  tough-cookie@2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==}
    engines: {node: '>=0.8'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  traverse@0.3.9:
    resolution: {integrity: sha512-iawgk0hLP3SxGKDfnDJf8wTz4p2qImnyihM5Hh/sGvQ3K37dPi/w8sRhdNIxYA1TwFwc5mDhIJq+O0RsvXBKdQ==}

  traverse@0.6.11:
    resolution: {integrity: sha512-vxXDZg8/+p3gblxB6BhhG5yWVn1kGRlaL8O78UDXc3wRnPizB5g83dcvWV1jpDMIPnjZjOFuxlMmE82XJ4407w==}
    engines: {node: '>= 0.4'}

  treeverse@3.0.0:
    resolution: {integrity: sha512-gcANaAnd2QDZFmHFEOF4k7uc1J/6a6z3DJMd/QwEyxLoKGiptJRwid582r7QIsFlFMIZ3SnxfS52S4hm2DHkuQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  trek-captcha@0.4.0:
    resolution: {integrity: sha512-3m27WCdBAqgdkpzoertvnLZwhw+c49XhZaUVajXixcoaW6EsLQX883ZHrzQLye4nH5QZ9Qk1dt4n6HuwHmgOSQ==}

  trim-newlines@3.0.1:
    resolution: {integrity: sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==}
    engines: {node: '>=8'}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  ts-nats@1.2.4:
    resolution: {integrity: sha512-bIC+PlwHplRqhooa9kE+QPcQ9RPGUXD2ruvdf6LoFpP6MjE6exVoRhYSydm0e5wk7VfuXpTbH1NoopTKHwlOKA==}
    engines: {node: '>= 6.14.4'}
    deprecated: nats now offers the async functionality directly

  ts-nkeys@1.0.16:
    resolution: {integrity: sha512-1qrhAlavbm36wtW+7NtKOgxpzl+70NTF8xlz9mEhiA5zHMlMxjj3sEVKWm3pGZhHXE0Q3ykjrj+OSRVaYw+Dqg==}

  ts-node@10.9.2:
    resolution: {integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==}
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true

  tsconfig-paths@4.2.0:
    resolution: {integrity: sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg==}
    engines: {node: '>=6'}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tuf-js@2.2.1:
    resolution: {integrity: sha512-GwIJau9XaA8nLVbUXsN3IlFi7WmQ48gBUrl3FTkkL/XLu/POhBzfmX9hd33FNMX1qAsfl6ozO1iMmW9NC8YniA==}
    engines: {node: ^16.14.0 || >=18.0.0}

  tunnel-agent@0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==}

  tweetnacl@0.14.5:
    resolution: {integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==}

  tweetnacl@1.0.3:
    resolution: {integrity: sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw==}

  twilio@3.29.0:
    resolution: {integrity: sha512-gMs8SUpndjRUstVrwU+OcMxOneZegwp3GLPzlfSk1a5zv8SJmV2HVV7b29+gMw0fHxKPuHPfE4zrbgYlSlrMzw==}
    engines: {node: '>=6.0'}

  type-check@0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==}
    engines: {node: '>= 0.8.0'}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}

  type-detect@4.1.0:
    resolution: {integrity: sha512-Acylog8/luQ8L7il+geoSxhEkazvkslg7PSNKOX59mbB9cOveP5aq9h74Y7YU8yDpJwetzQQrfIwtf4Wp4LKcw==}
    engines: {node: '>=4'}

  type-fest@0.18.1:
    resolution: {integrity: sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==}
    engines: {node: '>=10'}

  type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}

  type-fest@0.4.1:
    resolution: {integrity: sha512-IwzA/LSfD2vC1/YDYMv/zHP4rDF1usCwllsDpbolT3D4fUepIO7f9K70jjmUewU/LmGUKJcwcVtDCpnKk4BPMw==}
    engines: {node: '>=6'}

  type-fest@0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==}
    engines: {node: '>=8'}

  type-fest@0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==}
    engines: {node: '>=8'}

  type-fest@4.41.0:
    resolution: {integrity: sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==}
    engines: {node: '>=16'}

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==}
    engines: {node: '>= 0.4'}

  typedarray-to-buffer@3.1.5:
    resolution: {integrity: sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==}

  typedarray.prototype.slice@1.0.5:
    resolution: {integrity: sha512-q7QNVDGTdl702bVFiI5eY4l/HkgCM6at9KhcFbgUAzezHFbOVy4+0O/lCjsABEQwbZPravVfBIiBVGo89yzHFg==}
    engines: {node: '>= 0.4'}

  typedarray@0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==}

  typescript@5.6.3:
    resolution: {integrity: sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw==}
    engines: {node: '>=14.17'}
    hasBin: true

  uglify-js@3.19.3:
    resolution: {integrity: sha512-v3Xu+yuwBXisp6QYTcH4UbH+xYJXqnq2m/LtQVWKWzYc1iehYnLixoQDN9FH6/j9/oybfd6W9Ghwkl8+UMKTKQ==}
    engines: {node: '>=0.8.0'}
    hasBin: true

  ultron@1.1.1:
    resolution: {integrity: sha512-UIEXBNeYmKptWH6z8ZnqTeS8fV74zG0/eRU9VGkpzz+LIJNs8W/zM/L+7ctCkRrgbNnnR0xxw4bKOr0cW0N0Og==}

  unbox-primitive@1.1.0:
    resolution: {integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==}
    engines: {node: '>= 0.4'}

  undefsafe@2.0.5:
    resolution: {integrity: sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==}

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  unicorn-magic@0.1.0:
    resolution: {integrity: sha512-lRfVq8fE8gz6QMBuDM6a+LO3IAzTi05H6gCVaUpir2E1Rwpo4ZUog45KpNXKC/Mn3Yb9UDuHumeFTo9iV/D9FQ==}
    engines: {node: '>=18'}

  unique-filename@3.0.0:
    resolution: {integrity: sha512-afXhuC55wkAmZ0P18QsVE6kp8JaxrEokN2HGIoIVv2ijHQd419H0+6EigAFcIzXeMIkcIkNBpB3L/DXB3cTS/g==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  unique-slug@4.0.0:
    resolution: {integrity: sha512-WrcA6AyEfqDX5bWige/4NQfPZMtASNVxdmWR76WESYQVAACSgWcR6e9i0mofqqBxYFtL4oAxPIptY73/0YE1DQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  universal-user-agent@6.0.1:
    resolution: {integrity: sha512-yCzhz6FN2wU1NiiQRogkTQszlQSlpWaw8SvVegAc+bDxbzHgh1vX8uIe8OYyMH6DwH+sdTJsgMl36+mSMdRJIQ==}

  universalify@0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  unzipper@0.10.14:
    resolution: {integrity: sha512-ti4wZj+0bQTiX2KmKWuwj7lhV+2n//uXEotUmGuQqrbVZSEGFMbI68+c6JCQ8aAmUWYvtHEz2A8K6wXvueR/6g==}

  upath@2.0.1:
    resolution: {integrity: sha512-1uEe95xksV1O0CYKXo8vQvN1JEbtJp7lb7C5U9HMsIp6IVwntkH/oNUzyVNQSd4S1sYk2FpSSW44FqMc8qee5w==}
    engines: {node: '>=4'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@3.0.2:
    resolution: {integrity: sha512-SoboS4c924cg+wR2vxl8fospPPli3ZmVPIkRpJEWcrGIPeE8Tr3m9zNIyjYKn9YlF8EgiXQDCy3XVZxSFNjh8A==}

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-regex@3.2.0:
    resolution: {integrity: sha512-dQ9cJzMou5OKr6ZzfvwJkCq3rC72PNXhqz0v3EIhF4a3Np+ujr100AhUx2cKx5ei3iymoJpJrPB3sVSEMdqAeg==}
    engines: {node: '>=0.10.0'}

  url-value-parser@2.2.0:
    resolution: {integrity: sha512-yIQdxJpgkPamPPAPuGdS7Q548rLhny42tg8d4vyTNzFqvOnwqrgHXvgehT09U7fwrzxi3RxCiXjoNUNnNOlQ8A==}
    engines: {node: '>=6.0.0'}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  uuid@10.0.0:
    resolution: {integrity: sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==}
    hasBin: true

  uuid@3.3.2:
    resolution: {integrity: sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  uuid@3.3.3:
    resolution: {integrity: sha512-pW0No1RGHgzlpHJO1nsVrHKpOEIxkGg1xB+v0ZmdNH5OAeAwzAVrCnI2/6Mtx+Uys6iaylxa+D3g4j63IKKjSQ==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  uuid@3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution: {integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==}

  validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}

  validate-npm-package-name@5.0.1:
    resolution: {integrity: sha512-OljLrQ9SQdOUqTaQxqL5dEfZWrXExyyWsozYlAWFawPVNuD83igl7uJD2RTkNMbniIYgt8l81eCJGIdQF7avLQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  validator@10.11.0:
    resolution: {integrity: sha512-X/p3UZerAIsbBfN/IwahhYaBbY68EN/UQBWHtsbXGT5bfrH/p4NQzUCG1kF/rtKaNpnJ7jAu6NGTdSNtyNIXMw==}
    engines: {node: '>= 0.10'}

  validator@13.15.15:
    resolution: {integrity: sha512-BgWVbCI72aIQy937xbawcs+hrVaN/CZ2UwutgaJ36hGqRrLNM+f5LUT/YPRbo8IV/ASeFzXszezV+y2+rq3l8A==}
    engines: {node: '>= 0.10'}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  verror@1.10.0:
    resolution: {integrity: sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==}
    engines: {'0': node >=0.6.0}

  vm2@3.9.19:
    resolution: {integrity: sha512-J637XF0DHDMV57R6JyVsTak7nIL8gy5KH4r1HiwWLf/4GBbb5MKL5y7LpmF4A8E2nR6XmzpmMFQ7V7ppPTmUQg==}
    engines: {node: '>=6.0'}
    deprecated: The library contains critical security issues and should not be used for production! The maintenance of the project has been discontinued. Consider migrating your code to isolated-vm.
    hasBin: true

  walk-up-path@3.0.1:
    resolution: {integrity: sha512-9YlCL/ynK3CTlrSRrDxZvUauLzAswPCrsaCgilqFevUYpeEW0/3ScEjaa3kbW/T0ghhkEr7mv+fpjqn1Y1YuTA==}

  wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==}

  weak-map@1.0.8:
    resolution: {integrity: sha512-lNR9aAefbGPpHO7AEnY0hCFjz1eTkWCXYvkTRrTHs9qv8zJp+SkVYpzfLIFXQQiG3tVvbNFQgVg2bQS8YGgxyw==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}

  which-module@1.0.0:
    resolution: {integrity: sha512-F6+WgncZi/mJDrammbTuHe1q0R5hOXv/mBaiNA2TCNT/LTHusX0V+CJnj9XT8ki5ln2UZyyddDgHfCzyrOH7MQ==}

  which-module@2.0.1:
    resolution: {integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==}

  which-pm-runs@1.1.0:
    resolution: {integrity: sha512-n1brCuqClxfFfq/Rb0ICg9giSZqCS+pLtccdag6C2HyufBrh3fBOiy9nb6ggRMvWOVH5GrdJskj5iGTZNxd7SA==}
    engines: {node: '>=4'}

  which-typed-array@1.1.19:
    resolution: {integrity: sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==}
    engines: {node: '>= 0.4'}

  which@1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  which@4.0.0:
    resolution: {integrity: sha512-GlaYyEb07DPxYCKhKzplCWBJtvxZcZMrL+4UkrTSJHHPyZU4mYYTv3qaOe77H7EODLSSopAUFAc6W8U4yqvscg==}
    engines: {node: ^16.13.0 || >=18.0.0}
    hasBin: true

  wide-align@1.1.5:
    resolution: {integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==}

  window-or-global@1.0.1:
    resolution: {integrity: sha512-tE12J/NenOv4xdVobD+AD3fT06T4KNqnzRhkv5nBIu7K+pvOH2oLCEgYP+i+5mF2jtI6FEADheOdZkA8YWET9w==}

  wkx@0.5.0:
    resolution: {integrity: sha512-Xng/d4Ichh8uN4l0FToV/258EjMGU9MGcA0HV2d9B/ZpZB3lqQm7nkOdZdm5GhKtLLhAE7PiVQwN4eN+2YJJUg==}

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wordwrap@1.0.0:
    resolution: {integrity: sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==}

  workerpool@6.5.1:
    resolution: {integrity: sha512-Fs4dNYcsdpYSAfVxhnl1L5zTksjvOJxtC5hzMNl+1t9B8hTJTdKDyZ5ju7ztgPy+ft9tBFXoOlDNiOT9WUXZlA==}

  wrap-ansi@2.1.0:
    resolution: {integrity: sha512-vAaEaDM946gbNpH5pLVNR+vX2ht6n0Bt3GXwVB1AuAqZosOvHNF3P7wDnh8KLkSqgUh0uh77le7Owgoz+Z9XBw==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  write-file-atomic@2.4.3:
    resolution: {integrity: sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ==}

  write-file-atomic@3.0.3:
    resolution: {integrity: sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==}

  write-file-atomic@5.0.1:
    resolution: {integrity: sha512-+QU2zd6OTD8XWIJCbffaiQeH9U73qIqafo1x6V1snCWYGJf6cVE0cDR4D8xRzcEnfI21IFrUPzPGtcPf8AC+Rw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  write-json-file@3.2.0:
    resolution: {integrity: sha512-3xZqT7Byc2uORAatYiP3DHUUAVEkNOswEWNs9H5KXiicRTvzYzYqKjYc4G7p+8pltvAw641lVByKVtMpf+4sYQ==}
    engines: {node: '>=6'}

  write-pkg@4.0.0:
    resolution: {integrity: sha512-v2UQ+50TNf2rNHJ8NyWttfm/EJUBWMJcx6ZTYZr6Qp52uuegWw/lBkCtCbnYZEmPRNL61m+u67dAmGxo+HTULA==}
    engines: {node: '>=8'}

  ws@3.3.3:
    resolution: {integrity: sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.17.1:
    resolution: {integrity: sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xhr@2.6.0:
    resolution: {integrity: sha512-/eCGLb5rxjx5e3mF1A7s+pLlR6CGyqWN91fv1JgER5mVWg1MZmlhBvy9kjcsOdRk8RrIujotWyJamfyrp+WIcA==}

  xml-parse-from-string@1.0.1:
    resolution: {integrity: sha512-ErcKwJTF54uRzzNMXq2X5sMIy88zJvfN2DmdoQvy7PAFJ+tPRU6ydWuOKNMyfmOjdyBQTFREi60s0Y0SyI0G0g==}

  xml2js@0.5.0:
    resolution: {integrity: sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==}
    engines: {node: '>=4.0.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}

  xmlbuilder@9.0.1:
    resolution: {integrity: sha512-zJwYqpIaUEngaPnrd14rybovsfmVyhrgLzwXm+cvtWYGrfoQ7tk7HNLPMrMBM7jGVyArCin/BYD+LG7u5olfqA==}
    engines: {node: '>=4.0'}

  xmlhttprequest-ssl@1.5.5:
    resolution: {integrity: sha512-/bFPLUgJrfGUL10AIv4Y7/CUt6so9CLtB/oFxQSHseSDNNCdC6vwwKEqwLN6wNPBg9YWXAiMu8jkf6RPRS/75Q==}
    engines: {node: '>=0.4.0'}

  xregexp@2.0.0:
    resolution: {integrity: sha512-xl/50/Cf32VsGq/1R8jJE5ajH1yMCQkpmoS10QbFZWl2Oor4H0Me64Pu2yxvsRWK3m6soJbmGfzSR7BYmDcWAA==}

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  y18n@3.2.2:
    resolution: {integrity: sha512-uGZHXkHnhF0XeeAPgnKfPv1bgKAYyVvmNL1xlKsPYZPaIHxGti2hHqvOCQv71XMsLxu1QjergkqogUnms5D3YQ==}

  y18n@4.0.3:
    resolution: {integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yaml@2.8.1:
    resolution: {integrity: sha512-lcYcMxX2PO9XMGvAJkJ3OsNMw+/7FKes7/hgerGUYWIoWu5j/+YQqcZr5JnPZWzOsEBgMbSbiSTn/dv/69Mkpw==}
    engines: {node: '>= 14.6'}
    hasBin: true

  yargs-parser@11.1.1:
    resolution: {integrity: sha512-C6kB/WJDiaxONLJQnF8ccx9SEeoTTLek8RVbaOIsrAUS8VrBEXfmeSnCZxygc+XC2sNMBIwOOnfcxiynjHsVSQ==}

  yargs-parser@18.1.3:
    resolution: {integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==}
    engines: {node: '>=6'}

  yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs-parser@4.2.1:
    resolution: {integrity: sha512-+QQWqC2xeL0N5/TE+TY6OGEqyNRM+g2/r712PDNYgiCdXYCApXf1vzfmDSLBxfGRwV+moTq/V8FnMI24JCm2Yg==}

  yargs-unparser@2.0.0:
    resolution: {integrity: sha512-7pRTIA9Qc1caZ0bZ6RYRGbHJthJWuakf+WmHK0rVeLkNrrGhfoabBNdue6kdINI6r4if7ocq9aD/n7xwKOdzOA==}
    engines: {node: '>=10'}

  yargs@12.0.5:
    resolution: {integrity: sha512-Lhz8TLaYnxq/2ObqHDql8dX8CJi97oHxrjUcYtzKbbykPtVW9WB+poxI+NM2UIzsMgNCZTIf0AQwsjK5yMAqZw==}

  yargs@15.4.1:
    resolution: {integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==}
    engines: {node: '>=8'}

  yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  yargs@6.6.0:
    resolution: {integrity: sha512-6/QWTdisjnu5UHUzQGst+UOEuEVwIzFVGBjq3jMTFNs5WJQsH/X6nMURSaScIdF5txylr1Ao9bvbWiKi2yXbwA==}

  yeast@0.1.2:
    resolution: {integrity: sha512-8HFIh676uyGYP6wP13R/j6OJ/1HwJ46snpvzE7aHAN3Ryqh2yX6Xox2B4CUmTwwOIzlG3Bs7ocsP5dZH/R1Qbg==}

  yn@3.1.1:
    resolution: {integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==}
    engines: {node: '>=6'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  z-schema@3.25.1:
    resolution: {integrity: sha512-7tDlwhrBG+oYFdXNOjILSurpfQyuVgkRe3hB2q8TEssamDHB7BbLWYkYO98nTn0FibfdFroFKDjndbgufAgS/Q==}
    hasBin: true

snapshots:

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.28.4': {}

  '@babel/core@7.28.4':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.3
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.28.3(@babel/core@7.28.4)
      '@babel/helpers': 7.28.4
      '@babel/parser': 7.28.4
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.4
      '@babel/types': 7.28.4
      '@jridgewell/remapping': 2.3.5
      convert-source-map: 2.0.0
      debug: 4.4.1(supports-color@8.1.1)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.28.3':
    dependencies:
      '@babel/parser': 7.28.4
      '@babel/types': 7.28.4
      '@jridgewell/gen-mapping': 0.3.13
      '@jridgewell/trace-mapping': 0.3.30
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.28.4
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.4
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-globals@7.28.0': {}

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.4
      '@babel/types': 7.28.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.4)':
    dependencies:
      '@babel/core': 7.28.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.28.4':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.4

  '@babel/parser@7.28.4':
    dependencies:
      '@babel/types': 7.28.4

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.4
      '@babel/types': 7.28.4

  '@babel/traverse@7.28.4':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.3
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.4
      '@babel/template': 7.27.2
      '@babel/types': 7.28.4
      debug: 4.4.1(supports-color@8.1.1)
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.28.4':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@cspotcode/source-map-support@0.8.1':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.9

  '@elastic/elasticsearch@0.1.0-beta.2':
    dependencies:
      debug: 4.4.1(supports-color@8.1.1)
      decompress-response: 4.2.1
      into-stream: 4.0.0
      ms: 2.1.3
      once: 1.4.0
      pump: 3.0.3
    transitivePeerDependencies:
      - supports-color

  '@emnapi/core@1.5.0':
    dependencies:
      '@emnapi/wasi-threads': 1.1.0
      tslib: 2.8.1

  '@emnapi/runtime@1.5.0':
    dependencies:
      tslib: 2.8.1

  '@emnapi/wasi-threads@1.1.0':
    dependencies:
      tslib: 2.8.1

  '@eslint-community/eslint-utils@4.9.0(eslint@9.35.0)':
    dependencies:
      eslint: 9.35.0
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.21.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1(supports-color@8.1.1)
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.3.1': {}

  '@eslint/core@0.13.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/core@0.15.2':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1(supports-color@8.1.1)
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.35.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.2.8':
    dependencies:
      '@eslint/core': 0.13.0
      levn: 0.4.1

  '@eslint/plugin-kit@0.3.5':
    dependencies:
      '@eslint/core': 0.15.2
      levn: 0.4.1

  '@fastify/accept-negotiator@2.0.1': {}

  '@fastify/ajv-compiler@4.0.2':
    dependencies:
      ajv: 8.17.1
      ajv-formats: 3.0.1(ajv@8.17.1)
      fast-uri: 3.1.0

  '@fastify/compress@8.0.1':
    dependencies:
      '@fastify/accept-negotiator': 2.0.1
      fastify-plugin: 5.0.1
      mime-db: 1.54.0
      minipass: 7.1.2
      peek-stream: 1.1.3
      pump: 3.0.3
      pumpify: 2.0.1
      readable-stream: 4.7.0

  '@fastify/cookie@11.0.2':
    dependencies:
      cookie: 1.0.2
      fastify-plugin: 5.0.1

  '@fastify/error@4.2.0': {}

  '@fastify/fast-json-stringify-compiler@5.0.3':
    dependencies:
      fast-json-stringify: 6.0.1

  '@fastify/forwarded@3.0.0': {}

  '@fastify/merge-json-schemas@0.2.1':
    dependencies:
      dequal: 2.0.3

  '@fastify/middie@9.0.3':
    dependencies:
      '@fastify/error': 4.2.0
      fastify-plugin: 5.0.1
      path-to-regexp: 8.3.0
      reusify: 1.1.0

  '@fastify/proxy-addr@5.0.0':
    dependencies:
      '@fastify/forwarded': 3.0.0
      ipaddr.js: 2.2.0

  '@fastify/send@3.3.1':
    dependencies:
      '@lukeed/ms': 2.0.2
      escape-html: 1.0.3
      fast-decode-uri-component: 1.0.1
      http-errors: 2.0.0
      mime: 3.0.0

  '@fastify/static@8.1.1':
    dependencies:
      '@fastify/accept-negotiator': 2.0.1
      '@fastify/send': 3.3.1
      content-disposition: 0.5.4
      fastify-plugin: 5.0.1
      fastq: 1.19.1
      glob: 11.0.3

  '@google-cloud/paginator@5.0.2':
    dependencies:
      arrify: 2.0.1
      extend: 3.0.2

  '@google-cloud/projectify@4.0.0': {}

  '@google-cloud/promisify@4.0.0': {}

  '@google-cloud/storage@7.16.0(encoding@0.1.13)':
    dependencies:
      '@google-cloud/paginator': 5.0.2
      '@google-cloud/projectify': 4.0.0
      '@google-cloud/promisify': 4.0.0
      abort-controller: 3.0.0
      async-retry: 1.3.3
      duplexify: 4.1.3
      fast-xml-parser: 4.4.1
      gaxios: 6.7.1(encoding@0.1.13)
      google-auth-library: 9.15.1(encoding@0.1.13)
      html-entities: 2.6.0
      mime: 3.0.0
      p-limit: 3.1.0
      retry-request: 7.0.2(encoding@0.1.13)
      teeny-request: 9.0.0(encoding@0.1.13)
      uuid: 8.3.2
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.7':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.4.3

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@hutson/parse-repository-url@3.0.2': {}

  '@inquirer/external-editor@1.0.1(@types/node@22.14.1)':
    dependencies:
      chardet: 2.1.0
      iconv-lite: 0.6.3
    optionalDependencies:
      '@types/node': 22.14.1

  '@ioredis/commands@1.3.1': {}

  '@isaacs/balanced-match@4.0.1': {}

  '@isaacs/brace-expansion@5.0.0':
    dependencies:
      '@isaacs/balanced-match': 4.0.1

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.2
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@isaacs/string-locale-compare@1.1.0': {}

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jridgewell/gen-mapping@0.3.13':
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.5
      '@jridgewell/trace-mapping': 0.3.30

  '@jridgewell/remapping@2.3.5':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.13
      '@jridgewell/trace-mapping': 0.3.30

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/sourcemap-codec@1.5.5': {}

  '@jridgewell/trace-mapping@0.3.30':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.5

  '@jridgewell/trace-mapping@0.3.9':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.5

  '@lerna/create@8.2.2(@types/node@22.14.1)(encoding@0.1.13)(typescript@5.6.3)':
    dependencies:
      '@npmcli/arborist': 7.5.4
      '@npmcli/package-json': 5.2.0
      '@npmcli/run-script': 8.1.0
      '@nx/devkit': 20.8.2(nx@20.8.2)
      '@octokit/plugin-enterprise-rest': 6.0.1
      '@octokit/rest': 20.1.2
      aproba: 2.0.0
      byte-size: 8.1.1
      chalk: 4.1.0
      clone-deep: 4.0.1
      cmd-shim: 6.0.3
      color-support: 1.1.3
      columnify: 1.6.0
      console-control-strings: 1.1.0
      conventional-changelog-core: 5.0.1
      conventional-recommended-bump: 7.0.1
      cosmiconfig: 9.0.0(typescript@5.6.3)
      dedent: 1.5.3
      execa: 5.0.0
      fs-extra: 11.3.1
      get-stream: 6.0.0
      git-url-parse: 14.0.0
      glob-parent: 6.0.2
      globby: 11.1.0
      graceful-fs: 4.2.11
      has-unicode: 2.0.1
      ini: 1.3.8
      init-package-json: 6.0.3
      inquirer: 8.2.7(@types/node@22.14.1)
      is-ci: 3.0.1
      is-stream: 2.0.0
      js-yaml: 4.1.0
      libnpmpublish: 9.0.9
      load-json-file: 6.2.0
      lodash: 4.17.21
      make-dir: 4.0.0
      minimatch: 3.0.5
      multimatch: 5.0.0
      node-fetch: 2.6.7(encoding@0.1.13)
      npm-package-arg: 11.0.2
      npm-packlist: 8.0.2
      npm-registry-fetch: 17.1.0
      nx: 20.8.2
      p-map: 4.0.0
      p-map-series: 2.1.0
      p-queue: 6.6.2
      p-reduce: 2.1.0
      pacote: 18.0.6
      pify: 5.0.0
      read-cmd-shim: 4.0.0
      resolve-from: 5.0.0
      rimraf: 4.4.1
      semver: 7.7.2
      set-blocking: 2.0.0
      signal-exit: 3.0.7
      slash: 3.0.0
      ssri: 10.0.6
      string-width: 4.2.3
      strong-log-transformer: 2.1.0
      tar: 6.2.1
      temp-dir: 1.0.0
      upath: 2.0.1
      uuid: 10.0.0
      validate-npm-package-license: 3.0.4
      validate-npm-package-name: 5.0.1
      wide-align: 1.1.5
      write-file-atomic: 5.0.1
      write-pkg: 4.0.0
      yargs: 17.7.2
      yargs-parser: 21.1.1
    transitivePeerDependencies:
      - '@swc-node/register'
      - '@swc/core'
      - '@types/node'
      - babel-plugin-macros
      - bluebird
      - debug
      - encoding
      - supports-color
      - typescript

  '@lukeed/ms@2.0.2': {}

  '@messageformat/core@3.4.0':
    dependencies:
      '@messageformat/date-skeleton': 1.1.0
      '@messageformat/number-skeleton': 1.2.0
      '@messageformat/parser': 5.1.1
      '@messageformat/runtime': 3.0.1
      make-plural: 7.4.0
      safe-identifier: 0.4.2

  '@messageformat/date-skeleton@1.1.0': {}

  '@messageformat/number-skeleton@1.2.0': {}

  '@messageformat/parser@5.1.1':
    dependencies:
      moo: 0.5.2

  '@messageformat/runtime@3.0.1':
    dependencies:
      make-plural: 7.4.0

  '@napi-rs/wasm-runtime@0.2.4':
    dependencies:
      '@emnapi/core': 1.5.0
      '@emnapi/runtime': 1.5.0
      '@tybys/wasm-util': 0.9.0

  '@noble/hashes@1.8.0': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@npmcli/agent@2.2.2':
    dependencies:
      agent-base: 7.1.4
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      lru-cache: 10.4.3
      socks-proxy-agent: 8.0.5
    transitivePeerDependencies:
      - supports-color

  '@npmcli/arborist@7.5.4':
    dependencies:
      '@isaacs/string-locale-compare': 1.1.0
      '@npmcli/fs': 3.1.1
      '@npmcli/installed-package-contents': 2.1.0
      '@npmcli/map-workspaces': 3.0.6
      '@npmcli/metavuln-calculator': 7.1.1
      '@npmcli/name-from-folder': 2.0.0
      '@npmcli/node-gyp': 3.0.0
      '@npmcli/package-json': 5.2.0
      '@npmcli/query': 3.1.0
      '@npmcli/redact': 2.0.1
      '@npmcli/run-script': 8.1.0
      bin-links: 4.0.4
      cacache: 18.0.4
      common-ancestor-path: 1.0.1
      hosted-git-info: 7.0.2
      json-parse-even-better-errors: 3.0.2
      json-stringify-nice: 1.1.4
      lru-cache: 10.4.3
      minimatch: 9.0.5
      nopt: 7.2.1
      npm-install-checks: 6.3.0
      npm-package-arg: 11.0.2
      npm-pick-manifest: 9.1.0
      npm-registry-fetch: 17.1.0
      pacote: 18.0.6
      parse-conflict-json: 3.0.1
      proc-log: 4.2.0
      proggy: 2.0.0
      promise-all-reject-late: 1.0.1
      promise-call-limit: 3.0.2
      read-package-json-fast: 3.0.2
      semver: 7.7.2
      ssri: 10.0.6
      treeverse: 3.0.0
      walk-up-path: 3.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color

  '@npmcli/fs@3.1.1':
    dependencies:
      semver: 7.7.2

  '@npmcli/git@5.0.8':
    dependencies:
      '@npmcli/promise-spawn': 7.0.2
      ini: 4.1.3
      lru-cache: 10.4.3
      npm-pick-manifest: 9.1.0
      proc-log: 4.2.0
      promise-inflight: 1.0.1
      promise-retry: 2.0.1
      semver: 7.7.2
      which: 4.0.0
    transitivePeerDependencies:
      - bluebird

  '@npmcli/installed-package-contents@2.1.0':
    dependencies:
      npm-bundled: 3.0.1
      npm-normalize-package-bin: 3.0.1

  '@npmcli/map-workspaces@3.0.6':
    dependencies:
      '@npmcli/name-from-folder': 2.0.0
      glob: 10.4.5
      minimatch: 9.0.5
      read-package-json-fast: 3.0.2

  '@npmcli/metavuln-calculator@7.1.1':
    dependencies:
      cacache: 18.0.4
      json-parse-even-better-errors: 3.0.2
      pacote: 18.0.6
      proc-log: 4.2.0
      semver: 7.7.2
    transitivePeerDependencies:
      - bluebird
      - supports-color

  '@npmcli/name-from-folder@2.0.0': {}

  '@npmcli/node-gyp@3.0.0': {}

  '@npmcli/package-json@5.2.0':
    dependencies:
      '@npmcli/git': 5.0.8
      glob: 10.4.5
      hosted-git-info: 7.0.2
      json-parse-even-better-errors: 3.0.2
      normalize-package-data: 6.0.2
      proc-log: 4.2.0
      semver: 7.7.2
    transitivePeerDependencies:
      - bluebird

  '@npmcli/promise-spawn@7.0.2':
    dependencies:
      which: 4.0.0

  '@npmcli/query@3.1.0':
    dependencies:
      postcss-selector-parser: 6.1.2

  '@npmcli/redact@2.0.1': {}

  '@npmcli/run-script@8.1.0':
    dependencies:
      '@npmcli/node-gyp': 3.0.0
      '@npmcli/package-json': 5.2.0
      '@npmcli/promise-spawn': 7.0.2
      node-gyp: 10.3.1
      proc-log: 4.2.0
      which: 4.0.0
    transitivePeerDependencies:
      - bluebird
      - supports-color

  '@nx/devkit@20.8.2(nx@20.8.2)':
    dependencies:
      ejs: 3.1.10
      enquirer: 2.3.6
      ignore: 5.3.2
      minimatch: 9.0.3
      nx: 20.8.2
      semver: 7.7.2
      tmp: 0.2.5
      tslib: 2.8.1
      yargs-parser: 21.1.1

  '@nx/nx-darwin-arm64@20.8.2':
    optional: true

  '@nx/nx-darwin-x64@20.8.2':
    optional: true

  '@nx/nx-freebsd-x64@20.8.2':
    optional: true

  '@nx/nx-linux-arm-gnueabihf@20.8.2':
    optional: true

  '@nx/nx-linux-arm64-gnu@20.8.2':
    optional: true

  '@nx/nx-linux-arm64-musl@20.8.2':
    optional: true

  '@nx/nx-linux-x64-gnu@20.8.2':
    optional: true

  '@nx/nx-linux-x64-musl@20.8.2':
    optional: true

  '@nx/nx-win32-arm64-msvc@20.8.2':
    optional: true

  '@nx/nx-win32-x64-msvc@20.8.2':
    optional: true

  '@octokit/auth-token@4.0.0': {}

  '@octokit/core@5.2.2':
    dependencies:
      '@octokit/auth-token': 4.0.0
      '@octokit/graphql': 7.1.1
      '@octokit/request': 8.4.1
      '@octokit/request-error': 5.1.1
      '@octokit/types': 13.10.0
      before-after-hook: 2.2.3
      universal-user-agent: 6.0.1

  '@octokit/endpoint@9.0.6':
    dependencies:
      '@octokit/types': 13.10.0
      universal-user-agent: 6.0.1

  '@octokit/graphql@7.1.1':
    dependencies:
      '@octokit/request': 8.4.1
      '@octokit/types': 13.10.0
      universal-user-agent: 6.0.1

  '@octokit/openapi-types@24.2.0': {}

  '@octokit/plugin-enterprise-rest@6.0.1': {}

  '@octokit/plugin-paginate-rest@11.4.4-cjs.2(@octokit/core@5.2.2)':
    dependencies:
      '@octokit/core': 5.2.2
      '@octokit/types': 13.10.0

  '@octokit/plugin-request-log@4.0.1(@octokit/core@5.2.2)':
    dependencies:
      '@octokit/core': 5.2.2

  '@octokit/plugin-rest-endpoint-methods@13.3.2-cjs.1(@octokit/core@5.2.2)':
    dependencies:
      '@octokit/core': 5.2.2
      '@octokit/types': 13.10.0

  '@octokit/request-error@5.1.1':
    dependencies:
      '@octokit/types': 13.10.0
      deprecation: 2.3.1
      once: 1.4.0

  '@octokit/request@8.4.1':
    dependencies:
      '@octokit/endpoint': 9.0.6
      '@octokit/request-error': 5.1.1
      '@octokit/types': 13.10.0
      universal-user-agent: 6.0.1

  '@octokit/rest@20.1.2':
    dependencies:
      '@octokit/core': 5.2.2
      '@octokit/plugin-paginate-rest': 11.4.4-cjs.2(@octokit/core@5.2.2)
      '@octokit/plugin-request-log': 4.0.1(@octokit/core@5.2.2)
      '@octokit/plugin-rest-endpoint-methods': 13.3.2-cjs.1(@octokit/core@5.2.2)

  '@octokit/types@13.10.0':
    dependencies:
      '@octokit/openapi-types': 24.2.0

  '@opentelemetry/api@1.9.0': {}

  '@paralleldrive/cuid2@2.2.2':
    dependencies:
      '@noble/hashes': 1.8.0

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@sigstore/bundle@2.3.2':
    dependencies:
      '@sigstore/protobuf-specs': 0.3.3

  '@sigstore/core@1.1.0': {}

  '@sigstore/protobuf-specs@0.3.3': {}

  '@sigstore/sign@2.3.2':
    dependencies:
      '@sigstore/bundle': 2.3.2
      '@sigstore/core': 1.1.0
      '@sigstore/protobuf-specs': 0.3.3
      make-fetch-happen: 13.0.1
      proc-log: 4.2.0
      promise-retry: 2.0.1
    transitivePeerDependencies:
      - supports-color

  '@sigstore/tuf@2.3.4':
    dependencies:
      '@sigstore/protobuf-specs': 0.3.3
      tuf-js: 2.2.1
    transitivePeerDependencies:
      - supports-color

  '@sigstore/verify@1.2.1':
    dependencies:
      '@sigstore/bundle': 2.3.2
      '@sigstore/core': 1.1.0
      '@sigstore/protobuf-specs': 0.3.3

  '@sinclair/typebox@0.27.8': {}

  '@sinonjs/commons@3.0.1':
    dependencies:
      type-detect: 4.0.8

  '@sinonjs/fake-timers@10.3.0':
    dependencies:
      '@sinonjs/commons': 3.0.1

  '@sinonjs/fake-timers@11.3.1':
    dependencies:
      '@sinonjs/commons': 3.0.1

  '@sinonjs/samsam@8.0.3':
    dependencies:
      '@sinonjs/commons': 3.0.1
      type-detect: 4.1.0

  '@sinonjs/text-encoding@0.7.3': {}

  '@skywind-group/gelf-stream@1.2.6':
    dependencies:
      gelfling: 0.3.1

  '@skywind-group/sw-adapter-core@2.0.0(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(bole@5.0.15)(jsonwebtoken@9.0.2)(pg@8.14.1)(sequelize@6.37.7(pg@8.14.1))':
    dependencies:
      '@skywind-group/sw-utils': 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      body-parser: 1.20.3
      bole: 5.0.15
      compression: 1.7.4
      cookie-parser: 1.4.7
      express: 4.21.1
      express-mung: 0.5.1
      express-validator: 5.3.1
      gelf-stream-renewed: 1.2.2
      jsonwebtoken: 9.0.2
      method-override: 3.0.0
      pg: 8.14.1
      reflect-metadata: 0.2.2
      request: 2.88.2
      sequelize: 6.37.7(pg@8.14.1)
    transitivePeerDependencies:
      - supports-color

  '@skywind-group/sw-adapter-regulation-support@1.0.2(generic-pool@3.9.0)(ioredis@5.5.0)':
    optionalDependencies:
      generic-pool: 3.9.0
      ioredis: 5.5.0

  '@skywind-group/sw-currency-exchange@2.3.19(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(superagent@10.2.3)':
    dependencies:
      jsonwebtoken: 9.0.2
      node-schedule: 2.1.1
      superagent: 10.2.3

  '@skywind-group/sw-deferred-payment-cache@2.2.0': {}

  '@skywind-group/sw-deferred-payment-client@2.2.0(superagent@10.2.3)':
    dependencies:
      superagent: 10.2.3

  '@skywind-group/sw-deferred-payment@2.1.0': {}

  '@skywind-group/sw-domain-routing@3.1.0(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))':
    dependencies:
      '@elastic/elasticsearch': 0.1.0-beta.2
      '@skywind-group/sw-utils': 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      bole: 3.0.2
      bole-console: 0.1.10
      cls-hooked: 4.2.2
      express-prom-bundle: 6.6.0(prom-client@14.2.0)
      fastify: 2.15.3
      fastify-compress: 2.0.1
      gelf-stream-renewed: 1.2.2
      generic-pool: 3.9.0
      ioredis: 5.4.2
      node-schedule: 2.1.1
      pg: 8.14.1
      prom-client: 14.2.0
      reflect-metadata: 0.1.13
      sequelize: 6.37.4(pg@8.14.1)
    transitivePeerDependencies:
      - ibm_db
      - mariadb
      - mysql2
      - oracledb
      - pg-hstore
      - pg-native
      - snowflake-sdk
      - sqlite3
      - supports-color
      - tedious

  '@skywind-group/sw-falcon-oauth@1.2.2(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))':
    dependencies:
      '@skywind-group/sw-utils': 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      axios: 1.7.7
      bole: 5.0.15
      jsonwebtoken: 9.0.2
      jwks-rsa: 3.1.0
      reflect-metadata: 0.2.2
    transitivePeerDependencies:
      - debug
      - supports-color

  '@skywind-group/sw-game-provider-ext-game-history@3.1.10(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(bole@5.0.15)(ioredis@5.5.0)(jsonwebtoken@9.0.2)(node-schedule@2.1.1)(pg@8.14.1)(sequelize@6.37.7(pg@8.14.1))(superagent-proxy@3.0.0(superagent@10.2.3))(superagent@10.2.3)':
    dependencies:
      '@skywind-group/sw-adapter-core': 2.0.0(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(bole@5.0.15)(jsonwebtoken@9.0.2)(pg@8.14.1)(sequelize@6.37.7(pg@8.14.1))
      '@skywind-group/sw-utils': 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      body-parser: 1.20.3
      cls-hooked: 4.2.2
      cookie-parser: 1.4.7
      express: 4.21.1
      express-validator: 5.3.1
      gelf-stream-renewed: 1.2.2
      inversify: 6.0.3
      inversify-express-utils: 6.4.6
      inversify-inject-decorators: 3.1.0
      ioredis: 5.5.0
      jsonwebtoken: 9.0.2
      method-override: 3.0.0
      node-schedule: 2.1.1
      pg: 8.14.1
      sequelize: 6.37.7(pg@8.14.1)
      superagent: 10.2.3
      superagent-proxy: 3.0.0(superagent@10.2.3)
    transitivePeerDependencies:
      - bole
      - supports-color

  '@skywind-group/sw-gameprovider-adapter-core@1.3.2(agentkeepalive@4.6.0)(superagent-proxy@3.0.0(superagent@10.2.3))':
    dependencies:
      agentkeepalive: 4.6.0
      superagent-proxy: 3.0.0(superagent@10.2.3)

  '@skywind-group/sw-live-core@2.0.6': {}

  '@skywind-group/sw-messaging@0.2.4':
    dependencies:
      ts-nats: 1.2.4
      uuid: 3.3.3

  '@skywind-group/sw-pop-notification@0.1.0':
    dependencies:
      uuid: 3.3.2

  '@skywind-group/sw-round-details-report@1.1.2': {}

  '@skywind-group/sw-sm-result-builder@0.1.67': {}

  '@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)':
    optionalDependencies:
      '@skywind-group/gelf-stream': 1.2.6
      agentkeepalive: 4.6.0
      crc: 4.3.2(buffer@6.0.3)
      emitter-listener: 1.1.2
      express-prom-bundle: 7.0.2(prom-client@15.0.0)
      generic-pool: 3.9.0
      hashids: 2.3.0
      ioredis: 5.5.0
      js-big-integer: 1.0.2
      jsonwebtoken: 9.0.2
      kafka-node: 5.0.0
      prom-client: 15.0.0
      uuid: 9.0.1

  '@skywind-group/sw-wallet-adapter-core@2.1.9(@skywind-group/sw-deferred-payment@2.1.0)(@skywind-group/sw-round-details-report@1.1.2)(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(fast-xml-parser@4.4.1)(superagent-proxy@3.0.0(superagent@10.2.3))':
    dependencies:
      '@skywind-group/sw-deferred-payment': 2.1.0
      '@skywind-group/sw-round-details-report': 1.1.2
      '@skywind-group/sw-utils': 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      fast-xml-parser: 4.4.1
      superagent-proxy: 3.0.0(superagent@10.2.3)

  '@skywind-group/sw-wallet@1.0.8(@skywind-group/sw-utils@2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1))(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(node-schedule@2.1.1)(superagent@10.2.3)(uuid@9.0.1)':
    dependencies:
      '@skywind-group/sw-utils': 2.5.3(@skywind-group/gelf-stream@1.2.6)(agentkeepalive@4.6.0)(crc@4.3.2(buffer@6.0.3))(emitter-listener@1.1.2)(express-prom-bundle@7.0.2(prom-client@15.0.0))(generic-pool@3.9.0)(hashids@2.3.0)(ioredis@5.5.0)(js-big-integer@1.0.2)(jsonwebtoken@9.0.2)(kafka-node@5.0.0)(prom-client@15.0.0)(uuid@9.0.1)
      agentkeepalive: 4.6.0
      crc: 4.3.2(buffer@6.0.3)
      ioredis: 5.5.0
      js-big-integer: 1.0.2
      jsonwebtoken: 9.0.2
      kafka-node: 5.0.0
      node-schedule: 2.1.1
      superagent: 10.2.3
      uuid: 9.0.1

  '@socket.io/component-emitter@3.1.2': {}

  '@tootallnate/once@1.1.2': {}

  '@tootallnate/once@2.0.0': {}

  '@tsconfig/node10@1.0.11': {}

  '@tsconfig/node12@1.0.11': {}

  '@tsconfig/node14@1.0.3': {}

  '@tsconfig/node16@1.0.4': {}

  '@tufjs/canonical-json@2.0.0': {}

  '@tufjs/models@2.0.1':
    dependencies:
      '@tufjs/canonical-json': 2.0.0
      minimatch: 9.0.5

  '@tybys/wasm-util@0.9.0':
    dependencies:
      tslib: 2.8.1

  '@types/body-parser@1.19.6':
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 22.14.1

  '@types/caseless@0.12.5': {}

  '@types/chai-as-promised@7.1.8':
    dependencies:
      '@types/chai': 4.3.20

  '@types/chai-datetime@1.0.0':
    dependencies:
      '@types/chai': 4.3.20

  '@types/chai@4.3.20': {}

  '@types/cls-hooked@4.3.9':
    dependencies:
      '@types/node': 22.14.1

  '@types/connect@3.4.38':
    dependencies:
      '@types/node': 22.14.1

  '@types/cookiejar@2.1.5': {}

  '@types/cors@2.8.19':
    dependencies:
      '@types/node': 22.14.1

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 2.1.0

  '@types/estree@1.0.8': {}

  '@types/express-http-proxy@1.6.6':
    dependencies:
      '@types/express': 4.0.35

  '@types/express-serve-static-core@4.0.48':
    dependencies:
      '@types/node': 22.14.1

  '@types/express-serve-static-core@4.19.6':
    dependencies:
      '@types/node': 22.14.1
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.5

  '@types/express-validator@2.20.33':
    dependencies:
      '@types/express': 4.0.35

  '@types/express@4.0.35':
    dependencies:
      '@types/express-serve-static-core': 4.0.48
      '@types/serve-static': 1.15.7

  '@types/express@4.17.23':
    dependencies:
      '@types/body-parser': 1.19.6
      '@types/express-serve-static-core': 4.19.6
      '@types/qs': 6.14.0
      '@types/serve-static': 1.15.7

  '@types/hashring@3.2.5':
    dependencies:
      '@types/node': 22.14.1

  '@types/http-errors@2.0.5': {}

  '@types/i18n@0.13.12': {}

  '@types/json-schema@7.0.15': {}

  '@types/jsonwebtoken@9.0.10':
    dependencies:
      '@types/ms': 2.1.0
      '@types/node': 22.14.1

  '@types/lodash@4.17.12': {}

  '@types/methods@1.1.4': {}

  '@types/mime@1.3.5': {}

  '@types/minimatch@3.0.5': {}

  '@types/minimist@1.2.5': {}

  '@types/mocha@10.0.10': {}

  '@types/ms@2.1.0': {}

  '@types/node@22.14.1':
    dependencies:
      undici-types: 6.21.0

  '@types/normalize-package-data@2.4.4': {}

  '@types/pg@8.11.11':
    dependencies:
      '@types/node': 22.14.1
      pg-protocol: 1.10.3
      pg-types: 4.1.0

  '@types/qs@6.14.0': {}

  '@types/range-parser@1.2.7': {}

  '@types/request@2.48.12':
    dependencies:
      '@types/caseless': 0.12.5
      '@types/node': 22.14.1
      '@types/tough-cookie': 4.0.5
      form-data: 2.5.5

  '@types/send@0.17.5':
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 22.14.1

  '@types/serve-static@1.15.7':
    dependencies:
      '@types/http-errors': 2.0.5
      '@types/node': 22.14.1
      '@types/send': 0.17.5

  '@types/sinon-chai@3.2.12':
    dependencies:
      '@types/chai': 4.3.20
      '@types/sinon': 10.0.20

  '@types/sinon@10.0.20':
    dependencies:
      '@types/sinonjs__fake-timers': 8.1.5

  '@types/sinonjs__fake-timers@8.1.5': {}

  '@types/superagent@8.1.9':
    dependencies:
      '@types/cookiejar': 2.1.5
      '@types/methods': 1.1.4
      '@types/node': 22.14.1
      form-data: 4.0.4

  '@types/supertest@6.0.2':
    dependencies:
      '@types/methods': 1.1.4
      '@types/superagent': 8.1.9

  '@types/to-ico@1.1.3':
    dependencies:
      '@types/node': 22.14.1

  '@types/tough-cookie@4.0.5': {}

  '@types/uuid@9.0.8': {}

  '@types/validator@13.15.3': {}

  '@typescript-eslint/eslint-plugin@8.43.0(@typescript-eslint/parser@8.43.0(eslint@9.35.0)(typescript@5.6.3))(eslint@9.35.0)(typescript@5.6.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.43.0(eslint@9.35.0)(typescript@5.6.3)
      '@typescript-eslint/scope-manager': 8.43.0
      '@typescript-eslint/type-utils': 8.43.0(eslint@9.35.0)(typescript@5.6.3)
      '@typescript-eslint/utils': 8.43.0(eslint@9.35.0)(typescript@5.6.3)
      '@typescript-eslint/visitor-keys': 8.43.0
      eslint: 9.35.0
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.43.0(eslint@9.35.0)(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.43.0
      '@typescript-eslint/types': 8.43.0
      '@typescript-eslint/typescript-estree': 8.43.0(typescript@5.6.3)
      '@typescript-eslint/visitor-keys': 8.43.0
      debug: 4.4.1(supports-color@8.1.1)
      eslint: 9.35.0
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/project-service@8.43.0(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.43.0(typescript@5.6.3)
      '@typescript-eslint/types': 8.43.0
      debug: 4.4.1(supports-color@8.1.1)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.43.0':
    dependencies:
      '@typescript-eslint/types': 8.43.0
      '@typescript-eslint/visitor-keys': 8.43.0

  '@typescript-eslint/tsconfig-utils@8.43.0(typescript@5.6.3)':
    dependencies:
      typescript: 5.6.3

  '@typescript-eslint/type-utils@8.43.0(eslint@9.35.0)(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/types': 8.43.0
      '@typescript-eslint/typescript-estree': 8.43.0(typescript@5.6.3)
      '@typescript-eslint/utils': 8.43.0(eslint@9.35.0)(typescript@5.6.3)
      debug: 4.4.1(supports-color@8.1.1)
      eslint: 9.35.0
      ts-api-utils: 2.1.0(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.43.0': {}

  '@typescript-eslint/typescript-estree@8.43.0(typescript@5.6.3)':
    dependencies:
      '@typescript-eslint/project-service': 8.43.0(typescript@5.6.3)
      '@typescript-eslint/tsconfig-utils': 8.43.0(typescript@5.6.3)
      '@typescript-eslint/types': 8.43.0
      '@typescript-eslint/visitor-keys': 8.43.0
      debug: 4.4.1(supports-color@8.1.1)
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.6.3)
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.43.0(eslint@9.35.0)(typescript@5.6.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.9.0(eslint@9.35.0)
      '@typescript-eslint/scope-manager': 8.43.0
      '@typescript-eslint/types': 8.43.0
      '@typescript-eslint/typescript-estree': 8.43.0(typescript@5.6.3)
      eslint: 9.35.0
      typescript: 5.6.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.43.0':
    dependencies:
      '@typescript-eslint/types': 8.43.0
      eslint-visitor-keys: 4.2.1

  '@yarnpkg/lockfile@1.1.0': {}

  '@yarnpkg/parsers@3.0.2':
    dependencies:
      js-yaml: 3.14.1
      tslib: 2.8.1

  '@zkochan/js-yaml@0.0.7':
    dependencies:
      argparse: 2.0.1

  JSONStream@1.3.5:
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8

  abbrev@2.0.0: {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  abstract-logging@2.0.1: {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn-walk@8.3.4:
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  add-stream@1.0.0: {}

  after@0.8.2: {}

  agent-base@4.3.0:
    dependencies:
      es6-promisify: 5.0.0

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.1(supports-color@8.1.1)
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.4: {}

  agentkeepalive@4.6.0:
    dependencies:
      humanize-ms: 1.2.1

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0

  ajv-formats@3.0.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.1.0
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-colors@4.1.3: {}

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@2.1.1: {}

  ansi-regex@3.0.1: {}

  ansi-regex@5.0.1: {}

  ansi-regex@6.2.2: {}

  ansi-styles@2.2.1: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.3: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  append-field@1.0.0: {}

  append-query@2.1.1:
    dependencies:
      extend: 3.0.2

  append-transform@2.0.0:
    dependencies:
      default-require-extensions: 3.0.1

  aproba@1.2.0:
    optional: true

  aproba@2.0.0: {}

  archy@1.0.0: {}

  are-we-there-yet@1.1.7:
    dependencies:
      delegates: 1.0.0
      readable-stream: 2.3.8
    optional: true

  arg@4.1.3: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-differ@3.0.0: {}

  array-flatten@1.1.1: {}

  array-ify@1.0.0: {}

  array-union@2.1.0: {}

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  arraybuffer.slice@0.0.7: {}

  arrify@1.0.1: {}

  arrify@2.0.1: {}

  asap@2.0.6: {}

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  assert-plus@1.0.0: {}

  assertion-error@1.1.0: {}

  ast-types@0.13.4:
    dependencies:
      tslib: 2.8.1

  ast-types@0.14.2:
    dependencies:
      tslib: 2.8.1

  async-function@1.0.0: {}

  async-hook-jl@1.7.6:
    dependencies:
      stack-chain: 1.3.7

  async-limiter@1.0.1: {}

  async-retry@1.3.3:
    dependencies:
      retry: 0.13.1

  async@1.5.2: {}

  async@2.6.4:
    dependencies:
      lodash: 4.17.21

  async@3.2.6: {}

  asynckit@0.4.0: {}

  atomic-sleep@1.0.0: {}

  authenticator-cli@1.0.5:
    dependencies:
      authenticator: 1.1.5
      cli: 1.0.1
      qrcode-terminal: 0.12.0

  authenticator@1.1.5:
    dependencies:
      authenticator-cli: 1.0.5
      notp: 2.0.3
      thirty-two: 0.0.2

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  avvio@6.5.0:
    dependencies:
      archy: 1.0.0
      debug: 4.4.1(supports-color@8.1.1)
      fastq: 1.19.1
    transitivePeerDependencies:
      - supports-color

  avvio@9.1.0:
    dependencies:
      '@fastify/error': 4.2.0
      fastq: 1.19.1

  aws-sign2@0.7.0: {}

  aws4@1.13.2: {}

  axios@1.11.0:
    dependencies:
      follow-redirects: 1.15.11
      form-data: 4.0.4
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  axios@1.7.7:
    dependencies:
      follow-redirects: 1.15.11
      form-data: 4.0.4
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-runtime@6.26.0:
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1

  backo2@1.0.2: {}

  balanced-match@1.0.2: {}

  base64-arraybuffer@0.1.5: {}

  base64-js@1.5.1: {}

  base64id@1.0.0: {}

  base64id@2.0.0: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  before-after-hook@2.2.3: {}

  better-assert@1.0.2:
    dependencies:
      callsite: 1.0.0

  big-integer@1.6.52: {}

  bignumber.js@2.4.0: {}

  bignumber.js@4.1.0: {}

  bignumber.js@9.3.1: {}

  bin-links@4.0.4:
    dependencies:
      cmd-shim: 6.0.3
      npm-normalize-package-bin: 3.0.1
      read-cmd-shim: 4.0.0
      write-file-atomic: 5.0.1

  binary-extensions@2.3.0: {}

  binary@0.3.0:
    dependencies:
      buffers: 0.1.1
      chainsaw: 0.1.0

  bindings@1.5.0:
    dependencies:
      file-uri-to-path: 1.0.0
    optional: true

  bintrees@1.0.2: {}

  bl@1.2.3:
    dependencies:
      readable-stream: 2.3.8
      safe-buffer: 5.2.1
    optional: true

  bl@2.2.1:
    dependencies:
      readable-stream: 2.3.8
      safe-buffer: 5.2.1

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  blob@0.0.5: {}

  bluebird@3.4.7: {}

  bluebird@3.7.2: {}

  bmp-js@0.0.1: {}

  bmp-js@0.0.3: {}

  body-parser@1.12.4:
    dependencies:
      bytes: 1.0.0
      content-type: 1.0.5
      debug: 2.2.0
      depd: 1.0.1
      iconv-lite: 0.4.8
      on-finished: 2.2.1
      qs: 2.4.2
      raw-body: 2.0.2
      type-is: 1.6.18
    transitivePeerDependencies:
      - supports-color

  body-parser@1.18.2:
    dependencies:
      bytes: 3.0.0
      content-type: 1.0.5
      debug: 2.6.9
      depd: 1.1.2
      http-errors: 1.6.3
      iconv-lite: 0.4.19
      on-finished: 2.3.0
      qs: 6.5.1
      raw-body: 2.3.2
      type-is: 1.6.18
    transitivePeerDependencies:
      - supports-color

  body-parser@1.20.3:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bole-console@0.1.10:
    dependencies:
      clone: 0.2.0

  bole@3.0.2:
    dependencies:
      fast-safe-stringify: 1.1.13
      individual: 3.0.0

  bole@5.0.15:
    dependencies:
      fast-safe-stringify: 2.1.1
      individual: 3.0.0

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browser-stdout@1.3.1: {}

  browserslist@4.25.4:
    dependencies:
      caniuse-lite: 1.0.30001741
      electron-to-chromium: 1.5.215
      node-releases: 2.0.20
      update-browserslist-db: 1.1.3(browserslist@4.25.4)

  buffer-alloc-unsafe@1.1.0: {}

  buffer-alloc@1.2.0:
    dependencies:
      buffer-alloc-unsafe: 1.1.0
      buffer-fill: 1.0.0

  buffer-crc32@0.2.13: {}

  buffer-equal-constant-time@1.0.1: {}

  buffer-equal@0.0.1: {}

  buffer-fill@1.0.0: {}

  buffer-from@1.1.2: {}

  buffer-indexof-polyfill@1.0.2: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffermaker@1.2.1:
    dependencies:
      long: 1.1.2

  buffers@0.1.1: {}

  builtin-modules@3.3.0: {}

  builtin-modules@5.0.0: {}

  busboy@0.2.14:
    dependencies:
      dicer: 0.2.5
      readable-stream: 1.1.14

  byte-size@8.1.1: {}

  bytes@1.0.0: {}

  bytes@2.1.0: {}

  bytes@3.0.0: {}

  bytes@3.1.2: {}

  cacache@18.0.4:
    dependencies:
      '@npmcli/fs': 3.1.1
      fs-minipass: 3.0.3
      glob: 10.4.5
      lru-cache: 10.4.3
      minipass: 7.1.2
      minipass-collect: 2.0.1
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      p-map: 4.0.0
      ssri: 10.0.6
      tar: 6.2.1
      unique-filename: 3.0.0

  caching-transform@4.0.0:
    dependencies:
      hasha: 5.2.2
      make-dir: 3.1.0
      package-hash: 4.0.0
      write-file-atomic: 3.0.3

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsite@1.0.0: {}

  callsites@3.1.0: {}

  camelcase-keys@6.2.2:
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1

  camelcase@3.0.0: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  can-promise@0.0.1:
    dependencies:
      window-or-global: 1.0.1

  caniuse-lite@1.0.30001741: {}

  caseless@0.12.0: {}

  centra@2.7.0:
    dependencies:
      follow-redirects: 1.15.11
    transitivePeerDependencies:
      - debug

  chai-as-promised@7.1.2(chai@4.3.10):
    dependencies:
      chai: 4.3.10
      check-error: 1.0.3

  chai-datetime@1.8.1:
    dependencies:
      chai: 4.3.10

  chai-exclude@2.0.2(chai@4.3.10):
    dependencies:
      chai: 4.3.10
      fclone: 1.0.11

  chai-shallow-deep-equal@1.4.4(chai@4.3.10):
    dependencies:
      chai: 4.3.10

  chai@4.3.10:
    dependencies:
      assertion-error: 1.1.0
      check-error: 1.0.3
      deep-eql: 4.1.4
      get-func-name: 2.0.2
      loupe: 2.3.7
      pathval: 1.1.1
      type-detect: 4.1.0

  chainsaw@0.1.0:
    dependencies:
      traverse: 0.3.9

  chalk@1.1.3:
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  chalk@4.1.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chance@1.1.13: {}

  chardet@2.1.0: {}

  check-error@1.0.3:
    dependencies:
      get-func-name: 2.0.2

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chownr@1.1.4:
    optional: true

  chownr@2.0.0: {}

  ci-info@3.9.0: {}

  ci-info@4.3.0: {}

  cidr-regex@3.1.1:
    dependencies:
      ip-regex: 4.3.0

  clean-regexp@1.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  clean-stack@2.2.0: {}

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-spinners@2.6.1: {}

  cli-spinners@2.9.2: {}

  cli-width@3.0.0: {}

  cli@1.0.1:
    dependencies:
      exit: 0.1.2
      glob: 7.2.3

  cliui@3.2.0:
    dependencies:
      string-width: 1.0.2
      strip-ansi: 3.0.1
      wrap-ansi: 2.1.0

  cliui@4.1.0:
    dependencies:
      string-width: 2.1.1
      strip-ansi: 4.0.0
      wrap-ansi: 2.1.0

  cliui@6.0.0:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-deep@4.0.1:
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  clone@0.2.0: {}

  clone@1.0.4: {}

  clone@2.1.2: {}

  cls-hooked@4.2.2:
    dependencies:
      async-hook-jl: 1.7.6
      emitter-listener: 1.1.2
      semver: 5.7.2

  cluster-key-slot@1.1.2: {}

  cmd-shim@6.0.3: {}

  co@4.6.0: {}

  code-point-at@1.1.0: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  color-support@1.1.3: {}

  columnify@1.6.0:
    dependencies:
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.11.0: {}

  commander@2.20.3: {}

  common-ancestor-path@1.0.1: {}

  commondir@1.0.1: {}

  compare-func@2.0.0:
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0

  component-bind@1.0.0: {}

  component-emitter@1.2.1: {}

  component-emitter@1.3.1: {}

  component-inherit@0.0.3: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.54.0

  compression@1.7.4:
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  concat-stream@1.6.2:
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6

  concat-stream@2.0.0:
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2
      typedarray: 0.0.6

  connection-parse@0.0.7: {}

  console-control-strings@1.1.0: {}

  content-disposition@0.5.2: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  conventional-changelog-angular@7.0.0:
    dependencies:
      compare-func: 2.0.0

  conventional-changelog-core@5.0.1:
    dependencies:
      add-stream: 1.0.0
      conventional-changelog-writer: 6.0.1
      conventional-commits-parser: 4.0.0
      dateformat: 3.0.3
      get-pkg-repo: 4.2.1
      git-raw-commits: 3.0.0
      git-remote-origin-url: 2.0.0
      git-semver-tags: 5.0.1
      normalize-package-data: 3.0.3
      read-pkg: 3.0.0
      read-pkg-up: 3.0.0

  conventional-changelog-preset-loader@3.0.0: {}

  conventional-changelog-writer@6.0.1:
    dependencies:
      conventional-commits-filter: 3.0.0
      dateformat: 3.0.3
      handlebars: 4.7.8
      json-stringify-safe: 5.0.1
      meow: 8.1.2
      semver: 7.7.2
      split: 1.0.1

  conventional-commits-filter@3.0.0:
    dependencies:
      lodash.ismatch: 4.4.0
      modify-values: 1.0.1

  conventional-commits-parser@4.0.0:
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 1.0.1
      meow: 8.1.2
      split2: 3.2.2

  conventional-recommended-bump@7.0.1:
    dependencies:
      concat-stream: 2.0.0
      conventional-changelog-preset-loader: 3.0.0
      conventional-commits-filter: 3.0.0
      conventional-commits-parser: 4.0.0
      git-raw-commits: 3.0.0
      git-semver-tags: 5.0.1
      meow: 8.1.2

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cookie-parser@1.4.7:
    dependencies:
      cookie: 0.7.2
      cookie-signature: 1.0.6

  cookie-signature@1.0.6: {}

  cookie@0.3.1: {}

  cookie@0.4.2: {}

  cookie@0.7.1: {}

  cookie@0.7.2: {}

  cookie@1.0.2: {}

  cookiejar@2.0.6: {}

  cookiejar@2.1.4: {}

  core-js-compat@3.45.1:
    dependencies:
      browserslist: 4.25.4

  core-js@2.6.12: {}

  core-util-is@1.0.2: {}

  core-util-is@1.0.3: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  cosmiconfig@9.0.0(typescript@5.6.3):
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
    optionalDependencies:
      typescript: 5.6.3

  crc@4.3.2(buffer@6.0.3):
    optionalDependencies:
      buffer: 6.0.3

  create-require@1.1.1: {}

  cron-parser@4.9.0:
    dependencies:
      luxon: 3.7.2

  cross-spawn@5.1.0:
    dependencies:
      lru-cache: 4.1.5
      shebang-command: 1.2.0
      which: 1.3.1

  cross-spawn@6.0.6:
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  cssesc@3.0.0: {}

  dargs@7.0.0: {}

  dashdash@1.14.1:
    dependencies:
      assert-plus: 1.0.0

  data-uri-to-buffer@1.2.0: {}

  data-uri-to-buffer@3.0.1: {}

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  dateformat@3.0.3: {}

  debug@2.2.0:
    dependencies:
      ms: 0.7.1

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.1.0:
    dependencies:
      ms: 2.0.0

  debug@3.2.7(supports-color@5.5.0):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 5.5.0

  debug@4.3.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.1(supports-color@8.1.1):
    dependencies:
      ms: 2.1.3
    optionalDependencies:
      supports-color: 8.1.1

  decamelize-keys@1.1.1:
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1

  decamelize@1.2.0: {}

  decamelize@4.0.0: {}

  decompress-response@3.3.0:
    dependencies:
      mimic-response: 1.0.1
    optional: true

  decompress-response@4.2.1:
    dependencies:
      mimic-response: 2.1.0

  dedent@1.5.3: {}

  deep-eql@4.1.4:
    dependencies:
      type-detect: 4.1.0

  deep-extend@0.6.0:
    optional: true

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  default-require-extensions@3.0.1:
    dependencies:
      strip-bom: 4.0.0

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-lazy-prop@2.0.0: {}

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  degenerator@1.0.4:
    dependencies:
      ast-types: 0.14.2
      escodegen: 1.14.3
      esprima: 3.1.3

  degenerator@3.0.4:
    dependencies:
      ast-types: 0.13.4
      escodegen: 1.14.3
      esprima: 4.0.1
      vm2: 3.9.19

  delayed-stream@1.0.0: {}

  delegates@1.0.0:
    optional: true

  denque@1.5.1: {}

  denque@2.1.0: {}

  depd@1.0.1: {}

  depd@1.1.1: {}

  depd@1.1.2: {}

  depd@2.0.0: {}

  deprecate@1.0.0: {}

  deprecation@2.3.1: {}

  dequal@2.0.3: {}

  destroy@1.0.4: {}

  destroy@1.2.0: {}

  detect-indent@5.0.0: {}

  detect-libc@1.0.3:
    optional: true

  dezalgo@1.0.4:
    dependencies:
      asap: 2.0.6
      wrappy: 1.0.2

  dicer@0.2.5:
    dependencies:
      readable-stream: 1.1.14
      streamsearch: 0.1.2

  diff-sequences@29.6.3: {}

  diff@4.0.2: {}

  diff@5.2.0: {}

  dijkstrajs@1.0.3: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dom-walk@0.1.2: {}

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dotenv-expand@11.0.7:
    dependencies:
      dotenv: 16.6.1

  dotenv@16.4.7: {}

  dotenv@16.6.1: {}

  dottie@2.0.6: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  duplexer2@0.1.4:
    dependencies:
      readable-stream: 2.3.8

  duplexer@0.1.2: {}

  duplexify@3.7.1:
    dependencies:
      end-of-stream: 1.4.5
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.3

  duplexify@4.1.3:
    dependencies:
      end-of-stream: 1.4.5
      inherits: 2.0.4
      readable-stream: 3.6.2
      stream-shift: 1.0.3

  eastasianwidth@0.2.0: {}

  ecc-jsbn@0.1.2:
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  ee-first@1.1.0: {}

  ee-first@1.1.1: {}

  ejs@3.1.10:
    dependencies:
      jake: 10.9.4

  electron-to-chromium@1.5.215: {}

  emitter-listener@1.1.2:
    dependencies:
      shimmer: 1.2.1

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  encoding-negotiator@2.0.1: {}

  encoding@0.1.13:
    dependencies:
      iconv-lite: 0.6.3
    optional: true

  end-of-stream@1.4.5:
    dependencies:
      once: 1.4.0

  engine.io-client@3.2.1:
    dependencies:
      component-emitter: 1.2.1
      component-inherit: 0.0.3
      debug: 3.1.0
      engine.io-parser: 2.1.3
      has-cors: 1.1.0
      indexof: 0.0.1
      parseqs: 0.0.5
      parseuri: 0.0.5
      ws: 3.3.3
      xmlhttprequest-ssl: 1.5.5
      yeast: 0.1.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  engine.io-parser@2.1.3:
    dependencies:
      after: 0.8.2
      arraybuffer.slice: 0.0.7
      base64-arraybuffer: 0.1.5
      blob: 0.0.5
      has-binary2: 1.0.3

  engine.io-parser@5.2.3: {}

  engine.io@3.2.1:
    dependencies:
      accepts: 1.3.8
      base64id: 1.0.0
      cookie: 0.3.1
      debug: 3.1.0
      engine.io-parser: 2.1.3
      ws: 3.3.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  engine.io@6.6.4:
    dependencies:
      '@types/cors': 2.8.19
      '@types/node': 22.14.1
      accepts: 1.3.8
      base64id: 2.0.0
      cookie: 0.7.2
      cors: 2.8.5
      debug: 4.3.7
      engine.io-parser: 5.2.3
      ws: 8.17.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  enquirer@2.3.6:
    dependencies:
      ansi-colors: 4.1.3

  env-paths@2.2.1: {}

  envinfo@7.13.0: {}

  err-code@2.0.3: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-abstract@1.24.0:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-negative-zero: 2.0.3
      is-regex: 1.2.1
      is-set: 2.0.3
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      stop-iteration-iterator: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  es6-error@4.1.1: {}

  es6-promise@3.3.1: {}

  es6-promise@4.2.8: {}

  es6-promisify@5.0.0:
    dependencies:
      es6-promise: 4.2.8

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  escodegen@1.14.3:
    dependencies:
      esprima: 4.0.1
      estraverse: 4.3.0
      esutils: 2.0.3
      optionator: 0.8.3
    optionalDependencies:
      source-map: 0.6.1

  eslint-plugin-sonarjs@3.0.5(eslint@9.35.0):
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      builtin-modules: 3.3.0
      bytes: 3.1.2
      eslint: 9.35.0
      functional-red-black-tree: 1.0.1
      jsx-ast-utils-x: 0.1.0
      lodash.merge: 4.6.2
      minimatch: 9.0.5
      scslre: 0.3.0
      semver: 7.7.2
      typescript: 5.6.3

  eslint-plugin-unicorn@58.0.0(eslint@9.35.0):
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      '@eslint-community/eslint-utils': 4.9.0(eslint@9.35.0)
      '@eslint/plugin-kit': 0.2.8
      ci-info: 4.3.0
      clean-regexp: 1.0.0
      core-js-compat: 3.45.1
      eslint: 9.35.0
      esquery: 1.6.0
      globals: 16.4.0
      indent-string: 5.0.0
      is-builtin-module: 5.0.0
      jsesc: 3.1.0
      pluralize: 8.0.0
      read-package-up: 11.0.0
      regexp-tree: 0.1.27
      regjsparser: 0.12.0
      semver: 7.7.2
      strip-indent: 4.0.0

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.35.0:
    dependencies:
      '@eslint-community/eslint-utils': 4.9.0(eslint@9.35.0)
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.21.0
      '@eslint/config-helpers': 0.3.1
      '@eslint/core': 0.15.2
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.35.0
      '@eslint/plugin-kit': 0.3.5
      '@humanfs/node': 0.16.7
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1(supports-color@8.1.1)
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    transitivePeerDependencies:
      - supports-color

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esprima@3.1.3: {}

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  event-target-shim@5.0.1: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  execa@1.0.0:
    dependencies:
      cross-spawn: 6.0.6
      get-stream: 4.1.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0

  execa@5.0.0:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.0
      human-signals: 2.1.0
      is-stream: 2.0.0
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exif-parser@0.1.12: {}

  exit@0.1.2: {}

  expand-template@2.0.3:
    optional: true

  exponential-backoff@3.1.2: {}

  express-csv@0.6.0: {}

  express-http-proxy@2.1.1:
    dependencies:
      debug: 3.2.7(supports-color@5.5.0)
      es6-promise: 4.2.8
      raw-body: 2.5.2
    transitivePeerDependencies:
      - supports-color

  express-mung@0.5.1: {}

  express-prom-bundle@6.6.0(prom-client@14.2.0):
    dependencies:
      on-finished: 2.4.1
      prom-client: 14.2.0
      url-value-parser: 2.2.0

  express-prom-bundle@7.0.2(prom-client@15.0.0):
    dependencies:
      '@types/express': 4.17.23
      express: 4.21.1
      on-finished: 2.4.1
      prom-client: 15.0.0
      url-value-parser: 2.2.0
    transitivePeerDependencies:
      - supports-color

  express-validator@5.3.1:
    dependencies:
      lodash: 4.17.21
      validator: 10.11.0

  express@4.16.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.18.2
      content-disposition: 0.5.2
      content-type: 1.0.5
      cookie: 0.3.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 1.1.2
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.1.0
      fresh: 0.5.2
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.3.0
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.5.1
      range-parser: 1.2.1
      safe-buffer: 5.1.1
      send: 0.16.1
      serve-static: 1.13.1
      setprototypeof: 1.1.0
      statuses: 1.3.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  express@4.21.1:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.10
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  extend@3.0.0: {}

  extend@3.0.2: {}

  extsprintf@1.3.0: {}

  factory-girl@5.0.2:
    dependencies:
      babel-runtime: 6.26.0
      chance: 1.1.13

  fast-decode-uri-component@1.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-json-stringify@1.21.0:
    dependencies:
      ajv: 6.12.6
      deepmerge: 4.3.1
      string-similarity: 4.0.4

  fast-json-stringify@6.0.1:
    dependencies:
      '@fastify/merge-json-schemas': 0.2.1
      ajv: 8.17.1
      ajv-formats: 3.0.1(ajv@8.17.1)
      fast-uri: 3.1.0
      json-schema-ref-resolver: 2.0.1
      rfdc: 1.4.1

  fast-levenshtein@2.0.6: {}

  fast-printf@1.6.10: {}

  fast-querystring@1.1.2:
    dependencies:
      fast-decode-uri-component: 1.0.1

  fast-redact@2.1.0: {}

  fast-redact@3.5.0: {}

  fast-safe-stringify@1.1.13: {}

  fast-safe-stringify@2.1.1: {}

  fast-uri@3.1.0: {}

  fast-xml-parser@4.4.1:
    dependencies:
      strnum: 1.1.2

  fastify-compress@2.0.1:
    dependencies:
      encoding-negotiator: 2.0.1
      fastify-plugin: 1.6.1
      into-stream: 4.0.0
      is-deflate: 1.0.0
      is-gzip: 1.0.0
      is-stream: 2.0.1
      is-zip: 1.0.0
      mime-db: 1.54.0
      minipass: 2.9.0
      peek-stream: 1.1.3
      pump: 3.0.3
      pumpify: 2.0.1
      string-to-stream: 3.0.1
      unzipper: 0.10.14

  fastify-plugin@1.6.1:
    dependencies:
      semver: 6.3.1

  fastify-plugin@5.0.1: {}

  fastify@2.15.3:
    dependencies:
      abstract-logging: 2.0.1
      ajv: 6.12.6
      avvio: 6.5.0
      fast-json-stringify: 1.21.0
      find-my-way: 2.2.5
      flatstr: 1.0.12
      light-my-request: 3.8.0
      middie: 4.1.0
      pino: 5.17.0
      proxy-addr: 2.0.7
      readable-stream: 3.6.2
      rfdc: 1.4.1
      secure-json-parse: 2.7.0
      tiny-lru: 7.0.6
    transitivePeerDependencies:
      - supports-color

  fastify@5.3.0:
    dependencies:
      '@fastify/ajv-compiler': 4.0.2
      '@fastify/error': 4.2.0
      '@fastify/fast-json-stringify-compiler': 5.0.3
      '@fastify/proxy-addr': 5.0.0
      abstract-logging: 2.0.1
      avvio: 9.1.0
      fast-json-stringify: 6.0.1
      find-my-way: 9.3.0
      light-my-request: 6.6.0
      pino: 9.9.5
      process-warning: 5.0.0
      rfdc: 1.4.1
      secure-json-parse: 4.0.0
      semver: 7.7.2
      toad-cache: 3.7.0

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fclone@1.0.11: {}

  figures@3.2.0:
    dependencies:
      escape-string-regexp: 1.0.5

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  file-type@3.9.0: {}

  file-uri-to-path@1.0.0: {}

  file-uri-to-path@2.0.0: {}

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.1.0:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.3.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  finalhandler@1.3.1:
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-cache-dir@3.3.2:
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0

  find-my-way@2.2.5:
    dependencies:
      fast-decode-uri-component: 1.0.1
      safe-regex2: 2.0.0
      semver-store: 0.3.0

  find-my-way@9.3.0:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-querystring: 1.1.2
      safe-regex2: 5.0.0

  find-up-simple@1.0.1: {}

  find-up@1.1.2:
    dependencies:
      path-exists: 2.1.0
      pinkie-promise: 2.0.1

  find-up@2.1.0:
    dependencies:
      locate-path: 2.0.0

  find-up@3.0.0:
    dependencies:
      locate-path: 3.0.0

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flat@5.0.2: {}

  flatstr@1.0.12: {}

  flatted@3.3.3: {}

  follow-redirects@1.15.11: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  foreground-child@2.0.0:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 3.0.7

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  forever-agent@0.6.1: {}

  form-data@1.0.0-rc3:
    dependencies:
      async: 1.5.2
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@2.3.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@2.5.5:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35
      safe-buffer: 5.2.1

  form-data@4.0.4:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35

  formidable@1.0.17: {}

  formidable@1.2.6: {}

  formidable@2.1.5:
    dependencies:
      '@paralleldrive/cuid2': 2.2.2
      dezalgo: 1.0.4
      once: 1.4.0
      qs: 6.14.0

  formidable@3.5.4:
    dependencies:
      '@paralleldrive/cuid2': 2.2.2
      dezalgo: 1.0.4
      once: 1.4.0

  forwarded@0.2.0: {}

  fresh@0.5.2: {}

  from2@2.3.0:
    dependencies:
      inherits: 2.0.4
      readable-stream: 2.3.8

  fromentries@1.3.2: {}

  front-matter@4.0.2:
    dependencies:
      js-yaml: 3.14.1

  fs-constants@1.0.0: {}

  fs-extra@11.3.1:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.2.0
      universalify: 2.0.1

  fs-extra@8.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6

  fs-minipass@3.0.3:
    dependencies:
      minipass: 7.1.2

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  fstream@1.0.12:
    dependencies:
      graceful-fs: 4.2.11
      inherits: 2.0.4
      mkdirp: 0.5.6
      rimraf: 2.7.1

  ftp@0.3.10:
    dependencies:
      readable-stream: 1.1.14
      xregexp: 2.0.0

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functional-red-black-tree@1.0.1: {}

  functions-have-names@1.2.3: {}

  gauge@2.7.4:
    dependencies:
      aproba: 1.2.0
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 1.0.2
      strip-ansi: 3.0.1
      wide-align: 1.1.5
    optional: true

  gaxios@6.7.1(encoding@0.1.13):
    dependencies:
      extend: 3.0.2
      https-proxy-agent: 7.0.6
      is-stream: 2.0.1
      node-fetch: 2.7.0(encoding@0.1.13)
      uuid: 9.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  gcp-metadata@6.1.1(encoding@0.1.13):
    dependencies:
      gaxios: 6.7.1(encoding@0.1.13)
      google-logging-utils: 0.0.2
      json-bigint: 1.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  gelf-stream-renewed@1.2.2:
    dependencies:
      gelfling: 0.3.1

  gelfling@0.3.1: {}

  generic-pool@3.9.0: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@1.0.3: {}

  get-caller-file@2.0.5: {}

  get-func-name@2.0.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-package-type@0.1.0: {}

  get-pkg-repo@4.2.1:
    dependencies:
      '@hutson/parse-repository-url': 3.0.2
      hosted-git-info: 4.1.0
      through2: 2.0.5
      yargs: 16.2.0

  get-port@5.1.1: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@2.3.1:
    dependencies:
      object-assign: 4.1.1
      pinkie-promise: 2.0.1

  get-stream@4.1.0:
    dependencies:
      pump: 3.0.3

  get-stream@6.0.0: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  get-uri@2.0.4:
    dependencies:
      data-uri-to-buffer: 1.2.0
      debug: 2.6.9
      extend: 3.0.2
      file-uri-to-path: 1.0.0
      ftp: 0.3.10
      readable-stream: 2.3.8
    transitivePeerDependencies:
      - supports-color

  get-uri@3.0.2:
    dependencies:
      '@tootallnate/once': 1.1.2
      data-uri-to-buffer: 3.0.1
      debug: 4.4.1(supports-color@8.1.1)
      file-uri-to-path: 2.0.0
      fs-extra: 8.1.0
      ftp: 0.3.10
    transitivePeerDependencies:
      - supports-color

  getpass@0.1.7:
    dependencies:
      assert-plus: 1.0.0

  git-raw-commits@3.0.0:
    dependencies:
      dargs: 7.0.0
      meow: 8.1.2
      split2: 3.2.2

  git-remote-origin-url@2.0.0:
    dependencies:
      gitconfiglocal: 1.0.0
      pify: 2.3.0

  git-semver-tags@5.0.1:
    dependencies:
      meow: 8.1.2
      semver: 7.7.2

  git-up@7.0.0:
    dependencies:
      is-ssh: 1.4.1
      parse-url: 8.1.0

  git-url-parse@14.0.0:
    dependencies:
      git-up: 7.0.0

  gitconfiglocal@1.0.0:
    dependencies:
      ini: 1.3.8

  github-from-package@0.0.0:
    optional: true

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@11.0.3:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 4.1.1
      minimatch: 10.0.3
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 2.0.0

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@8.1.0:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0

  glob@9.3.5:
    dependencies:
      fs.realpath: 1.0.0
      minimatch: 8.0.4
      minipass: 4.2.8
      path-scurry: 1.11.1

  global@4.4.0:
    dependencies:
      min-document: 2.19.0
      process: 0.11.10

  globals@14.0.0: {}

  globals@16.4.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  google-auth-library@9.15.1(encoding@0.1.13):
    dependencies:
      base64-js: 1.5.1
      ecdsa-sig-formatter: 1.0.11
      gaxios: 6.7.1(encoding@0.1.13)
      gcp-metadata: 6.1.1(encoding@0.1.13)
      gtoken: 7.1.0(encoding@0.1.13)
      jws: 4.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  google-logging-utils@0.0.2: {}

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  graphlib@2.1.8:
    dependencies:
      lodash: 4.17.21

  gtoken@7.1.0(encoding@0.1.13):
    dependencies:
      gaxios: 6.7.1(encoding@0.1.13)
      jws: 4.0.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  handlebars@4.7.8:
    dependencies:
      minimist: 1.2.8
      neo-async: 2.6.2
      source-map: 0.6.1
      wordwrap: 1.0.0
    optionalDependencies:
      uglify-js: 3.19.3

  har-schema@2.0.0: {}

  har-validator@5.1.5:
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  hard-rejection@2.1.0: {}

  has-ansi@2.0.0:
    dependencies:
      ansi-regex: 2.1.1

  has-bigints@1.1.0: {}

  has-binary2@1.0.3:
    dependencies:
      isarray: 2.0.1

  has-cors@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  has-unicode@2.0.1: {}

  hasha@5.2.2:
    dependencies:
      is-stream: 2.0.1
      type-fest: 0.8.1

  hashids@2.3.0: {}

  hashring@3.2.0:
    dependencies:
      connection-parse: 0.0.7
      simple-lru-cache: 0.0.2

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hosted-git-info@2.8.9: {}

  hosted-git-info@4.1.0:
    dependencies:
      lru-cache: 6.0.0

  hosted-git-info@7.0.2:
    dependencies:
      lru-cache: 10.4.3

  html-entities@2.6.0: {}

  html-escaper@2.0.2: {}

  http-cache-semantics@4.2.0: {}

  http-errors@1.6.2:
    dependencies:
      depd: 1.1.1
      inherits: 2.0.3
      setprototypeof: 1.0.3
      statuses: 1.3.1

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-proxy-agent@2.1.0:
    dependencies:
      agent-base: 4.3.0
      debug: 3.1.0
    transitivePeerDependencies:
      - supports-color

  http-proxy-agent@4.0.1:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.4.1(supports-color@8.1.1)
    transitivePeerDependencies:
      - supports-color

  http-proxy-agent@5.0.0:
    dependencies:
      '@tootallnate/once': 2.0.0
      agent-base: 6.0.2
      debug: 4.4.1(supports-color@8.1.1)
    transitivePeerDependencies:
      - supports-color

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.4
      debug: 4.4.1(supports-color@8.1.1)
    transitivePeerDependencies:
      - supports-color

  http-signature@1.2.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  http-status-codes@1.4.0: {}

  http-status-codes@2.3.0: {}

  https-proxy-agent@2.2.4:
    dependencies:
      agent-base: 4.3.0
      debug: 3.2.7(supports-color@5.5.0)
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1(supports-color@8.1.1)
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.4
      debug: 4.4.1(supports-color@8.1.1)
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  i18n@0.15.1:
    dependencies:
      '@messageformat/core': 3.4.0
      debug: 4.4.1(supports-color@8.1.1)
      fast-printf: 1.6.10
      make-plural: 7.4.0
      math-interval-parser: 2.0.1
      mustache: 4.2.0
    transitivePeerDependencies:
      - supports-color

  iconv-lite@0.4.19: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.4.8: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  ignore-by-default@1.0.1: {}

  ignore-walk@6.0.5:
    dependencies:
      minimatch: 9.0.5

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  image-size@0.5.5: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-local@3.1.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  indent-string@5.0.0: {}

  index-to-position@1.1.0: {}

  indexof@0.0.1: {}

  individual@3.0.0: {}

  inflection@1.13.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  ini@1.3.8: {}

  ini@4.1.3: {}

  init-package-json@6.0.3:
    dependencies:
      '@npmcli/package-json': 5.2.0
      npm-package-arg: 11.0.2
      promzard: 1.0.2
      read: 3.0.1
      semver: 7.7.2
      validate-npm-package-license: 3.0.4
      validate-npm-package-name: 5.0.1
    transitivePeerDependencies:
      - bluebird

  inquirer@8.2.7(@types/node@22.14.1):
    dependencies:
      '@inquirer/external-editor': 1.0.1(@types/node@22.14.1)
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      ora: 5.4.1
      run-async: 2.4.1
      rxjs: 7.8.2
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
      wrap-ansi: 6.2.0
    transitivePeerDependencies:
      - '@types/node'

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  into-stream@4.0.0:
    dependencies:
      from2: 2.3.0
      p-is-promise: 2.1.0

  inversify-express-utils@6.3.2:
    dependencies:
      express: 4.16.2
      http-status-codes: 1.4.0
    transitivePeerDependencies:
      - supports-color

  inversify-express-utils@6.4.6:
    dependencies:
      express: 4.21.1
      http-status-codes: 2.3.0
      inversify: 6.0.3
    transitivePeerDependencies:
      - supports-color

  inversify-inject-decorators@3.1.0: {}

  inversify@5.0.1: {}

  inversify@6.0.3: {}

  invert-kv@1.0.0: {}

  invert-kv@2.0.0: {}

  ioredis@5.4.2:
    dependencies:
      '@ioredis/commands': 1.3.1
      cluster-key-slot: 1.1.2
      debug: 4.4.1(supports-color@8.1.1)
      denque: 2.1.0
      lodash.defaults: 4.2.0
      lodash.isarguments: 3.1.0
      redis-errors: 1.2.0
      redis-parser: 3.0.0
      standard-as-callback: 2.1.0
    transitivePeerDependencies:
      - supports-color

  ioredis@5.5.0:
    dependencies:
      '@ioredis/commands': 1.3.1
      cluster-key-slot: 1.1.2
      debug: 4.4.1(supports-color@8.1.1)
      denque: 2.1.0
      lodash.defaults: 4.2.0
      lodash.isarguments: 3.1.0
      redis-errors: 1.2.0
      redis-parser: 3.0.0
      standard-as-callback: 2.1.0
    transitivePeerDependencies:
      - supports-color

  ip-address@10.0.1: {}

  ip-regex@1.0.3: {}

  ip-regex@4.3.0: {}

  ip@1.1.9: {}

  ipaddr.js@1.9.1: {}

  ipaddr.js@2.2.0: {}

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-builtin-module@5.0.0:
    dependencies:
      builtin-modules: 5.0.0

  is-callable@1.2.7: {}

  is-ci@3.0.1:
    dependencies:
      ci-info: 3.9.0

  is-cidr@4.0.2:
    dependencies:
      cidr-regex: 3.1.1

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-deflate@1.0.0: {}

  is-docker@2.2.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@1.0.0:
    dependencies:
      number-is-nan: 1.0.1

  is-fullwidth-code-point@2.0.0: {}

  is-fullwidth-code-point@3.0.0: {}

  is-function@1.0.2: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-gzip@1.0.0: {}

  is-interactive@1.0.0: {}

  is-lambda@1.0.1: {}

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-obj@2.0.0: {}

  is-plain-obj@1.1.0: {}

  is-plain-obj@2.1.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-ssh@1.4.1:
    dependencies:
      protocols: 2.0.2

  is-stream@1.1.0: {}

  is-stream@2.0.0: {}

  is-stream@2.0.1: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-text-path@1.0.1:
    dependencies:
      text-extensions: 1.9.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-typedarray@1.0.0: {}

  is-unicode-supported@0.1.0: {}

  is-utf8@0.2.1: {}

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-windows@1.0.2: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  is-zip@1.0.0: {}

  isarray@0.0.1: {}

  isarray@1.0.0: {}

  isarray@2.0.1: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isexe@3.1.1: {}

  isobject@3.0.1: {}

  isstream@0.1.2: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-hook@3.0.0:
    dependencies:
      append-transform: 2.0.0

  istanbul-lib-instrument@4.0.3:
    dependencies:
      '@babel/core': 7.28.4
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-processinfo@2.0.3:
    dependencies:
      archy: 1.0.0
      cross-spawn: 7.0.6
      istanbul-lib-coverage: 3.2.2
      p-map: 3.0.0
      rimraf: 3.0.2
      uuid: 8.3.2

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.4.1(supports-color@8.1.1)
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.2.0:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jackspeak@4.1.1:
    dependencies:
      '@isaacs/cliui': 8.0.2

  jake@10.9.4:
    dependencies:
      async: 3.2.6
      filelist: 1.0.4
      picocolors: 1.1.1

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.0
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-get-type@29.6.3: {}

  jimp@0.2.28:
    dependencies:
      bignumber.js: 2.4.0
      bmp-js: 0.0.3
      es6-promise: 3.3.1
      exif-parser: 0.1.12
      file-type: 3.9.0
      jpeg-js: 0.2.0
      load-bmfont: 1.4.2
      mime: 1.6.0
      mkdirp: 0.5.1
      pixelmatch: 4.0.2
      pngjs: 3.4.0
      read-chunk: 1.0.1
      request: 2.88.0
      stream-to-buffer: 0.1.0
      tinycolor2: 1.6.0
      url-regex: 3.2.0
    transitivePeerDependencies:
      - debug

  jose@4.15.9: {}

  jpeg-js@0.1.2: {}

  jpeg-js@0.2.0: {}

  js-big-integer@1.0.2: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsbn@0.1.1: {}

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-bigint@0.2.3:
    dependencies:
      bignumber.js: 4.1.0

  json-bigint@1.0.0:
    dependencies:
      bignumber.js: 9.3.1

  json-buffer@3.0.1: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-parse-even-better-errors@3.0.2: {}

  json-refs@2.1.7:
    dependencies:
      commander: 2.20.3
      graphlib: 2.1.8
      js-yaml: 3.14.1
      native-promise-only: 0.8.1
      path-loader: 1.0.12
      slash: 1.0.0
      uri-js: 3.0.2
    transitivePeerDependencies:
      - supports-color

  json-refs@3.0.12:
    dependencies:
      commander: 2.11.0
      graphlib: 2.1.8
      js-yaml: 3.14.1
      lodash: 4.17.21
      native-promise-only: 0.8.1
      path-loader: 1.0.12
      slash: 1.0.0
      uri-js: 3.0.2
    transitivePeerDependencies:
      - supports-color

  json-schema-ref-resolver@2.0.1:
    dependencies:
      dequal: 2.0.3

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-schema@0.4.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stringify-nice@1.1.4: {}

  json-stringify-safe@5.0.1: {}

  json5@2.2.3: {}

  jsonc-parser@3.2.0: {}

  jsonfile@4.0.0:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonfile@6.2.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonparse@1.3.1: {}

  jsonwebtoken@8.5.1:
    dependencies:
      jws: 3.2.2
      lodash.includes: 4.3.0
      lodash.isboolean: 3.0.3
      lodash.isinteger: 4.0.4
      lodash.isnumber: 3.0.3
      lodash.isplainobject: 4.0.6
      lodash.isstring: 4.0.1
      lodash.once: 4.1.1
      ms: 2.1.3
      semver: 5.7.2

  jsonwebtoken@9.0.2:
    dependencies:
      jws: 3.2.2
      lodash.includes: 4.3.0
      lodash.isboolean: 3.0.3
      lodash.isinteger: 4.0.4
      lodash.isnumber: 3.0.3
      lodash.isplainobject: 4.0.6
      lodash.isstring: 4.0.1
      lodash.once: 4.1.1
      ms: 2.1.3
      semver: 7.7.2

  jsprim@1.4.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  jsx-ast-utils-x@0.1.0: {}

  just-diff-apply@5.5.0: {}

  just-diff@6.0.2: {}

  just-extend@6.2.0: {}

  jwa@1.4.2:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jwa@2.0.1:
    dependencies:
      buffer-equal-constant-time: 1.0.1
      ecdsa-sig-formatter: 1.0.11
      safe-buffer: 5.2.1

  jwks-rsa@3.1.0:
    dependencies:
      '@types/express': 4.17.23
      '@types/jsonwebtoken': 9.0.10
      debug: 4.4.1(supports-color@8.1.1)
      jose: 4.15.9
      limiter: 1.1.5
      lru-memoizer: 2.3.0
    transitivePeerDependencies:
      - supports-color

  jws@3.2.2:
    dependencies:
      jwa: 1.4.2
      safe-buffer: 5.2.1

  jws@4.0.0:
    dependencies:
      jwa: 2.0.1
      safe-buffer: 5.2.1

  kafka-node@5.0.0:
    dependencies:
      optional: 0.1.4
      async: 2.6.4
      binary: 0.3.0
      bl: 2.2.1
      buffer-crc32: 0.2.13
      buffermaker: 1.2.1
      debug: 2.6.9
      denque: 1.5.1
      lodash: 4.17.21
      minimatch: 3.1.2
      nested-error-stacks: 2.1.1
      retry: 0.10.1
      uuid: 3.4.0
    optionalDependencies:
      snappy: 6.3.5
    transitivePeerDependencies:
      - supports-color

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@6.0.3: {}

  lcid@1.0.0:
    dependencies:
      invert-kv: 1.0.0

  lcid@2.0.0:
    dependencies:
      invert-kv: 2.0.0

  lerna@8.2.2(@types/node@22.14.1)(encoding@0.1.13):
    dependencies:
      '@lerna/create': 8.2.2(@types/node@22.14.1)(encoding@0.1.13)(typescript@5.6.3)
      '@npmcli/arborist': 7.5.4
      '@npmcli/package-json': 5.2.0
      '@npmcli/run-script': 8.1.0
      '@nx/devkit': 20.8.2(nx@20.8.2)
      '@octokit/plugin-enterprise-rest': 6.0.1
      '@octokit/rest': 20.1.2
      aproba: 2.0.0
      byte-size: 8.1.1
      chalk: 4.1.0
      clone-deep: 4.0.1
      cmd-shim: 6.0.3
      color-support: 1.1.3
      columnify: 1.6.0
      console-control-strings: 1.1.0
      conventional-changelog-angular: 7.0.0
      conventional-changelog-core: 5.0.1
      conventional-recommended-bump: 7.0.1
      cosmiconfig: 9.0.0(typescript@5.6.3)
      dedent: 1.5.3
      envinfo: 7.13.0
      execa: 5.0.0
      fs-extra: 11.3.1
      get-port: 5.1.1
      get-stream: 6.0.0
      git-url-parse: 14.0.0
      glob-parent: 6.0.2
      globby: 11.1.0
      graceful-fs: 4.2.11
      has-unicode: 2.0.1
      import-local: 3.1.0
      ini: 1.3.8
      init-package-json: 6.0.3
      inquirer: 8.2.7(@types/node@22.14.1)
      is-ci: 3.0.1
      is-stream: 2.0.0
      jest-diff: 29.7.0
      js-yaml: 4.1.0
      libnpmaccess: 8.0.6
      libnpmpublish: 9.0.9
      load-json-file: 6.2.0
      lodash: 4.17.21
      make-dir: 4.0.0
      minimatch: 3.0.5
      multimatch: 5.0.0
      node-fetch: 2.6.7(encoding@0.1.13)
      npm-package-arg: 11.0.2
      npm-packlist: 8.0.2
      npm-registry-fetch: 17.1.0
      nx: 20.8.2
      p-map: 4.0.0
      p-map-series: 2.1.0
      p-pipe: 3.1.0
      p-queue: 6.6.2
      p-reduce: 2.1.0
      p-waterfall: 2.1.1
      pacote: 18.0.6
      pify: 5.0.0
      read-cmd-shim: 4.0.0
      resolve-from: 5.0.0
      rimraf: 4.4.1
      semver: 7.7.2
      set-blocking: 2.0.0
      signal-exit: 3.0.7
      slash: 3.0.0
      ssri: 10.0.6
      string-width: 4.2.3
      strong-log-transformer: 2.1.0
      tar: 6.2.1
      temp-dir: 1.0.0
      typescript: 5.6.3
      upath: 2.0.1
      uuid: 10.0.0
      validate-npm-package-license: 3.0.4
      validate-npm-package-name: 5.0.1
      wide-align: 1.1.5
      write-file-atomic: 5.0.1
      write-pkg: 4.0.0
      yargs: 17.7.2
      yargs-parser: 21.1.1
    transitivePeerDependencies:
      - '@swc-node/register'
      - '@swc/core'
      - '@types/node'
      - babel-plugin-macros
      - bluebird
      - debug
      - encoding
      - supports-color

  levn@0.3.0:
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  libnpmaccess@8.0.6:
    dependencies:
      npm-package-arg: 11.0.2
      npm-registry-fetch: 17.1.0
    transitivePeerDependencies:
      - supports-color

  libnpmpublish@9.0.9:
    dependencies:
      ci-info: 4.3.0
      normalize-package-data: 6.0.2
      npm-package-arg: 11.0.2
      npm-registry-fetch: 17.1.0
      proc-log: 4.2.0
      semver: 7.7.2
      sigstore: 2.3.1
      ssri: 10.0.6
    transitivePeerDependencies:
      - supports-color

  light-my-request@3.8.0:
    dependencies:
      ajv: 6.12.6
      cookie: 0.4.2
      readable-stream: 3.6.2
      set-cookie-parser: 2.7.1

  light-my-request@6.6.0:
    dependencies:
      cookie: 1.0.2
      process-warning: 4.0.1
      set-cookie-parser: 2.7.1

  limiter@1.1.5: {}

  lines-and-columns@1.2.4: {}

  lines-and-columns@2.0.3: {}

  listenercount@1.0.1: {}

  load-bmfont@1.4.2:
    dependencies:
      buffer-equal: 0.0.1
      mime: 1.6.0
      parse-bmfont-ascii: 1.0.6
      parse-bmfont-binary: 1.0.6
      parse-bmfont-xml: 1.1.6
      phin: 3.7.1
      xhr: 2.6.0
      xtend: 4.0.2
    transitivePeerDependencies:
      - debug

  load-json-file@1.1.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 2.2.0
      pify: 2.3.0
      pinkie-promise: 2.0.1
      strip-bom: 2.0.0

  load-json-file@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0

  load-json-file@6.2.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 5.2.0
      strip-bom: 4.0.0
      type-fest: 0.6.0

  locate-path@2.0.0:
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0

  locate-path@3.0.0:
    dependencies:
      p-locate: 3.0.0
      path-exists: 3.0.0

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash-compat@3.10.2: {}

  lodash._arraypool@2.4.1: {}

  lodash._basebind@2.4.1:
    dependencies:
      lodash._basecreate: 2.4.1
      lodash._setbinddata: 2.4.1
      lodash._slice: 2.4.1
      lodash.isobject: 2.4.1

  lodash._baseclone@2.4.1:
    dependencies:
      lodash._getarray: 2.4.1
      lodash._releasearray: 2.4.1
      lodash._slice: 2.4.1
      lodash.assign: 2.4.1
      lodash.foreach: 2.4.1
      lodash.forown: 2.4.1
      lodash.isarray: 2.4.1
      lodash.isobject: 2.4.1

  lodash._basecreate@2.4.1:
    dependencies:
      lodash._isnative: 2.4.1
      lodash.isobject: 2.4.1
      lodash.noop: 2.4.1

  lodash._basecreatecallback@2.4.1:
    dependencies:
      lodash._setbinddata: 2.4.1
      lodash.bind: 2.4.1
      lodash.identity: 2.4.1
      lodash.support: 2.4.1

  lodash._basecreatewrapper@2.4.1:
    dependencies:
      lodash._basecreate: 2.4.1
      lodash._setbinddata: 2.4.1
      lodash._slice: 2.4.1
      lodash.isobject: 2.4.1

  lodash._createwrapper@2.4.1:
    dependencies:
      lodash._basebind: 2.4.1
      lodash._basecreatewrapper: 2.4.1
      lodash._slice: 2.4.1
      lodash.isfunction: 2.4.1

  lodash._getarray@2.4.1:
    dependencies:
      lodash._arraypool: 2.4.1

  lodash._isnative@2.4.1: {}

  lodash._maxpoolsize@2.4.1: {}

  lodash._objecttypes@2.4.1: {}

  lodash._releasearray@2.4.1:
    dependencies:
      lodash._arraypool: 2.4.1
      lodash._maxpoolsize: 2.4.1

  lodash._setbinddata@2.4.1:
    dependencies:
      lodash._isnative: 2.4.1
      lodash.noop: 2.4.1

  lodash._shimkeys@2.4.1:
    dependencies:
      lodash._objecttypes: 2.4.1

  lodash._slice@2.4.1: {}

  lodash.assign@2.4.1:
    dependencies:
      lodash._basecreatecallback: 2.4.1
      lodash._objecttypes: 2.4.1
      lodash.keys: 2.4.1

  lodash.bind@2.4.1:
    dependencies:
      lodash._createwrapper: 2.4.1
      lodash._slice: 2.4.1

  lodash.clonedeep@2.4.1:
    dependencies:
      lodash._baseclone: 2.4.1
      lodash._basecreatecallback: 2.4.1

  lodash.clonedeep@4.5.0: {}

  lodash.defaults@4.2.0: {}

  lodash.flattendeep@4.4.0: {}

  lodash.foreach@2.4.1:
    dependencies:
      lodash._basecreatecallback: 2.4.1
      lodash.forown: 2.4.1

  lodash.forown@2.4.1:
    dependencies:
      lodash._basecreatecallback: 2.4.1
      lodash._objecttypes: 2.4.1
      lodash.keys: 2.4.1

  lodash.get@4.4.2: {}

  lodash.identity@2.4.1: {}

  lodash.includes@4.3.0: {}

  lodash.isarguments@3.1.0: {}

  lodash.isarray@2.4.1:
    dependencies:
      lodash._isnative: 2.4.1

  lodash.isboolean@3.0.3: {}

  lodash.isequal@4.5.0: {}

  lodash.isfunction@2.4.1: {}

  lodash.isinteger@4.0.4: {}

  lodash.ismatch@4.4.0: {}

  lodash.isnumber@3.0.3: {}

  lodash.isobject@2.4.1:
    dependencies:
      lodash._objecttypes: 2.4.1

  lodash.isplainobject@4.0.6: {}

  lodash.isstring@4.0.1: {}

  lodash.keys@2.4.1:
    dependencies:
      lodash._isnative: 2.4.1
      lodash._shimkeys: 2.4.1
      lodash.isobject: 2.4.1

  lodash.merge@4.6.2: {}

  lodash.noop@2.4.1: {}

  lodash.once@4.1.1: {}

  lodash.support@2.4.1:
    dependencies:
      lodash._isnative: 2.4.1

  lodash@4.17.21: {}

  log-symbols@4.1.0:
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  long-timeout@0.1.1: {}

  long@1.1.2: {}

  loupe@2.3.7:
    dependencies:
      get-func-name: 2.0.2

  lru-cache@10.4.3: {}

  lru-cache@11.2.1: {}

  lru-cache@4.1.5:
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  lru-memoizer@2.3.0:
    dependencies:
      lodash.clonedeep: 4.5.0
      lru-cache: 6.0.0

  luxon@3.7.2: {}

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.2

  make-error@1.3.6: {}

  make-fetch-happen@13.0.1:
    dependencies:
      '@npmcli/agent': 2.2.2
      cacache: 18.0.4
      http-cache-semantics: 4.2.0
      is-lambda: 1.0.1
      minipass: 7.1.2
      minipass-fetch: 3.0.5
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.4
      proc-log: 4.2.0
      promise-retry: 2.0.1
      ssri: 10.0.6
    transitivePeerDependencies:
      - supports-color

  make-plural@7.4.0: {}

  map-age-cleaner@0.1.3:
    dependencies:
      p-defer: 1.0.0

  map-obj@1.0.1: {}

  map-obj@4.3.0: {}

  math-interval-parser@2.0.1: {}

  math-intrinsics@1.1.0: {}

  maxmind@4.3.22:
    dependencies:
      mmdb-lib: 2.1.1
      tiny-lru: 11.2.11

  media-typer@0.3.0: {}

  mem@4.3.0:
    dependencies:
      map-age-cleaner: 0.1.3
      mimic-fn: 2.1.0
      p-is-promise: 2.1.0

  meow@8.1.2:
    dependencies:
      '@types/minimist': 1.2.5
      camelcase-keys: 6.2.2
      decamelize-keys: 1.1.1
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9

  merge-descriptors@1.0.1: {}

  merge-descriptors@1.0.3: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  method-override@3.0.0:
    dependencies:
      debug: 3.1.0
      methods: 1.1.2
      parseurl: 1.3.3
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  methods@1.1.2: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  middie@4.1.0:
    dependencies:
      path-to-regexp: 4.0.5
      reusify: 1.1.0

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.3.4: {}

  mime@1.4.1: {}

  mime@1.6.0: {}

  mime@2.6.0: {}

  mime@3.0.0: {}

  mimic-fn@2.1.0: {}

  mimic-response@1.0.1:
    optional: true

  mimic-response@2.1.0: {}

  min-document@2.19.0:
    dependencies:
      dom-walk: 0.1.2

  min-indent@1.0.1: {}

  minimatch@10.0.3:
    dependencies:
      '@isaacs/brace-expansion': 5.0.0

  minimatch@3.0.5:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.2

  minimatch@8.0.4:
    dependencies:
      brace-expansion: 2.0.2

  minimatch@9.0.3:
    dependencies:
      brace-expansion: 2.0.2

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist-options@4.1.0:
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3

  minimist@0.0.8: {}

  minimist@1.2.8: {}

  minipass-collect@2.0.1:
    dependencies:
      minipass: 7.1.2

  minipass-fetch@3.0.5:
    dependencies:
      minipass: 7.1.2
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13

  minipass-flush@1.0.5:
    dependencies:
      minipass: 3.3.6

  minipass-pipeline@1.2.4:
    dependencies:
      minipass: 3.3.6

  minipass-sized@1.0.3:
    dependencies:
      minipass: 3.3.6

  minipass@2.9.0:
    dependencies:
      safe-buffer: 5.2.1
      yallist: 3.1.1

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0

  minipass@4.2.8: {}

  minipass@5.0.0: {}

  minipass@7.1.2: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0

  mkdirp@0.5.1:
    dependencies:
      minimist: 0.0.8

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4: {}

  mmdb-lib@2.1.1: {}

  mocha-typescript@1.1.12:
    dependencies:
      chalk: 1.1.3
      cross-spawn: 5.1.0
      yargs: 6.6.0

  mocha@10.7.3:
    dependencies:
      ansi-colors: 4.1.3
      browser-stdout: 1.3.1
      chokidar: 3.6.0
      debug: 4.4.1(supports-color@8.1.1)
      diff: 5.2.0
      escape-string-regexp: 4.0.0
      find-up: 5.0.0
      glob: 8.1.0
      he: 1.2.0
      js-yaml: 4.1.0
      log-symbols: 4.1.0
      minimatch: 5.1.6
      ms: 2.1.3
      serialize-javascript: 6.0.2
      strip-json-comments: 3.1.1
      supports-color: 8.1.1
      workerpool: 6.5.1
      yargs: 16.2.0
      yargs-parser: 20.2.9
      yargs-unparser: 2.0.0

  modify-values@1.0.1: {}

  moment-timezone@0.5.48:
    dependencies:
      moment: 2.30.1

  moment@2.19.3: {}

  moment@2.30.1: {}

  moo@0.5.2: {}

  ms@0.7.1: {}

  ms@2.0.0: {}

  ms@2.1.3: {}

  multer@1.4.4:
    dependencies:
      append-field: 1.0.0
      busboy: 0.2.14
      concat-stream: 1.6.2
      mkdirp: 0.5.6
      object-assign: 4.1.1
      on-finished: 2.4.1
      type-is: 1.6.18
      xtend: 4.0.2

  multimatch@5.0.0:
    dependencies:
      '@types/minimatch': 3.0.5
      array-differ: 3.0.0
      array-union: 2.1.0
      arrify: 2.0.1
      minimatch: 3.0.5

  mustache@4.2.0: {}

  mute-stream@0.0.8: {}

  mute-stream@1.0.0: {}

  nan@2.23.0:
    optional: true

  nanoid@2.1.11: {}

  napi-build-utils@1.0.2:
    optional: true

  native-promise-only@0.8.1: {}

  natural-compare@1.4.0: {}

  negotiator@0.6.3: {}

  negotiator@0.6.4: {}

  neo-async@2.6.2: {}

  nested-error-stacks@2.1.1: {}

  netmask@1.0.6: {}

  netmask@2.0.2: {}

  nice-try@1.0.5: {}

  nise@5.1.9:
    dependencies:
      '@sinonjs/commons': 3.0.1
      '@sinonjs/fake-timers': 11.3.1
      '@sinonjs/text-encoding': 0.7.3
      just-extend: 6.2.0
      path-to-regexp: 6.3.0

  node-abi@2.30.1:
    dependencies:
      semver: 5.7.2
    optional: true

  node-cache@5.1.2:
    dependencies:
      clone: 2.1.2

  node-fetch@2.6.7(encoding@0.1.13):
    dependencies:
      whatwg-url: 5.0.0
    optionalDependencies:
      encoding: 0.1.13

  node-fetch@2.7.0(encoding@0.1.13):
    dependencies:
      whatwg-url: 5.0.0
    optionalDependencies:
      encoding: 0.1.13

  node-gyp@10.3.1:
    dependencies:
      env-paths: 2.2.1
      exponential-backoff: 3.1.2
      glob: 10.4.5
      graceful-fs: 4.2.11
      make-fetch-happen: 13.0.1
      nopt: 7.2.1
      proc-log: 4.2.0
      semver: 7.7.2
      tar: 6.2.1
      which: 4.0.0
    transitivePeerDependencies:
      - supports-color

  node-machine-id@1.1.12: {}

  node-mailjet@3.3.1:
    dependencies:
      bluebird: 3.7.2
      json-bigint: 0.2.3
      qs: 6.14.0
      superagent: 3.8.3
      superagent-proxy: 1.0.3(superagent@3.8.3)
    transitivePeerDependencies:
      - supports-color

  node-preload@0.2.1:
    dependencies:
      process-on-spawn: 1.1.0

  node-releases@2.0.20: {}

  node-schedule@2.1.1:
    dependencies:
      cron-parser: 4.9.0
      long-timeout: 0.1.1
      sorted-array-functions: 1.3.0

  nodemon@2.0.22:
    dependencies:
      chokidar: 3.6.0
      debug: 3.2.7(supports-color@5.5.0)
      ignore-by-default: 1.0.1
      minimatch: 3.1.2
      pstree.remy: 1.1.8
      semver: 5.7.2
      simple-update-notifier: 1.1.0
      supports-color: 5.5.0
      touch: 3.1.1
      undefsafe: 2.0.5

  noop-logger@0.1.1:
    optional: true

  nopt@7.2.1:
    dependencies:
      abbrev: 2.0.0

  normalize-package-data@2.5.0:
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.10
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  normalize-package-data@3.0.3:
    dependencies:
      hosted-git-info: 4.1.0
      is-core-module: 2.16.1
      semver: 7.7.2
      validate-npm-package-license: 3.0.4

  normalize-package-data@6.0.2:
    dependencies:
      hosted-git-info: 7.0.2
      semver: 7.7.2
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  notp@2.0.3: {}

  npm-bundled@3.0.1:
    dependencies:
      npm-normalize-package-bin: 3.0.1

  npm-install-checks@6.3.0:
    dependencies:
      semver: 7.7.2

  npm-normalize-package-bin@3.0.1: {}

  npm-package-arg@11.0.2:
    dependencies:
      hosted-git-info: 7.0.2
      proc-log: 4.2.0
      semver: 7.7.2
      validate-npm-package-name: 5.0.1

  npm-packlist@8.0.2:
    dependencies:
      ignore-walk: 6.0.5

  npm-pick-manifest@9.1.0:
    dependencies:
      npm-install-checks: 6.3.0
      npm-normalize-package-bin: 3.0.1
      npm-package-arg: 11.0.2
      semver: 7.7.2

  npm-registry-fetch@17.1.0:
    dependencies:
      '@npmcli/redact': 2.0.1
      jsonparse: 1.3.1
      make-fetch-happen: 13.0.1
      minipass: 7.1.2
      minipass-fetch: 3.0.5
      minizlib: 2.1.2
      npm-package-arg: 11.0.2
      proc-log: 4.2.0
    transitivePeerDependencies:
      - supports-color

  npm-run-path@2.0.2:
    dependencies:
      path-key: 2.0.1

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npmlog@4.1.2:
    dependencies:
      are-we-there-yet: 1.1.7
      console-control-strings: 1.1.0
      gauge: 2.7.4
      set-blocking: 2.0.0
    optional: true

  nuid@1.1.6: {}

  number-is-nan@1.0.1: {}

  nx@20.8.2:
    dependencies:
      '@napi-rs/wasm-runtime': 0.2.4
      '@yarnpkg/lockfile': 1.1.0
      '@yarnpkg/parsers': 3.0.2
      '@zkochan/js-yaml': 0.0.7
      axios: 1.11.0
      chalk: 4.1.0
      cli-cursor: 3.1.0
      cli-spinners: 2.6.1
      cliui: 8.0.1
      dotenv: 16.4.7
      dotenv-expand: 11.0.7
      enquirer: 2.3.6
      figures: 3.2.0
      flat: 5.0.2
      front-matter: 4.0.2
      ignore: 5.3.2
      jest-diff: 29.7.0
      jsonc-parser: 3.2.0
      lines-and-columns: 2.0.3
      minimatch: 9.0.3
      node-machine-id: 1.1.12
      npm-run-path: 4.0.1
      open: 8.4.2
      ora: 5.3.0
      resolve.exports: 2.0.3
      semver: 7.7.2
      string-width: 4.2.3
      tar-stream: 2.2.0
      tmp: 0.2.5
      tsconfig-paths: 4.2.0
      tslib: 2.8.1
      yaml: 2.8.1
      yargs: 17.7.2
      yargs-parser: 21.1.1
    optionalDependencies:
      '@nx/nx-darwin-arm64': 20.8.2
      '@nx/nx-darwin-x64': 20.8.2
      '@nx/nx-freebsd-x64': 20.8.2
      '@nx/nx-linux-arm-gnueabihf': 20.8.2
      '@nx/nx-linux-arm64-gnu': 20.8.2
      '@nx/nx-linux-arm64-musl': 20.8.2
      '@nx/nx-linux-x64-gnu': 20.8.2
      '@nx/nx-linux-x64-musl': 20.8.2
      '@nx/nx-win32-arm64-msvc': 20.8.2
      '@nx/nx-win32-x64-msvc': 20.8.2
    transitivePeerDependencies:
      - debug

  nyc@15.1.0:
    dependencies:
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      caching-transform: 4.0.0
      convert-source-map: 1.9.0
      decamelize: 1.2.0
      find-cache-dir: 3.3.2
      find-up: 4.1.0
      foreground-child: 2.0.0
      get-package-type: 0.1.0
      glob: 7.2.3
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-hook: 3.0.0
      istanbul-lib-instrument: 4.0.3
      istanbul-lib-processinfo: 2.0.3
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.2.0
      make-dir: 3.1.0
      node-preload: 0.2.1
      p-map: 3.0.0
      process-on-spawn: 1.1.0
      resolve-from: 5.0.0
      rimraf: 3.0.2
      signal-exit: 3.0.7
      spawn-wrap: 2.0.0
      test-exclude: 6.0.0
      yargs: 15.4.1
    transitivePeerDependencies:
      - supports-color

  oauth-sign@0.9.0: {}

  object-assign@4.1.1: {}

  object-component@0.0.3: {}

  object-inspect@1.13.4: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  obuf@1.1.2: {}

  on-exit-leak-free@2.1.2: {}

  on-finished@2.2.1:
    dependencies:
      ee-first: 1.1.0

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  optional@0.1.4: {}

  optionator@0.8.3:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  ora@5.3.0:
    dependencies:
      bl: 4.1.0
      chalk: 4.1.0
      cli-cursor: 3.1.0
      cli-spinners: 2.6.1
      is-interactive: 1.0.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  ora@5.4.1:
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  os-homedir@1.0.2:
    optional: true

  os-locale@1.4.0:
    dependencies:
      lcid: 1.0.0

  os-locale@3.1.0:
    dependencies:
      execa: 1.0.0
      lcid: 2.0.0
      mem: 4.3.0

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-defer@1.0.0: {}

  p-finally@1.0.0: {}

  p-is-promise@2.1.0: {}

  p-limit@1.3.0:
    dependencies:
      p-try: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@2.0.0:
    dependencies:
      p-limit: 1.3.0

  p-locate@3.0.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-map-series@2.1.0: {}

  p-map@3.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-pipe@3.1.0: {}

  p-queue@6.6.2:
    dependencies:
      eventemitter3: 4.0.7
      p-timeout: 3.2.0

  p-reduce@2.1.0: {}

  p-timeout@3.2.0:
    dependencies:
      p-finally: 1.0.0

  p-try@1.0.0: {}

  p-try@2.2.0: {}

  p-waterfall@2.1.1:
    dependencies:
      p-reduce: 2.1.0

  pac-proxy-agent@2.0.2:
    dependencies:
      agent-base: 4.3.0
      debug: 3.2.7(supports-color@5.5.0)
      get-uri: 2.0.4
      http-proxy-agent: 2.1.0
      https-proxy-agent: 2.2.4
      pac-resolver: 3.0.0
      raw-body: 2.5.2
      socks-proxy-agent: 3.0.1
    transitivePeerDependencies:
      - supports-color

  pac-proxy-agent@5.0.0:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.4.1(supports-color@8.1.1)
      get-uri: 3.0.2
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      pac-resolver: 5.0.1
      raw-body: 2.5.2
      socks-proxy-agent: 5.0.1
    transitivePeerDependencies:
      - supports-color

  pac-resolver@3.0.0:
    dependencies:
      co: 4.6.0
      degenerator: 1.0.4
      ip: 1.1.9
      netmask: 1.0.6
      thunkify: 2.1.2

  pac-resolver@5.0.1:
    dependencies:
      degenerator: 3.0.4
      ip: 1.1.9
      netmask: 2.0.2

  package-hash@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      hasha: 5.2.2
      lodash.flattendeep: 4.4.0
      release-zalgo: 1.0.0

  package-json-from-dist@1.0.1: {}

  pacote@18.0.6:
    dependencies:
      '@npmcli/git': 5.0.8
      '@npmcli/installed-package-contents': 2.1.0
      '@npmcli/package-json': 5.2.0
      '@npmcli/promise-spawn': 7.0.2
      '@npmcli/run-script': 8.1.0
      cacache: 18.0.4
      fs-minipass: 3.0.3
      minipass: 7.1.2
      npm-package-arg: 11.0.2
      npm-packlist: 8.0.2
      npm-pick-manifest: 9.1.0
      npm-registry-fetch: 17.1.0
      proc-log: 4.2.0
      promise-retry: 2.0.1
      sigstore: 2.3.1
      ssri: 10.0.6
      tar: 6.2.1
    transitivePeerDependencies:
      - bluebird
      - supports-color

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-bmfont-ascii@1.0.6: {}

  parse-bmfont-binary@1.0.6: {}

  parse-bmfont-xml@1.1.6:
    dependencies:
      xml-parse-from-string: 1.0.1
      xml2js: 0.5.0

  parse-conflict-json@3.0.1:
    dependencies:
      json-parse-even-better-errors: 3.0.2
      just-diff: 6.0.2
      just-diff-apply: 5.5.0

  parse-headers@2.0.6: {}

  parse-json@2.2.0:
    dependencies:
      error-ex: 1.3.2

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-json@8.3.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      index-to-position: 1.1.0
      type-fest: 4.41.0

  parse-path@7.1.0:
    dependencies:
      protocols: 2.0.2

  parse-png@1.1.2:
    dependencies:
      pngjs: 3.4.0

  parse-url@8.1.0:
    dependencies:
      parse-path: 7.1.0

  parseqs@0.0.5:
    dependencies:
      better-assert: 1.0.2

  parseuri@0.0.5:
    dependencies:
      better-assert: 1.0.2

  parseurl@1.3.3: {}

  path-exists@2.1.0:
    dependencies:
      pinkie-promise: 2.0.1

  path-exists@3.0.0: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@2.0.1: {}

  path-key@3.1.1: {}

  path-loader@1.0.12:
    dependencies:
      native-promise-only: 0.8.1
      superagent: 7.1.6
    transitivePeerDependencies:
      - supports-color

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-scurry@2.0.0:
    dependencies:
      lru-cache: 11.2.1
      minipass: 7.1.2

  path-to-regexp@0.1.10: {}

  path-to-regexp@0.1.7: {}

  path-to-regexp@1.9.0:
    dependencies:
      isarray: 0.0.1

  path-to-regexp@4.0.5: {}

  path-to-regexp@6.3.0: {}

  path-to-regexp@8.3.0: {}

  path-type@1.1.0:
    dependencies:
      graceful-fs: 4.2.11
      pify: 2.3.0
      pinkie-promise: 2.0.1

  path-type@3.0.0:
    dependencies:
      pify: 3.0.0

  path-type@4.0.0: {}

  pathval@1.1.1: {}

  peek-stream@1.1.3:
    dependencies:
      buffer-from: 1.1.2
      duplexify: 3.7.1
      through2: 2.0.5

  performance-now@2.1.0: {}

  pg-cloudflare@1.2.7:
    optional: true

  pg-connection-string@2.9.1: {}

  pg-int8@1.0.1: {}

  pg-numeric@1.0.2: {}

  pg-pool@3.10.1(pg@8.14.1):
    dependencies:
      pg: 8.14.1

  pg-protocol@1.10.3: {}

  pg-types@2.2.0:
    dependencies:
      pg-int8: 1.0.1
      postgres-array: 2.0.0
      postgres-bytea: 1.0.0
      postgres-date: 1.0.7
      postgres-interval: 1.2.0

  pg-types@4.1.0:
    dependencies:
      pg-int8: 1.0.1
      pg-numeric: 1.0.2
      postgres-array: 3.0.4
      postgres-bytea: 3.0.0
      postgres-date: 2.1.0
      postgres-interval: 3.0.0
      postgres-range: 1.1.4

  pg@8.14.1:
    dependencies:
      pg-connection-string: 2.9.1
      pg-pool: 3.10.1(pg@8.14.1)
      pg-protocol: 1.10.3
      pg-types: 2.2.0
      pgpass: 1.0.5
    optionalDependencies:
      pg-cloudflare: 1.2.7

  pgpass@1.0.5:
    dependencies:
      split2: 4.2.0

  phin@3.7.1:
    dependencies:
      centra: 2.7.0
    transitivePeerDependencies:
      - debug

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pify@2.3.0: {}

  pify@3.0.0: {}

  pify@4.0.1: {}

  pify@5.0.0: {}

  pinkie-promise@2.0.1:
    dependencies:
      pinkie: 2.0.4

  pinkie@2.0.4: {}

  pino-abstract-transport@2.0.0:
    dependencies:
      split2: 4.2.0

  pino-std-serializers@2.5.0: {}

  pino-std-serializers@7.0.0: {}

  pino@5.17.0:
    dependencies:
      fast-redact: 2.1.0
      fast-safe-stringify: 2.1.1
      flatstr: 1.0.12
      pino-std-serializers: 2.5.0
      quick-format-unescaped: 3.0.3
      sonic-boom: 0.7.7

  pino@9.9.5:
    dependencies:
      atomic-sleep: 1.0.0
      fast-redact: 3.5.0
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 2.0.0
      pino-std-serializers: 7.0.0
      process-warning: 5.0.0
      quick-format-unescaped: 4.0.4
      real-require: 0.2.0
      safe-stable-stringify: 2.5.0
      sonic-boom: 4.2.0
      thread-stream: 3.1.0

  pixelmatch@4.0.2:
    dependencies:
      pngjs: 3.4.0

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  pluralize@8.0.0: {}

  pngjs@3.4.0: {}

  pop-iterate@1.0.1: {}

  possible-typed-array-names@1.1.0: {}

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postgres-array@2.0.0: {}

  postgres-array@3.0.4: {}

  postgres-bytea@1.0.0: {}

  postgres-bytea@3.0.0:
    dependencies:
      obuf: 1.1.2

  postgres-date@1.0.7: {}

  postgres-date@2.1.0: {}

  postgres-interval@1.2.0:
    dependencies:
      xtend: 4.0.2

  postgres-interval@3.0.0: {}

  postgres-range@1.1.4: {}

  prebuild-install@5.3.0:
    dependencies:
      detect-libc: 1.0.3
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.8
      mkdirp: 0.5.6
      napi-build-utils: 1.0.2
      node-abi: 2.30.1
      noop-logger: 0.1.1
      npmlog: 4.1.2
      os-homedir: 1.0.2
      pump: 2.0.1
      rc: 1.2.8
      simple-get: 2.8.2
      tar-fs: 1.16.5
      tunnel-agent: 0.6.0
      which-pm-runs: 1.1.0
    optional: true

  prelude-ls@1.1.2: {}

  prelude-ls@1.2.1: {}

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  proc-log@4.2.0: {}

  process-nextick-args@2.0.1: {}

  process-on-spawn@1.1.0:
    dependencies:
      fromentries: 1.3.2

  process-warning@4.0.1: {}

  process-warning@5.0.0: {}

  process@0.11.10: {}

  proggy@2.0.0: {}

  prom-client@14.2.0:
    dependencies:
      tdigest: 0.1.2

  prom-client@15.0.0:
    dependencies:
      '@opentelemetry/api': 1.9.0
      tdigest: 0.1.2

  promise-all-reject-late@1.0.1: {}

  promise-call-limit@3.0.2: {}

  promise-inflight@1.0.1: {}

  promise-retry@2.0.1:
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0

  promzard@1.0.2:
    dependencies:
      read: 3.0.1

  properties@1.2.1: {}

  protocols@2.0.2: {}

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-agent@2.3.1:
    dependencies:
      agent-base: 4.3.0
      debug: 3.2.7(supports-color@5.5.0)
      http-proxy-agent: 2.1.0
      https-proxy-agent: 2.2.4
      lru-cache: 4.1.5
      pac-proxy-agent: 2.0.2
      proxy-from-env: 1.1.0
      socks-proxy-agent: 3.0.1
    transitivePeerDependencies:
      - supports-color

  proxy-agent@5.0.0:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1(supports-color@8.1.1)
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      lru-cache: 5.1.1
      pac-proxy-agent: 5.0.0
      proxy-from-env: 1.1.0
      socks-proxy-agent: 5.0.1
    transitivePeerDependencies:
      - supports-color

  proxy-from-env@1.1.0: {}

  pseudomap@1.0.2: {}

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  pstree.remy@1.1.8: {}

  pump@1.0.3:
    dependencies:
      end-of-stream: 1.4.5
      once: 1.4.0
    optional: true

  pump@2.0.1:
    dependencies:
      end-of-stream: 1.4.5
      once: 1.4.0
    optional: true

  pump@3.0.3:
    dependencies:
      end-of-stream: 1.4.5
      once: 1.4.0

  pumpify@2.0.1:
    dependencies:
      duplexify: 4.1.3
      inherits: 2.0.4
      pump: 3.0.3

  punycode@1.4.1: {}

  punycode@2.3.1: {}

  q@2.0.3:
    dependencies:
      asap: 2.0.6
      pop-iterate: 1.0.1
      weak-map: 1.0.8

  qrcode-terminal@0.12.0: {}

  qrcode@1.3.3:
    dependencies:
      can-promise: 0.0.1
      dijkstrajs: 1.0.3
      isarray: 2.0.5
      pngjs: 3.4.0
      yargs: 12.0.5

  qs@2.3.3: {}

  qs@2.4.2: {}

  qs@4.0.0: {}

  qs@6.13.0:
    dependencies:
      side-channel: 1.1.0

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  qs@6.5.1: {}

  qs@6.5.3: {}

  queue-microtask@1.2.3: {}

  quick-format-unescaped@3.0.3: {}

  quick-format-unescaped@4.0.4: {}

  quick-lru@4.0.1: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.0.2:
    dependencies:
      bytes: 2.1.0
      iconv-lite: 0.4.8

  raw-body@2.3.2:
    dependencies:
      bytes: 3.0.0
      http-errors: 1.6.2
      iconv-lite: 0.4.19
      unpipe: 1.0.0

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1
    optional: true

  react-is@18.3.1: {}

  read-chunk@1.0.1: {}

  read-cmd-shim@4.0.0: {}

  read-package-json-fast@3.0.2:
    dependencies:
      json-parse-even-better-errors: 3.0.2
      npm-normalize-package-bin: 3.0.1

  read-package-up@11.0.0:
    dependencies:
      find-up-simple: 1.0.1
      read-pkg: 9.0.1
      type-fest: 4.41.0

  read-pkg-up@1.0.1:
    dependencies:
      find-up: 1.1.2
      read-pkg: 1.1.0

  read-pkg-up@3.0.0:
    dependencies:
      find-up: 2.1.0
      read-pkg: 3.0.0

  read-pkg-up@7.0.1:
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1

  read-pkg@1.1.0:
    dependencies:
      load-json-file: 1.1.0
      normalize-package-data: 2.5.0
      path-type: 1.1.0

  read-pkg@3.0.0:
    dependencies:
      load-json-file: 4.0.0
      normalize-package-data: 2.5.0
      path-type: 3.0.0

  read-pkg@5.2.0:
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0

  read-pkg@9.0.1:
    dependencies:
      '@types/normalize-package-data': 2.4.4
      normalize-package-data: 6.0.2
      parse-json: 8.3.0
      type-fest: 4.41.0
      unicorn-magic: 0.1.0

  read@3.0.1:
    dependencies:
      mute-stream: 1.0.0

  readable-stream@1.0.27-1:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31

  readable-stream@1.1.14:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readable-stream@4.7.0:
    dependencies:
      abort-controller: 3.0.0
      buffer: 6.0.3
      events: 3.3.0
      process: 0.11.10
      string_decoder: 1.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  real-require@0.2.0: {}

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  redis-errors@1.2.0: {}

  redis-parser@3.0.0:
    dependencies:
      redis-errors: 1.2.0

  reduce-component@1.0.1: {}

  refa@0.12.1:
    dependencies:
      '@eslint-community/regexpp': 4.12.1

  reflect-metadata@0.1.13: {}

  reflect-metadata@0.2.2: {}

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerator-runtime@0.11.1: {}

  regexp-ast-analysis@0.7.1:
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      refa: 0.12.1

  regexp-tree@0.1.27: {}

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  release-zalgo@1.0.0:
    dependencies:
      es6-error: 4.1.1

  request@2.88.0:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.4.3
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  request@2.88.2:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-main-filename@1.0.1: {}

  require-main-filename@2.0.0: {}

  resize-img@1.1.2:
    dependencies:
      bmp-js: 0.0.1
      file-type: 3.9.0
      get-stream: 2.3.1
      jimp: 0.2.28
      jpeg-js: 0.1.2
      parse-png: 1.1.2
    transitivePeerDependencies:
      - debug

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve.exports@2.0.3: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  ret@0.2.2: {}

  ret@0.5.0: {}

  retry-as-promised@7.1.1: {}

  retry-request@7.0.2(encoding@0.1.13):
    dependencies:
      '@types/request': 2.48.12
      extend: 3.0.2
      teeny-request: 9.0.0(encoding@0.1.13)
    transitivePeerDependencies:
      - encoding
      - supports-color

  retry@0.10.1: {}

  retry@0.12.0: {}

  retry@0.13.1: {}

  reusify@1.1.0: {}

  rfdc@1.4.1: {}

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rimraf@4.4.1:
    dependencies:
      glob: 9.3.5

  rootpath@0.1.2: {}

  run-async@2.4.1: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.1.1: {}

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-identifier@0.4.2: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safe-regex2@2.0.0:
    dependencies:
      ret: 0.2.2

  safe-regex2@5.0.0:
    dependencies:
      ret: 0.5.0

  safe-stable-stringify@2.5.0: {}

  safer-buffer@2.1.2: {}

  sax@1.4.1: {}

  scmp@2.0.0: {}

  scslre@0.3.0:
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      refa: 0.12.1
      regexp-ast-analysis: 0.7.1

  secure-json-parse@2.7.0: {}

  secure-json-parse@4.0.0: {}

  semver-store@0.3.0: {}

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.0.0: {}

  semver@7.7.2: {}

  send@0.16.1:
    dependencies:
      debug: 2.6.9
      depd: 1.1.2
      destroy: 1.0.4
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 1.6.3
      mime: 1.4.1
      ms: 2.0.0
      on-finished: 2.3.0
      range-parser: 1.2.1
      statuses: 1.3.1
    transitivePeerDependencies:
      - supports-color

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  sequelize-pool@7.1.0: {}

  sequelize@6.37.4(pg@8.14.1):
    dependencies:
      '@types/debug': 4.1.12
      '@types/validator': 13.15.3
      debug: 4.4.1(supports-color@8.1.1)
      dottie: 2.0.6
      inflection: 1.13.4
      lodash: 4.17.21
      moment: 2.30.1
      moment-timezone: 0.5.48
      pg-connection-string: 2.9.1
      retry-as-promised: 7.1.1
      semver: 7.7.2
      sequelize-pool: 7.1.0
      toposort-class: 1.0.1
      uuid: 8.3.2
      validator: 13.15.15
      wkx: 0.5.0
    optionalDependencies:
      pg: 8.14.1
    transitivePeerDependencies:
      - supports-color

  sequelize@6.37.7(pg@8.14.1):
    dependencies:
      '@types/debug': 4.1.12
      '@types/validator': 13.15.3
      debug: 4.4.1(supports-color@8.1.1)
      dottie: 2.0.6
      inflection: 1.13.4
      lodash: 4.17.21
      moment: 2.30.1
      moment-timezone: 0.5.48
      pg-connection-string: 2.9.1
      retry-as-promised: 7.1.1
      semver: 7.7.2
      sequelize-pool: 7.1.0
      toposort-class: 1.0.1
      uuid: 8.3.2
      validator: 13.15.15
      wkx: 0.5.0
    optionalDependencies:
      pg: 8.14.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  serve-static@1.13.1:
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.16.1
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  set-blocking@2.0.0: {}

  set-cookie-parser@2.7.1: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  setimmediate@1.0.5: {}

  setprototypeof@1.0.3: {}

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  shallow-clone@3.0.1:
    dependencies:
      kind-of: 6.0.3

  shebang-command@1.2.0:
    dependencies:
      shebang-regex: 1.0.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@1.0.0: {}

  shebang-regex@3.0.0: {}

  shimmer@1.2.1: {}

  shortid@2.2.16:
    dependencies:
      nanoid: 2.1.11

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  sigstore@2.3.1:
    dependencies:
      '@sigstore/bundle': 2.3.2
      '@sigstore/core': 1.1.0
      '@sigstore/protobuf-specs': 0.3.3
      '@sigstore/sign': 2.3.2
      '@sigstore/tuf': 2.3.4
      '@sigstore/verify': 1.2.1
    transitivePeerDependencies:
      - supports-color

  simple-concat@1.0.1:
    optional: true

  simple-get@2.8.2:
    dependencies:
      decompress-response: 3.3.0
      once: 1.4.0
      simple-concat: 1.0.1
    optional: true

  simple-lru-cache@0.0.2: {}

  simple-update-notifier@1.1.0:
    dependencies:
      semver: 7.0.0

  sinon-chai@3.7.0(chai@4.3.10)(sinon@16.1.3):
    dependencies:
      chai: 4.3.10
      sinon: 16.1.3

  sinon@16.1.3:
    dependencies:
      '@sinonjs/commons': 3.0.1
      '@sinonjs/fake-timers': 10.3.0
      '@sinonjs/samsam': 8.0.3
      diff: 5.2.0
      nise: 5.1.9
      supports-color: 7.2.0

  slash@1.0.0: {}

  slash@3.0.0: {}

  smart-buffer@1.1.15: {}

  smart-buffer@4.2.0: {}

  snappy@6.3.5:
    dependencies:
      bindings: 1.5.0
      nan: 2.23.0
      prebuild-install: 5.3.0
    optional: true

  socket.io-adapter@1.1.2: {}

  socket.io-adapter@2.5.5:
    dependencies:
      debug: 4.3.7
      ws: 8.17.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socket.io-client@2.1.1:
    dependencies:
      backo2: 1.0.2
      base64-arraybuffer: 0.1.5
      component-bind: 1.0.0
      component-emitter: 1.2.1
      debug: 3.1.0
      engine.io-client: 3.2.1
      has-binary2: 1.0.3
      has-cors: 1.1.0
      indexof: 0.0.1
      object-component: 0.0.3
      parseqs: 0.0.5
      parseuri: 0.0.5
      socket.io-parser: 3.2.0
      to-array: 0.1.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socket.io-parser@3.2.0:
    dependencies:
      component-emitter: 1.2.1
      debug: 3.1.0
      isarray: 2.0.1
    transitivePeerDependencies:
      - supports-color

  socket.io-parser@4.2.4:
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.7
    transitivePeerDependencies:
      - supports-color

  socket.io@2.1.1:
    dependencies:
      debug: 3.1.0
      engine.io: 3.2.1
      has-binary2: 1.0.3
      socket.io-adapter: 1.1.2
      socket.io-client: 2.1.1
      socket.io-parser: 3.2.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socket.io@4.8.1:
    dependencies:
      accepts: 1.3.8
      base64id: 2.0.0
      cors: 2.8.5
      debug: 4.3.7
      engine.io: 6.6.4
      socket.io-adapter: 2.5.5
      socket.io-parser: 4.2.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socks-proxy-agent@3.0.1:
    dependencies:
      agent-base: 4.3.0
      socks: 1.1.10

  socks-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.1(supports-color@8.1.1)
      socks: 2.8.7
    transitivePeerDependencies:
      - supports-color

  socks-proxy-agent@8.0.5:
    dependencies:
      agent-base: 7.1.4
      debug: 4.4.1(supports-color@8.1.1)
      socks: 2.8.7
    transitivePeerDependencies:
      - supports-color

  socks@1.1.10:
    dependencies:
      ip: 1.1.9
      smart-buffer: 1.1.15

  socks@2.8.7:
    dependencies:
      ip-address: 10.0.1
      smart-buffer: 4.2.0

  sonic-boom@0.7.7:
    dependencies:
      atomic-sleep: 1.0.0
      flatstr: 1.0.12

  sonic-boom@4.2.0:
    dependencies:
      atomic-sleep: 1.0.0

  sort-keys@2.0.0:
    dependencies:
      is-plain-obj: 1.1.0

  sorted-array-functions@1.3.0: {}

  source-map@0.6.1: {}

  spark-md5@1.0.1: {}

  spawn-wrap@2.0.0:
    dependencies:
      foreground-child: 2.0.0
      is-windows: 1.0.2
      make-dir: 3.1.0
      rimraf: 3.0.2
      signal-exit: 3.0.7
      which: 2.0.2

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.22

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.22

  spdx-license-ids@3.0.22: {}

  split2@3.2.2:
    dependencies:
      readable-stream: 3.6.2

  split2@4.2.0: {}

  split@1.0.1:
    dependencies:
      through: 2.3.8

  sprintf-js@1.0.3: {}

  sshpk@1.18.0:
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  ssri@10.0.6:
    dependencies:
      minipass: 7.1.2

  stack-chain@1.3.7: {}

  standard-as-callback@2.1.0: {}

  statuses@1.3.1: {}

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  stop-iteration-iterator@1.1.0:
    dependencies:
      es-errors: 1.3.0
      internal-slot: 1.1.0

  stream-events@1.0.5:
    dependencies:
      stubs: 3.0.0

  stream-shift@1.0.3: {}

  stream-to-buffer@0.1.0:
    dependencies:
      stream-to: 0.2.2

  stream-to@0.2.2: {}

  streamsearch@0.1.2: {}

  string-similarity@4.0.4: {}

  string-to-stream@3.0.1:
    dependencies:
      readable-stream: 3.6.2

  string-width@1.0.2:
    dependencies:
      code-point-at: 1.1.0
      is-fullwidth-code-point: 1.0.0
      strip-ansi: 3.0.1

  string-width@2.1.1:
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.2

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string@3.3.3: {}

  string_decoder@0.10.31: {}

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@3.0.1:
    dependencies:
      ansi-regex: 2.1.1

  strip-ansi@4.0.0:
    dependencies:
      ansi-regex: 3.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.2:
    dependencies:
      ansi-regex: 6.2.2

  strip-bom@2.0.0:
    dependencies:
      is-utf8: 0.2.1

  strip-bom@3.0.0: {}

  strip-bom@4.0.0: {}

  strip-eof@1.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-indent@4.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@2.0.1:
    optional: true

  strip-json-comments@3.1.1: {}

  strnum@1.1.2: {}

  strong-log-transformer@2.1.0:
    dependencies:
      duplexer: 0.1.2
      minimist: 1.2.8
      through: 2.3.8

  stubs@3.0.0: {}

  superagent-proxy@1.0.3(superagent@3.8.3):
    dependencies:
      debug: 3.2.7(supports-color@5.5.0)
      proxy-agent: 2.3.1
      superagent: 3.8.3
    transitivePeerDependencies:
      - supports-color

  superagent-proxy@3.0.0(superagent@10.2.3):
    dependencies:
      debug: 4.4.1(supports-color@8.1.1)
      proxy-agent: 5.0.0
      superagent: 10.2.3
    transitivePeerDependencies:
      - supports-color

  superagent@1.8.5:
    dependencies:
      component-emitter: 1.2.1
      cookiejar: 2.0.6
      debug: 2.6.9
      extend: 3.0.0
      form-data: 1.0.0-rc3
      formidable: 1.0.17
      methods: 1.1.2
      mime: 1.3.4
      qs: 2.3.3
      readable-stream: 1.0.27-1
      reduce-component: 1.0.1
    transitivePeerDependencies:
      - supports-color

  superagent@10.2.3:
    dependencies:
      component-emitter: 1.3.1
      cookiejar: 2.1.4
      debug: 4.4.1(supports-color@8.1.1)
      fast-safe-stringify: 2.1.1
      form-data: 4.0.4
      formidable: 3.5.4
      methods: 1.1.2
      mime: 2.6.0
      qs: 6.14.0
    transitivePeerDependencies:
      - supports-color

  superagent@3.8.3:
    dependencies:
      component-emitter: 1.3.1
      cookiejar: 2.1.4
      debug: 3.2.7(supports-color@5.5.0)
      extend: 3.0.2
      form-data: 2.5.5
      formidable: 1.2.6
      methods: 1.1.2
      mime: 1.6.0
      qs: 6.14.0
      readable-stream: 2.3.8
    transitivePeerDependencies:
      - supports-color

  superagent@7.1.6:
    dependencies:
      component-emitter: 1.3.1
      cookiejar: 2.1.4
      debug: 4.4.1(supports-color@8.1.1)
      fast-safe-stringify: 2.1.1
      form-data: 4.0.4
      formidable: 2.1.5
      methods: 1.1.2
      mime: 2.6.0
      qs: 6.14.0
      readable-stream: 3.6.2
      semver: 7.7.2
    transitivePeerDependencies:
      - supports-color

  superagent@9.0.2:
    dependencies:
      component-emitter: 1.3.1
      cookiejar: 2.1.4
      debug: 4.4.1(supports-color@8.1.1)
      fast-safe-stringify: 2.1.1
      form-data: 4.0.4
      formidable: 3.5.4
      methods: 1.1.2
      mime: 2.6.0
      qs: 6.14.0
    transitivePeerDependencies:
      - supports-color

  supertest@7.0.0:
    dependencies:
      methods: 1.1.2
      superagent: 9.0.2
    transitivePeerDependencies:
      - supports-color

  supports-color@2.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  swagger-converter@0.1.7:
    dependencies:
      lodash.clonedeep: 2.4.1

  swagger-tools@0.10.1:
    dependencies:
      async: 1.5.2
      body-parser: 1.12.4
      commander: 2.20.3
      debug: 2.6.9
      js-yaml: 3.14.1
      json-refs: 2.1.7
      lodash-compat: 3.10.2
      multer: 1.4.4
      parseurl: 1.3.3
      path-to-regexp: 1.9.0
      qs: 4.0.0
      serve-static: 1.16.2
      spark-md5: 1.0.1
      string: 3.3.3
      superagent: 1.8.5
      swagger-converter: 0.1.7
      traverse: 0.6.11
      z-schema: 3.25.1
    transitivePeerDependencies:
      - supports-color

  tar-fs@1.16.5:
    dependencies:
      chownr: 1.1.4
      mkdirp: 0.5.6
      pump: 1.0.3
      tar-stream: 1.6.2
    optional: true

  tar-stream@1.6.2:
    dependencies:
      bl: 1.2.3
      buffer-alloc: 1.2.0
      end-of-stream: 1.4.5
      fs-constants: 1.0.0
      readable-stream: 2.3.8
      to-buffer: 1.2.1
      xtend: 4.0.2
    optional: true

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.5
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0

  tdigest@0.1.2:
    dependencies:
      bintrees: 1.0.2

  teeny-request@9.0.0(encoding@0.1.13):
    dependencies:
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      node-fetch: 2.7.0(encoding@0.1.13)
      stream-events: 1.0.5
      uuid: 9.0.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  temp-dir@1.0.0: {}

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-extensions@1.9.0: {}

  thirty-two@0.0.2: {}

  thread-stream@3.1.0:
    dependencies:
      real-require: 0.2.0

  through2@2.0.5:
    dependencies:
      readable-stream: 2.3.8
      xtend: 4.0.2

  through@2.3.8: {}

  thunkify@2.1.2: {}

  tiny-lru@11.2.11: {}

  tiny-lru@7.0.6: {}

  tinycolor2@1.6.0: {}

  tmp@0.2.5: {}

  to-array@0.1.4: {}

  to-buffer@1.2.1:
    dependencies:
      isarray: 2.0.5
      safe-buffer: 5.2.1
      typed-array-buffer: 1.0.3
    optional: true

  to-ico@1.1.5:
    dependencies:
      arrify: 1.0.1
      buffer-alloc: 1.2.0
      image-size: 0.5.5
      parse-png: 1.1.2
      resize-img: 1.1.2
    transitivePeerDependencies:
      - debug

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toad-cache@3.7.0: {}

  toidentifier@1.0.1: {}

  toposort-class@1.0.1: {}

  touch@3.1.1: {}

  tough-cookie@2.4.3:
    dependencies:
      psl: 1.15.0
      punycode: 1.4.1

  tough-cookie@2.5.0:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1

  tr46@0.0.3: {}

  traverse@0.3.9: {}

  traverse@0.6.11:
    dependencies:
      gopd: 1.2.0
      typedarray.prototype.slice: 1.0.5
      which-typed-array: 1.1.19

  treeverse@3.0.0: {}

  trek-captcha@0.4.0: {}

  trim-newlines@3.0.1: {}

  ts-api-utils@2.1.0(typescript@5.6.3):
    dependencies:
      typescript: 5.6.3

  ts-nats@1.2.4:
    dependencies:
      nuid: 1.1.6
      ts-nkeys: 1.0.16

  ts-nkeys@1.0.16:
    dependencies:
      tweetnacl: 1.0.3

  ts-node@10.9.2(@types/node@22.14.1)(typescript@5.6.3):
    dependencies:
      '@cspotcode/source-map-support': 0.8.1
      '@tsconfig/node10': 1.0.11
      '@tsconfig/node12': 1.0.11
      '@tsconfig/node14': 1.0.3
      '@tsconfig/node16': 1.0.4
      '@types/node': 22.14.1
      acorn: 8.15.0
      acorn-walk: 8.3.4
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.6.3
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tsconfig-paths@4.2.0:
    dependencies:
      json5: 2.2.3
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@2.8.1: {}

  tuf-js@2.2.1:
    dependencies:
      '@tufjs/models': 2.0.1
      debug: 4.4.1(supports-color@8.1.1)
      make-fetch-happen: 13.0.1
    transitivePeerDependencies:
      - supports-color

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tweetnacl@0.14.5: {}

  tweetnacl@1.0.3: {}

  twilio@3.29.0:
    dependencies:
      '@types/express': 4.17.23
      deprecate: 1.0.0
      jsonwebtoken: 8.5.1
      lodash: 4.17.21
      moment: 2.19.3
      q: 2.0.3
      request: 2.88.0
      rootpath: 0.1.2
      scmp: 2.0.0
      xmlbuilder: 9.0.1

  type-check@0.3.2:
    dependencies:
      prelude-ls: 1.1.2

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-detect@4.1.0: {}

  type-fest@0.18.1: {}

  type-fest@0.21.3: {}

  type-fest@0.4.1: {}

  type-fest@0.6.0: {}

  type-fest@0.8.1: {}

  type-fest@4.41.0: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typedarray-to-buffer@3.1.5:
    dependencies:
      is-typedarray: 1.0.0

  typedarray.prototype.slice@1.0.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-proto: 1.0.1
      math-intrinsics: 1.1.0
      typed-array-buffer: 1.0.3
      typed-array-byte-offset: 1.0.4

  typedarray@0.0.6: {}

  typescript@5.6.3: {}

  uglify-js@3.19.3:
    optional: true

  ultron@1.1.1: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undefsafe@2.0.5: {}

  undici-types@6.21.0: {}

  unicorn-magic@0.1.0: {}

  unique-filename@3.0.0:
    dependencies:
      unique-slug: 4.0.0

  unique-slug@4.0.0:
    dependencies:
      imurmurhash: 0.1.4

  universal-user-agent@6.0.1: {}

  universalify@0.1.2: {}

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  unzipper@0.10.14:
    dependencies:
      big-integer: 1.6.52
      binary: 0.3.0
      bluebird: 3.4.7
      buffer-indexof-polyfill: 1.0.2
      duplexer2: 0.1.4
      fstream: 1.0.12
      graceful-fs: 4.2.11
      listenercount: 1.0.1
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  upath@2.0.1: {}

  update-browserslist-db@1.1.3(browserslist@4.25.4):
    dependencies:
      browserslist: 4.25.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@3.0.2:
    dependencies:
      punycode: 2.3.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-regex@3.2.0:
    dependencies:
      ip-regex: 1.0.3

  url-value-parser@2.2.0: {}

  util-deprecate@1.0.2: {}

  utils-merge@1.0.1: {}

  uuid@10.0.0: {}

  uuid@3.3.2: {}

  uuid@3.3.3: {}

  uuid@3.4.0: {}

  uuid@8.3.2: {}

  uuid@9.0.1: {}

  v8-compile-cache-lib@3.0.1: {}

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  validate-npm-package-name@5.0.1: {}

  validator@10.11.0: {}

  validator@13.15.15: {}

  vary@1.1.2: {}

  verror@1.10.0:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  vm2@3.9.19:
    dependencies:
      acorn: 8.15.0
      acorn-walk: 8.3.4

  walk-up-path@3.0.1: {}

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  weak-map@1.0.8: {}

  webidl-conversions@3.0.1: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-module@1.0.0: {}

  which-module@2.0.1: {}

  which-pm-runs@1.1.0:
    optional: true

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  which@4.0.0:
    dependencies:
      isexe: 3.1.1

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3

  window-or-global@1.0.1: {}

  wkx@0.5.0:
    dependencies:
      '@types/node': 22.14.1

  word-wrap@1.2.5: {}

  wordwrap@1.0.0: {}

  workerpool@6.5.1: {}

  wrap-ansi@2.1.0:
    dependencies:
      string-width: 1.0.2
      strip-ansi: 3.0.1

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.3
      string-width: 5.1.2
      strip-ansi: 7.1.2

  wrappy@1.0.2: {}

  write-file-atomic@2.4.3:
    dependencies:
      graceful-fs: 4.2.11
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  write-file-atomic@3.0.3:
    dependencies:
      imurmurhash: 0.1.4
      is-typedarray: 1.0.0
      signal-exit: 3.0.7
      typedarray-to-buffer: 3.1.5

  write-file-atomic@5.0.1:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 4.1.0

  write-json-file@3.2.0:
    dependencies:
      detect-indent: 5.0.0
      graceful-fs: 4.2.11
      make-dir: 2.1.0
      pify: 4.0.1
      sort-keys: 2.0.0
      write-file-atomic: 2.4.3

  write-pkg@4.0.0:
    dependencies:
      sort-keys: 2.0.0
      type-fest: 0.4.1
      write-json-file: 3.2.0

  ws@3.3.3:
    dependencies:
      async-limiter: 1.0.1
      safe-buffer: 5.1.2
      ultron: 1.1.1

  ws@8.17.1: {}

  xhr@2.6.0:
    dependencies:
      global: 4.4.0
      is-function: 1.0.2
      parse-headers: 2.0.6
      xtend: 4.0.2

  xml-parse-from-string@1.0.1: {}

  xml2js@0.5.0:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xmlbuilder@9.0.1: {}

  xmlhttprequest-ssl@1.5.5: {}

  xregexp@2.0.0: {}

  xtend@4.0.2: {}

  y18n@3.2.2: {}

  y18n@4.0.3: {}

  y18n@5.0.8: {}

  yallist@2.1.2: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml@2.8.1: {}

  yargs-parser@11.1.1:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@20.2.9: {}

  yargs-parser@21.1.1: {}

  yargs-parser@4.2.1:
    dependencies:
      camelcase: 3.0.0

  yargs-unparser@2.0.0:
    dependencies:
      camelcase: 6.3.0
      decamelize: 4.0.0
      flat: 5.0.2
      is-plain-obj: 2.1.0

  yargs@12.0.5:
    dependencies:
      cliui: 4.1.0
      decamelize: 1.2.0
      find-up: 3.0.0
      get-caller-file: 1.0.3
      os-locale: 3.1.0
      require-directory: 2.1.1
      require-main-filename: 1.0.1
      set-blocking: 2.0.0
      string-width: 2.1.1
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 11.1.1

  yargs@15.4.1:
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yargs@6.6.0:
    dependencies:
      camelcase: 3.0.0
      cliui: 3.2.0
      decamelize: 1.2.0
      get-caller-file: 1.0.3
      os-locale: 1.4.0
      read-pkg-up: 1.0.1
      require-directory: 2.1.1
      require-main-filename: 1.0.1
      set-blocking: 2.0.0
      string-width: 1.0.2
      which-module: 1.0.0
      y18n: 3.2.2
      yargs-parser: 4.2.1

  yeast@0.1.2: {}

  yn@3.1.1: {}

  yocto-queue@0.1.0: {}

  z-schema@3.25.1:
    dependencies:
      core-js: 2.6.12
      lodash.get: 4.4.2
      lodash.isequal: 4.5.0
      validator: 10.11.0
    optionalDependencies:
      commander: 2.20.3
