--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;


--changeset vladimir.minakov:2025-07-01-SWS-49988-create-currencies-table
--comment Create currencies table
SET search_path = swxrates, public;

CREATE TABLE IF NOT EXISTS currencies (
    code CHAR(3) NOT NULL,
    name VARCHAR(100) NOT NULL,
    properties JSONB,
    copy_limits_from CHAR(3),
    origin_currency_code CHAR(3),
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
    PRIMARY KEY (code)
);

CREATE INDEX IF NOT EXISTS idx_currencies_copy_limits_from ON currencies USING btree (copy_limits_from);
CREATE INDEX IF NOT EXISTS idx_currencies_origin_currency_code ON currencies USING btree (origin_currency_code);

RESET search_path;

--rollback SET search_path = swxrates, public;
--rollback DROP TABLE IF EXISTS currencies;
--rollback RESET search_path;
