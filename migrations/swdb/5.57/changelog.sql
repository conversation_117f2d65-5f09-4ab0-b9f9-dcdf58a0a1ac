--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset vladimir.minakov:2025-01-17-SWS-51561-prod-ro-tapking-domain-block-detection-service
SET search_path = swmanagement;

ALTER TABLE static_domains
    ADD COLUMN info JSONB;

COMMENT ON COLUMN static_domains.info IS 'Domain info';

ALTER TABLE dynamic_domains
    ADD COLUMN info JSONB;

COMMENT ON COLUMN dynamic_domains.info IS 'Domain info';

ALTER TABLE static_domain_pools
    ADD COLUMN domain_detector_adapter_id VARCHAR(255);

COMMENT ON COLUMN static_domain_pools.domain_detector_adapter_id IS 'Domain detector adapter identifier for monitoring domains';

ALTER TABLE dynamic_domain_pools
    ADD COLUMN domain_detector_adapter_id VARCHAR(255);

COMMENT ON COLUMN dynamic_domain_pools.domain_detector_adapter_id IS 'Domain detector adapter identifier for monitoring domains';

ALTER TABLE static_domain_pools_static_domains
    ADD COLUMN blocked_date TIMESTAMP WITHOUT TIME ZONE;

COMMENT ON COLUMN static_domain_pools_static_domains.blocked_date IS 'Timestamp when the domain was blocked';

ALTER TABLE dynamic_domain_pools_dynamic_domains
    ADD COLUMN blocked_date TIMESTAMP WITHOUT TIME ZONE;

COMMENT ON COLUMN dynamic_domain_pools_dynamic_domains.blocked_date IS 'Timestamp when the dynamic domain was blocked';

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE static_domains DROP COLUMN info;
--rollback ALTER TABLE dynamic_domains DROP COLUMN info;
--rollback ALTER TABLE static_domain_pools DROP COLUMN domain_detector_adapter_id;
--rollback ALTER TABLE dynamic_domain_pools DROP COLUMN domain_detector_adapter_id;
--rollback ALTER TABLE static_domain_pools_static_domains DROP COLUMN blocked_date;
--rollback ALTER TABLE dynamic_domain_pools_dynamic_domains DROP COLUMN blocked_date;
--rollback RESET search_path;
