--liquibase formatted sql

--example: --changeset author:yyyy-mm-dd-SWS-XXXX-operation-description
--example: --comment create table for test
--example: SET search_path = swmanagement;
--example: CREATE TABLE tmp(id int);
--example: RESET search_path;
--example: --rollback SET search_path = swmanagement;
--example: --rollback DROP TABLE tmp;
--example: --rollback RESET search_path;

--changeset vladimir.minakov:2025-01-22-SWS-51270-create-database-migration-to-add-new-columns-and-tables-for-enhanced-domains
--comment Add description, provider, status, expiryDate, and type fields to domain models
SET search_path = swmanagement;

-- Create enum for domain status
CREATE TYPE enum_domain_status AS ENUM ('active', 'suspended');

-- Create enum for static domain type
CREATE TYPE enum_static_domain_type AS ENUM ('static', 'lobby', 'live-streaming', 'ehub');

ALTER TABLE dynamic_domains
    ADD COLUMN description VARCHAR(2048),
    ADD COLUMN provider VARCHAR(255),
    ADD COLUMN status enum_domain_status NOT NULL DEFAULT 'active',
    ADD COLUMN expiry_date TIMESTAMP WITHOUT TIME ZONE;

ALTER TABLE static_domains
    ADD COLUMN description VARCHAR(2048),
    ADD COLUMN provider VARCHAR(255),
    ADD COLUMN status enum_domain_status NOT NULL DEFAULT 'active',
    ADD COLUMN expiry_date TIMESTAMP WITHOUT TIME ZONE,
    ADD COLUMN type enum_static_domain_type NOT NULL DEFAULT 'static';

ALTER TABLE lobby_domains
    ADD COLUMN description VARCHAR(2048),
    ADD COLUMN provider VARCHAR(255),
    ADD COLUMN status enum_domain_status NOT NULL DEFAULT 'active',
    ADD COLUMN expiry_date TIMESTAMP WITHOUT TIME ZONE;

RESET search_path;

--rollback SET search_path = swmanagement;

--rollback ALTER TABLE dynamic_domains DROP COLUMN description;
--rollback ALTER TABLE dynamic_domains DROP COLUMN provider;
--rollback ALTER TABLE dynamic_domains DROP COLUMN status;
--rollback ALTER TABLE dynamic_domains DROP COLUMN expiry_date;

--rollback ALTER TABLE static_domains DROP COLUMN description;
--rollback ALTER TABLE static_domains DROP COLUMN provider;
--rollback ALTER TABLE static_domains DROP COLUMN status;
--rollback ALTER TABLE static_domains DROP COLUMN expiry_date;
--rollback ALTER TABLE static_domains DROP COLUMN type;

--rollback ALTER TABLE lobby_domains DROP COLUMN description;
--rollback ALTER TABLE lobby_domains DROP COLUMN provider;
--rollback ALTER TABLE lobby_domains DROP COLUMN status;
--rollback ALTER TABLE lobby_domains DROP COLUMN expiry_date;

--rollback DROP TYPE IF EXISTS enum_domain_status;
--rollback DROP TYPE IF EXISTS enum_static_domain_type;

--rollback RESET search_path;

--changeset vladimir.minakov:2025-01-28-SWS-51271-remove-separate-table-for-lobby-domains endDelimiter:# stripComments:false rollbackEndDelimiter:#
--comment Migrate lobby_domains data to static_domains table and drop lobby_domains table
SET search_path = swmanagement;

-- Step 1: Migrate data from lobby_domains to static_domains
-- Insert lobby domain records into static_domains with type='lobby'
INSERT INTO static_domains (
    domain,
    description,
    provider,
    status,
    expiry_date,
    type,
    created_at,
    updated_at
)
SELECT
    ld.name as domain,
    ld.description,
    ld.provider,
    ld.status,
    ld.expiry_date,
    'lobby'::enum_static_domain_type as type,
    ld.created_at,
    ld.updated_at
FROM lobby_domains ld
WHERE NOT EXISTS (
    SELECT 1 FROM static_domains sd
    WHERE sd.domain = ld.name
);

-- Step 2: Create a temporary mapping table to track old lobby_domain_id to new static_domain_id
CREATE TEMPORARY TABLE lobby_to_static_mapping AS
SELECT
    ld.id as old_lobby_domain_id,
    sd.id as new_static_domain_id,
    ld.is_active as lobby_is_active
FROM lobby_domains ld
JOIN static_domains sd ON sd.domain = ld.name AND sd.type = 'lobby';

-- Safety check: Verify all lobby domains were successfully migrated
DO $$
DECLARE
    lobby_count INTEGER;
    mapping_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO lobby_count FROM lobby_domains;
    SELECT COUNT(*) INTO mapping_count FROM lobby_to_static_mapping;

    IF lobby_count != mapping_count THEN
        RAISE EXCEPTION 'Data migration incomplete. Expected % lobby domains, but only % were mapped to static domains.',
            lobby_count, mapping_count;
    END IF;

    RAISE NOTICE 'Successfully mapped % lobby domains to static domains.', mapping_count;
END $$;

-- Step 3: Migrate junction table relationships
-- Insert into static_domain_pools_static_domains using the mapping
INSERT INTO static_domain_pools_static_domains (
    static_domain_pool_id,
    static_domain_id,
    is_active
)
SELECT DISTINCT
    spdld.static_domain_pool_id,
    ltsm.new_static_domain_id,
    -- Use the more restrictive active status (both junction table and lobby domain must be active)
    CASE
        WHEN spdld.is_active = true AND ltsm.lobby_is_active = true THEN true
        ELSE false
    END as is_active
FROM static_domain_pools_lobby_domains spdld
JOIN lobby_to_static_mapping ltsm ON ltsm.old_lobby_domain_id = spdld.lobby_domain_id
WHERE NOT EXISTS (
    SELECT 1 FROM static_domain_pools_static_domains spdsd
    WHERE spdsd.static_domain_pool_id = spdld.static_domain_pool_id
    AND spdsd.static_domain_id = ltsm.new_static_domain_id
);

-- Safety check: Verify junction table migration
DO $$
DECLARE
    old_junction_count INTEGER;
    new_junction_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO old_junction_count FROM static_domain_pools_lobby_domains;

    SELECT COUNT(*) INTO new_junction_count
    FROM static_domain_pools_static_domains spdsd
    JOIN static_domains sd ON sd.id = spdsd.static_domain_id
    WHERE sd.type = 'lobby';

    RAISE NOTICE 'Migrated % lobby domain pool relationships to static domain pools.', new_junction_count;

    IF old_junction_count > 0 AND new_junction_count = 0 THEN
        RAISE WARNING 'No junction relationships were migrated, but % existed in the old table.', old_junction_count;
    END IF;
END $$;

-- Step 4: Final safety check before dropping tables
DO $$
DECLARE
    lobby_domains_in_static INTEGER;
    total_lobby_domains INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_lobby_domains FROM lobby_domains;
    SELECT COUNT(*) INTO lobby_domains_in_static FROM static_domains WHERE type = 'lobby';

    IF total_lobby_domains != lobby_domains_in_static THEN
        RAISE EXCEPTION 'Migration verification failed. Found % lobby domains but only % in static_domains with type=lobby. Aborting table drop.',
            total_lobby_domains, lobby_domains_in_static;
    END IF;

    RAISE NOTICE 'Migration verification successful. Proceeding to drop lobby_domains table.';
END $$;

-- Step 5: Drop the junction table first (due to foreign key constraints)
DROP TABLE static_domain_pools_lobby_domains;

-- Step 6: Drop the lobby_domains table
DROP TABLE lobby_domains;

RESET search_path;

--rollback SET search_path = swmanagement;

--rollback -- Recreate lobby_domains table
--rollback CREATE TABLE lobby_domains (
--rollback     id SERIAL PRIMARY KEY,
--rollback     name VARCHAR(255) NOT NULL UNIQUE,
--rollback     description VARCHAR(2048),
--rollback     provider VARCHAR(255),
--rollback     status enum_domain_status NOT NULL DEFAULT 'active',
--rollback     expiry_date TIMESTAMP WITHOUT TIME ZONE,
--rollback     is_active BOOLEAN NOT NULL DEFAULT TRUE,
--rollback     created_at timestamp without time zone DEFAULT NOW(),
--rollback     updated_at timestamp without time zone DEFAULT NOW()
--rollback );
--rollback COMMENT ON TABLE lobby_domains IS 'Lobby domain entities';
--rollback COMMENT ON COLUMN lobby_domains.name IS 'Domain name';
--rollback COMMENT ON COLUMN lobby_domains.is_active IS 'Flag that indicates whether the lobby domain is active and can be used';

--rollback -- Recreate static_domain_pools_lobby_domains junction table
--rollback CREATE TABLE static_domain_pools_lobby_domains (
--rollback     static_domain_pool_id INT NOT NULL REFERENCES static_domain_pools(id) ON DELETE CASCADE,
--rollback     lobby_domain_id INT NOT NULL REFERENCES lobby_domains(id) ON DELETE CASCADE,
--rollback     is_active BOOLEAN NOT NULL DEFAULT TRUE,
--rollback     PRIMARY KEY (static_domain_pool_id, lobby_domain_id)
--rollback );

--rollback CREATE INDEX idx_static_domain_pools_lobby_domains_lobby_domain_id ON static_domain_pools_lobby_domains USING btree (lobby_domain_id);
--rollback CREATE INDEX idx_static_domain_pools_lobby_domains_is_active ON static_domain_pools_lobby_domains USING btree (is_active);

--rollback COMMENT ON TABLE static_domain_pools_lobby_domains IS 'Connection table between static domain pools and lobby domains';
--rollback COMMENT ON COLUMN static_domain_pools_lobby_domains.is_active IS 'Flag that indicates whether the lobby domain is active and can be used';

--rollback -- Restore lobby_domains data from static_domains where type='lobby'
--rollback INSERT INTO lobby_domains (
--rollback     name,
--rollback     description,
--rollback     provider,
--rollback     status,
--rollback     expiry_date,
--rollback     is_active,
--rollback     created_at,
--rollback     updated_at
--rollback )
--rollback SELECT
--rollback     sd.domain as name,
--rollback     sd.description,
--rollback     sd.provider,
--rollback     sd.status,
--rollback     sd.expiry_date,
--rollback     true as is_active,
--rollback     sd.created_at,
--rollback     sd.updated_at
--rollback FROM static_domains sd
--rollback WHERE sd.type = 'lobby';

--rollback -- Restore junction table relationships
--rollback INSERT INTO static_domain_pools_lobby_domains (
--rollback     static_domain_pool_id,
--rollback     lobby_domain_id,
--rollback     is_active
--rollback )
--rollback SELECT
--rollback     spdsd.static_domain_pool_id,
--rollback     ld.id as lobby_domain_id,
--rollback     spdsd.is_active
--rollback FROM static_domain_pools_static_domains spdsd
--rollback JOIN static_domains sd ON sd.id = spdsd.static_domain_id AND sd.type = 'lobby'
--rollback JOIN lobby_domains ld ON ld.name = sd.domain;

--rollback -- Remove migrated lobby domains from static_domains
--rollback DELETE FROM static_domain_pools_static_domains
--rollback WHERE static_domain_id IN (
--rollback     SELECT id FROM static_domains WHERE type = 'lobby'
--rollback );

--rollback DELETE FROM static_domains WHERE type = 'lobby';

--rollback RESET search_path;

--changeset vladimir.minakov:2025-01-28-SWS-51271-restore-separate-table-for-lobby-domains
--comment Restore previously deleted lobby_domains and static_domain_pools_lobby_domains tables to support zero maintenance operations
SET search_path = swmanagement;

-- Recreate lobby_domains table with its original schema including enhanced columns
CREATE TABLE lobby_domains (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description VARCHAR(2048),
    provider VARCHAR(255),
    status enum_domain_status NOT NULL DEFAULT 'active',
    expiry_date TIMESTAMP WITHOUT TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at timestamp without time zone DEFAULT NOW(),
    updated_at timestamp without time zone DEFAULT NOW()
);

-- Recreate static_domain_pools_lobby_domains junction table
CREATE TABLE static_domain_pools_lobby_domains (
    static_domain_pool_id INT NOT NULL REFERENCES static_domain_pools(id) ON DELETE CASCADE,
    lobby_domain_id INT NOT NULL REFERENCES lobby_domains(id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    PRIMARY KEY (static_domain_pool_id, lobby_domain_id)
);

-- Create indexes for performance
CREATE INDEX idx_static_domain_pools_lobby_domains_lobby_domain_id ON static_domain_pools_lobby_domains USING btree (lobby_domain_id);
CREATE INDEX idx_static_domain_pools_lobby_domains_is_active ON static_domain_pools_lobby_domains USING btree (is_active);

-- Restore data from static_domains where type='lobby' back to lobby_domains
INSERT INTO lobby_domains (
    name,
    description,
    provider,
    status,
    expiry_date,
    is_active,
    created_at,
    updated_at
)
SELECT
    sd.domain as name,
    sd.description,
    sd.provider,
    sd.status,
    sd.expiry_date,
    true as is_active, -- Default to active since we can't determine original is_active from static_domains
    sd.created_at,
    sd.updated_at
FROM static_domains sd
WHERE sd.type = 'lobby';

-- Restore junction table relationships from static_domain_pools_static_domains
-- where the static_domain has type='lobby'
INSERT INTO static_domain_pools_lobby_domains (
    static_domain_pool_id,
    lobby_domain_id,
    is_active
)
SELECT
    spdsd.static_domain_pool_id,
    ld.id as lobby_domain_id,
    spdsd.is_active
FROM static_domain_pools_static_domains spdsd
JOIN static_domains sd ON sd.id = spdsd.static_domain_id AND sd.type = 'lobby'
JOIN lobby_domains ld ON ld.name = sd.domain;

RESET search_path;

--rollback -- Rollback for restoration changeset
--rollback SET search_path = swmanagement;
--rollback DROP TABLE IF EXISTS static_domain_pools_lobby_domains;
--rollback DROP TABLE IF EXISTS lobby_domains;
--rollback RESET search_path;

--changeset vladimir.minakov:2025-01-29-SWS-51434-extend-entity-to-support-all-static-domain-types
--comment Add domain_id fields for all static domain types (lobby, live-streaming, ehub) to entities table for fallback when staticDomainPool is not configured
SET search_path = swmanagement;

-- Add domain_id fields for all static domain types
ALTER TABLE entities ADD COLUMN lobby_domain_id INTEGER;
ALTER TABLE entities ADD COLUMN live_streaming_domain_id INTEGER;
ALTER TABLE entities ADD COLUMN ehub_domain_id INTEGER;

-- Add comments for the new domain ID columns
COMMENT ON COLUMN entities.lobby_domain_id IS 'Reference to static domain with type=lobby used as fallback when staticDomainPool is not configured';
COMMENT ON COLUMN entities.live_streaming_domain_id IS 'Reference to static domain with type=live-streaming used as fallback when staticDomainPool is not configured';
COMMENT ON COLUMN entities.ehub_domain_id IS 'Reference to static domain with type=ehub used as fallback when staticDomainPool is not configured';

-- Add foreign key constraints to static_domains table
ALTER TABLE entities ADD CONSTRAINT entities_lobby_domain_id_fkey
    FOREIGN KEY (lobby_domain_id) REFERENCES static_domains(id) ON DELETE SET NULL;
ALTER TABLE entities ADD CONSTRAINT entities_live_streaming_domain_id_fkey
    FOREIGN KEY (live_streaming_domain_id) REFERENCES static_domains(id) ON DELETE SET NULL;
ALTER TABLE entities ADD CONSTRAINT entities_ehub_domain_id_fkey
    FOREIGN KEY (ehub_domain_id) REFERENCES static_domains(id) ON DELETE SET NULL;

RESET search_path;

--rollback SET search_path = swmanagement;
--rollback ALTER TABLE entities DROP CONSTRAINT IF EXISTS entities_lobby_domain_id_fkey;
--rollback ALTER TABLE entities DROP CONSTRAINT IF EXISTS entities_live_streaming_domain_id_fkey;
--rollback ALTER TABLE entities DROP CONSTRAINT IF EXISTS entities_ehub_domain_id_fkey;
--rollback ALTER TABLE entities DROP COLUMN lobby_domain_id;
--rollback ALTER TABLE entities DROP COLUMN live_streaming_domain_id;
--rollback ALTER TABLE entities DROP COLUMN ehub_domain_id;
--rollback RESET search_path;
